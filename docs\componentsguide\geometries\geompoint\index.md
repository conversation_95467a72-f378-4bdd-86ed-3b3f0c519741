<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 11:48:46
-->

# bc-geom-point

`bc-geom-point` 可以在 `bc-feature` 内部使用来绘制单个点

<script lang="ts" setup>
import GeomPoint from "@demos/GeomPoint.vue"
</script>

<ClientOnly>
<GeomPoint />
</ClientOnly>

## 用法

::: code-group

<<< ../../../../src/demos/GeomPoint.vue

:::

## 属性

### coordinates

- **类型**: `number[]`
  该点的坐标（以地图投影为单位）。

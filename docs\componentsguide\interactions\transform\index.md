<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: <PERSON><PERSON>er<PERSON>
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 13:54:02
-->

# bc-interaction-transform

> 变换特征几何图形的交互。

<script lang="ts" setup>
import TransformDemo from "@demos/TransformDemo.vue"
</script>

<ClientOnly>
<TransformDemo/>
</ClientOnly>

## 用法

::: code-group

<<< ../../../../src/demos/TransformDemo.vue

:::

## 属性

### enableRotatedTransform

- **类型**: `Function`

### condition

- **类型**: `Function`

### addCondition

- **类型**: `Function`

### filter

- **类型**: `Function`

### layers

- **类型**: `Array`

### hitTolerance

- **类型**: `Number`
- **默认值**: `2`

### translateFeature

- **类型**: `Boolean`
- **默认值**: `true`

### scale

- **类型**: `Boolean`
- **默认值**: `true`

### rotate

- **类型**: `Boolean`
- **默认值**: `true`

### keepAspectRatio

- **类型**: `Boolean`
- **默认值**: `false`

### translate

- **类型**: `Boolean`
- **默认值**: `true`

### stretch

- **类型**: `Boolean`
- **默认值**: `true`

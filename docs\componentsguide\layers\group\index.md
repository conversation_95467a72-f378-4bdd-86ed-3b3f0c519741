<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISer<PERSON>
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 14:57:38
-->

# bc-layer-group

> 可以一起处理的图层的集合。

<script lang="ts" setup>
import TileJSONDemo from "@demos/TileJSONDemo.vue"
</script>
<ClientOnly>
<TileJSONDemo />
</ClientOnly>

## 用法

下面的示例展示了如何在多个图层上应用常见的样式/行为。

::: code-group

<<< ../../../../src/demos/TileJSONDemo.vue

:::

## 属性

### Props 继承自 OpenLayers

属性直接从 OpenLayers 传递。它们的类型和默认值可以在[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_layer_Group-LayerGroup.html)中查看。只有部分属性是由 Vue / HTML 的保留关键字引起的。下面的部分描述了这种偏离的 props。

### 偏离属性

无

## 事件

您可以从底层源访问所有事件。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_layer_Group-LayerGroup.html)以查看将触发的可用事件。

```html
<bc-layer-group :opacity="0.2" @error="handleEvent">
  <!-- ... -->
</bc-layer-group>
```

## 方法

您可以从底层源访问所有方法。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_layer_Group-LayerGroup.html)以查看可用的方法。

要访问源代码，您可以使用 `ref()` 如下所示：

```vue
<template>
  <!-- ... -->
  <bc-layer-group :opacity="0.2" ref="layerGroupRef">
    <!-- ... -->
  </bc-layer-group>
  <!-- ... -->
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import type LayerGroup from "ol/layer/LayerGroup";

const layerGroupRef = ref<{ layerGroup: LayerGroup }>(null);

onMounted(() => {
  const layerGroup: LayerGroup = sourceRef.layerGroup;
  // call your method on `layerGroup`
});
</script>
```

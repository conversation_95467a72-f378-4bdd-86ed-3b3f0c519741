<template>
  <bc-map
    :loadTilesWhileAnimating="true"
    :loadTilesWhileInteracting="true"
    style="height: 400px"
  >
    <bc-view ref="view" :center="center" :rotation="rotation" :zoom="zoom" />

    <bc-webgl-tile-layer>
      <bc-source-osm />
    </bc-webgl-tile-layer>
  </bc-map>
</template>

<script lang="ts" setup>
import { ref } from "vue";

const center = ref([-90, 50]);
const zoom = ref(2);
const rotation = ref(0);
</script>

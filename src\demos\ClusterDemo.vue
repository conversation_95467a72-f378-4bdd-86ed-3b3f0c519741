<template>
  <bc-map
    ref="map"
    :loadTilesWhileAnimating="true"
    :loadTilesWhileInteracting="true"
    style="height: 800px"
  >
    <bc-view
      ref="view"
      :center="center"
      :rotation="rotation"
      :zoom="zoom"
      :projection="projection"
    />

    <bc-tile-layer>
      <bc-source-osm />
    </bc-tile-layer>

    <bc-vector-layer>
      <bc-source-cluster :distance="40">
        <bc-source-vector>
          <bc-feature v-for="index in 300" :key="index">
            <bc-geom-point
              :coordinates="[
                getRandomInRange(24, 45, 3),
                getRandomInRange(35, 41, 3),
              ]"
            ></bc-geom-point>
          </bc-feature>
        </bc-source-vector>
      </bc-source-cluster>

      <bc-style :overrideStyleFunction="overrideStyleFunction">
        <bc-style-stroke color="red" :width="2"></bc-style-stroke>
        <bc-style-fill color="rgba(255,255,255,0.1)"></bc-style-fill>

        <bc-style-circle :radius="10">
          <bc-style-fill color="#3399CC"></bc-style-fill>
          <bc-style-stroke color="#fff" :width="1"></bc-style-stroke>
        </bc-style-circle>
        <bc-style-text>
          <bc-style-fill color="#fff"></bc-style-fill>
        </bc-style-text>
      </bc-style>
    </bc-vector-layer>
  </bc-map>
</template>

<script lang="ts" setup>
import { ref } from "vue";

const center = ref([34, 39.13]);
const projection = ref("EPSG:4326");
const zoom = ref(6.8);
const rotation = ref(0);

const overrideStyleFunction = (feature, style, resolution) => {
  console.log({ feature, style, resolution });
  const clusteredFeatures = feature.get("features");
  const size = clusteredFeatures.length;
  style.getText().setText(size.toString());
};

const getRandomInRange = (from, to, fixed) => {
  return (Math.random() * (to - from) + from).toFixed(fixed) * 1;
};
</script>

<style>
.overlay-content {
  background: red !important;
  color: white;
  box-shadow: 0 5px 10px rgb(2 2 2 / 20%);
  padding: 10px 20px;
  font-size: 16px;
}
</style>

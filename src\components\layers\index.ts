import type { App } from "vue";
import BcAnimatedClusterLayer from "./BcAnimatedClusterLayer.vue";
import BcHeatmapLayer from "./BcHeatmapLayer.vue";
import BcImageLayer from "./BcImageLayer.vue";
import BcLayerGroup from "./BcLayerGroup.vue";
import BcTileLayer from "./BcTileLayer.vue";
import BcVectorLayer from "./BcVectorLayer.vue";
import BcVectorTileLayer from "./BcVectorTileLayer.vue";
import BcVectorImageLayer from "./BcVectorImageLayer.vue";
import BcWebglPointsLayer from "./BcWebglPointsLayer.vue";
import BcWebglTileLayer from "./BcWebglTileLayer.vue";

let installed = false;

function install(app: App) {
  if (installed) return;
  installed = true;

  app.component("bc-animated-clusterlayer", BcAnimatedClusterLayer);
  app.component("bc-heatmap-layer", BcHeatmapLayer);
  app.component("bc-image-layer", BcImageLayer);
  app.component("bc-layer-group", BcLayerGroup);
  app.component("bc-tile-layer", BcTileLayer);
  app.component("bc-vector-image-layer", BcVectorImageLayer);
  app.component("bc-vector-layer", BcVectorLayer);
  app.component("bc-vector-tile-layer", BcVectorTileLayer);
  app.component("bc-webgl-points-layer", BcWebglPointsLayer);
  app.component("bc-webgl-tile-layer", BcWebglTileLayer);
}

export default install;

export {
  install,
  BcAnimatedClusterLayer,
  BcHeatmapLayer,
  BcImageLayer,
  BcLayerGroup,
  BcTileLayer,
  BcVectorImageLayer,
  BcVectorLayer,
  BcWebglPointsLayer,
  BcWebglTileLayer,
};

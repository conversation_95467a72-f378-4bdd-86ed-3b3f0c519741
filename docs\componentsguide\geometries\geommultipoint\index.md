<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 11:51:45
-->

# bc-geom-multi-point

`bc-geom--multi-point` 可以在 `bc-feature` 内部使用来一次绘制多个点。

<script lang="ts" setup>
import MultiPoint from "@demos/MultiPoint.vue"
</script>
<ClientOnly>
<MultiPoint />
</ClientOnly>

## 用法

::: code-group

<<< ../../../../src/demos/MultiPoint.vue

:::

## 属性

### coordinates

- **类型**: `number[][]`
  以地图投影为单位的点数组。

<template>
  <label for="count">Marker:</label>
  <input type="number" id="count" v-model="count" />
  <bc-map
    :loadTilesWhileAnimating="true"
    :loadTilesWhileInteracting="true"
    renderer="webgl"
    style="height: 400px"
  >
    <bc-view
      ref="view"
      :center="center"
      :rotation="rotation"
      :zoom="zoom"
      :projection="projection"
    />

    <bc-tile-layer>
      <bc-source-osm />
    </bc-tile-layer>

    <bc-interaction-clusterselect @select="featureSelected" :pointRadius="20">
      <bc-style>
        <bc-style-stroke color="green" :width="5"></bc-style-stroke>
        <bc-style-fill color="rgba(255,255,255,0.5)"></bc-style-fill>
        <bc-style-icon :src="markerIcon" :scale="0.05"></bc-style-icon>
      </bc-style>
    </bc-interaction-clusterselect>

    <bc-animated-clusterlayer :animationDuration="500" :distance="40">
      <bc-source-vector
        ref="vectorsource"
        :features="features"
        @featuresloadstart="featuresloadstart"
        @featuresloadend="featuresloadend"
        @featuresloaderror="featuresloaderror"
      />

      <bc-style :overrideStyleFunction="overrideStyleFunction">
        <bc-style-stroke color="red" :width="2"></bc-style-stroke>
        <bc-style-fill color="rgba(255,255,255,0.1)"></bc-style-fill>

        <bc-style-circle :radius="20">
          <bc-style-stroke
            color="black"
            :width="15"
            :lineDash="[]"
            lineCap="butt"
          ></bc-style-stroke>
          <bc-style-fill color="black"></bc-style-fill>
        </bc-style-circle>

        <bc-style-text>
          <bc-style-fill color="white"></bc-style-fill>
        </bc-style-text>
      </bc-style>
    </bc-animated-clusterlayer>
  </bc-map>
</template>

<script lang="ts" setup>
import { computed, ref } from "vue";
import { Point } from "ol/geom";
import Feature from "ol/Feature";
import markerIcon from "@/assets/marker.png";

const center = ref([40, 40]);
const projection = ref("EPSG:4326");
const zoom = ref(5);
const rotation = ref(0);
const count = ref(5000);

const features = computed(() => {
  return Array.from({ length: count.value }, (_, i) => {
    return new Feature({
      geometry: new Point([
        getRandomInRange(24, 45, 3),
        getRandomInRange(35, 41, 3),
      ]),
      index: i,
    });
  });
});

const overrideStyleFunction = (feature, style) => {
  const clusteredFeatures = feature.get("features");
  const size = clusteredFeatures.length;

  const color = size > 20 ? "192,0,0" : size > 8 ? "255,128,0" : "0,128,0";
  const radius = Math.max(8, Math.min(size, 20));
  const dash = (2 * Math.PI * radius) / 6;
  const calculatedDash = [0, dash, dash, dash, dash, dash, dash];

  style.getImage().getStroke().setLineDash(dash);
  style
    .getImage()
    .getStroke()
    .setColor("rgba(" + color + ",0.5)");
  style.getImage().getStroke().setLineDash(calculatedDash);
  style
    .getImage()
    .getFill()
    .setColor("rgba(" + color + ",1)");

  style.getImage().setRadius(radius);

  style.getText().setText(size.toString());
};

const getRandomInRange = (from, to, fixed) => {
  return (Math.random() * (to - from) + from).toFixed(fixed) * 1;
};

const featureSelected = (event) => {
  console.log(event);
};

function featuresloadstart() {
  console.log("features load start");
}
function featuresloaderror() {
  console.log("features load error");
}
function featuresloadend() {
  console.log("features load end");
}
</script>

<style scoped>
input {
  margin: 0.5rem;
  padding: 0.25rem 0.5rem;
  font-size: 1rem;
  border: 1px solid black;
  width: 100px;
}
</style>

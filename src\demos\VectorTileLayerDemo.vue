<template>
  <bc-map
    :loadTilesWhileAnimating="true"
    :loadTilesWhileInteracting="true"
    style="height: 400px"
  >
    <bc-view ref="view" :center="center" :rotation="rotation" :zoom="zoom" />

    <bc-vector-tile-layer>
      <bc-source-vector-tile :url="url" :format="mvtFormat">
      </bc-source-vector-tile>
    </bc-vector-tile-layer>
  </bc-map>
</template>

<script lang="ts" setup>
import { ref, inject } from "vue";

const center = ref([0, 0]);
const zoom = ref(3);
const rotation = ref(0);

const url = ref(
  "https://basemaps.arcgis.com/arcgis/rest/services/World_Basemap_v2/VectorTileServer/tile/{z}/{y}/{x}.pbf"
);
const format = inject("ol-format");
const mvtFormat = new format.MVT();
</script>

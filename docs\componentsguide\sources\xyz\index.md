<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 16:26:55
-->

# bc-source-xyz

切片数据的图层源，其 URL 采用在 URL 模板中定义的设定 `XYZ` 格式。

`bc-source-xyz` 允许您使用从 OpenStreetMaps 到 google 地图的任何平铺源。

<script lang="ts" setup>
import XYZSourceDemo from "@demos/XYZSourceDemo.vue"
</script>

<ClientOnly>
<XYZSourceDemo />
</ClientOnly>

## 用法

`bc-source-xyz` 加载 OSM 图块的示例（请注意，如果您需要 OSM 层，最好使用 `bc-source-osm`，这仅用于演示目的）。

加载谷歌地图卫星图块的示例。请注意，这仅允许在互联网公开可用的地方（而不是例如在密码后面）。

::: code-group

<<< ../../../../src/demos/XYZSourceDemo.vue

:::

## 属性

### Props 继承自 OpenLayers

属性直接从 OpenLayers 传递。它们的类型和默认值可以在[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_XYZ-XYZ.html)中查看。只有部分属性是由 Vue / HTML 的保留关键字引起的。下面的部分描述了这种偏离的 props。

### 偏离属性

The following additional properties are available for setting specific `params`.

## 事件

您可以从底层源访问所有事件。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_XYZ-XYZ.html)以查看将触发的可用事件。

```html
<bc-source-xyz :url="url" @error="handleEvent" />
```

## 方法

您可以从底层源访问所有方法。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_XYZ-XYZ.html)以查看可用的方法。

要访问源代码，您可以使用`ref()`如下所示：

```vue
<template>
  <!-- ... -->
  <bc-source-xyz :url="url" ref="sourceRef" />
  <!-- ... -->
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import type XYZ from "ol/source/XYZ";

const sourceRef = ref<{ source: XYZ }>(null);

onMounted(() => {
  const source: XYZ = sourceRef.value?.source;
  // call your method on `source`
});
</script>
```

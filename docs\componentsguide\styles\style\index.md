<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 13:57:24
-->

# bc-style

> 样式集合的容器

在 `bc-feature`、`bc-vector-layer`、`bc-interaction-select`、`bc-interaction-draw`、`bc-interaction-modify` 中使用它可以为矢量特征提供自定义样式。

## 用法

<script lang="ts" setup>
import StyleDemo from "@demos/StyleDemo.vue"
import StyleDemo2 from "@demos/StyleDemo2.vue"
</script>

### 设计一个要素的样式。

<ClientOnly>
<StyleDemo/>
</ClientOnly>

::: code-group

<<< ../../../../src/demos/StyleDemo.vue

:::

### 设计整个图层的样式。

<ClientOnly>
<StyleDemo2/>
</ClientOnly>

::: code-group

<<< ../../../../src/demos/StyleDemo2.vue

:::

## 属性

### zIndex

- **类型**: `Number`

### `overrideStyleFunction`

- **类型**: `OverrideStyleFunction`

更改样式，例如在集群中，您可以更改文本样式中的特征计数（请参阅集群文档）该函数具有三个参数：

1. `feature: FeatureLike`: 与样式相关的功能。
2. `currentStyle: Style`: 当前应用的样式（您可以在此处覆盖它）
3. `resolution?: number`: 代表视图分辨率的数字

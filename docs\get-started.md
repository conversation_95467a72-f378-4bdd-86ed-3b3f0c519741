<!-- auto-generated-peer-dependency-requirements START -->

- **[ol](https://github.com/openlayers/openlayers)**: `^7.4.0`
- **[ol-contextmenu](https://github.com/jonataswalker/ol-contextmenu)**: `^5.2.1`
- **[ol-ext](https://github.com/Viglino/ol-ext)**: `^4.0.8`
- **[vue](https://github.com/vuejs/core)**: `^3.0.0`

<!-- auto-generated-peer-dependency-requirements END -->

## 安装

```bash
npm install ol ol-ext ol-contextmenu  # install the peerDependencies
npm install bcgis-ol           # install this library
```

## 使用

要在应用程序中使用`bcgis-ol`，您可以导入所有组件或仅导入您真正需要的组件。

```ts
import { createApp } from "vue";
import App from "./App.vue";

import OpenLayersMap from "bcgis-ol";
import "bcgis-ol/styles.css"; // bcgis-ol version < 1.0.0-*

const app = createApp(App);
app.use(OpenLayersMap /* options */);

app.mount("#app");
```

## 调试模式

您可以激活`debug`模式，以记录从 OpenLayers 接收的事件以及在控制台上传递到 OpenLayers 的 props。

```ts
import OpenLayersMap, { type Vue3OpenlayersGlobalOptions } from "bcgis-ol";
// ...

const options: Vue3OpenlayersGlobalOptions = {
  debug: true,
};
app.use(OpenLayersMap, options);
```

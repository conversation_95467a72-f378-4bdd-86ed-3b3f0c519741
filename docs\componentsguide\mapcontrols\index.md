<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 14:15:14
-->

---

<!-- ## aside: false -->

# 地图控制

控件是一个可见的小部件，其 DOM 元素位于屏幕上的固定位置。它们可以涉及用户输入（按钮），或者仅提供信息；位置是使用 CSS 确定的。默认情况下，它们放置在 CSS 类名为 `bc-overlaycontainer-stopevent` 的容器中，但可以使用任何外部 DOM 元素。

<script lang="ts" setup>
import MapControlDemo from "@demos/MapControlDemo.vue"
</script>
<ClientOnly>
<MapControlDemo />
</ClientOnly>

## 控制清单

请参阅左侧的菜单项。

## 用法

::: code-group

<<< ../../../src/demos/MapControlDemo.vue

:::

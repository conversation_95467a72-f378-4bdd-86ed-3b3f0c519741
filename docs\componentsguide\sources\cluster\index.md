<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 15:43:05
-->
<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 15:40:46
-->

# bc-source-cluster

> 对矢量数据进行聚类的图层源。

开箱即用，具有点几何形状。对于其他几何类型，或者如果并非所有几何都应考虑进行聚类，则`geometryFunction`可以定义自定义。

<script lang="ts" setup>
import ClusterDemo from "@demos/ClusterDemo.vue"
</script>

<ClientOnly>
<ClusterDemo />
</ClientOnly>

## 用法

此示例展示了如何对 1000 个点要素进行聚类。

::: code-group

<<< ../../../../src/demos/ClusterDemo.vue

:::

## 属性

### Props 继承自 OpenLayers

属性直接从 OpenLayers 传递。它们的类型和默认值可以在[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_Cluster-Cluster.html)中查看。只有部分属性是由 Vue / HTML 的保留关键字引起的。下面的部分描述了这种偏离的 props。

### 偏离属性

无.

## 事件

您可以从底层源访问所有事件。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_Cluster-Cluster.html)以查看将触发的可用事件。

```html
<bc-source-cluster @error="handleEvent" />
```

## 方法

您可以从底层源访问所有方法。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_Cluster-Cluster.html)以查看可用的方法。

要访问源代码，您可以使用`ref()`如下所示：

```vue
<template>
  <!-- ... -->
  <bc-source-cluster :distance="40" ref="sourceRef" />
  <!-- ... -->
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import type { Cluster } from "ol/source";

const sourceRef = ref<{ source: Cluster }>(null);

onMounted(() => {
  const source: Cluster = sourceRef.value?.source;
  // call your method on `source`
});
</script>
```

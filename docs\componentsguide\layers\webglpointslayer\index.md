<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: G<PERSON>er<PERSON>
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 15:24:01
-->
<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 15:22:52
-->

# bc-webgl-points-layer

> 使用 WebGL 渲染点的图层

请注意，您不能`bc-style`在此处使用相关样式组件作为子组件。有关更多信息，请同时查看[`bc-source-webglpoints` 文档](../../sources/webglpoints/)。

<script lang="ts" setup>
import WebglPointsLayerDemo from "@demos/WebglPointsLayerDemo.vue"
</script>
<ClientOnly>
<WebglPointsLayerDemo />
</ClientOnly>

## 用法

::: code-group

<<< ../../../../src/demos/WebglPointsLayerDemo.vue

:::

## 属性

### disableHitDetection

- **类型**: `boolean`
- **默认值**: `false`

### style

- **类型**: `object`
- **默认值**: `() => ({
    symbol: {
        symbolType: 'circle',
        size: 8,
        color: '#33AAFF',
        opacity: 0.9
    }
})`

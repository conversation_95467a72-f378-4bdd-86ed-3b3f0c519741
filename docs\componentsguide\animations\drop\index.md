<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 11:18:13
-->

# bc-animation-drop

> 要素掉落动画

<script lang="ts" setup>
import DropAnimation from "@demos/DropAnimation.vue"
</script>

<ClientOnly>
<DropAnimation />
</ClientOnly>

::: code-group

<<< ../../../../src/demos/DropAnimation.vue

:::

## 属性

### duration

- **类型**: `Number`
- **默认值**: `1000`

动画持续时间（以毫秒为单位），默认 1000

### revers

- **类型**: `Boolean`
- **默认值**: `false`

动画食品方向

### repeat

- **类型**: `Number`
- **默认值**: `0`

动画重复次数，默认 0

### hiddenStyle

- **类型**: `ol.style.Style`

动画播放时显示功能的样式，用于使播放动画时选择该功能，默认该功能将在播放时隐藏（且不可选择）

### fade

- **类型**: `function`
- **默认值**: `none`

用于淡入功能的缓动函数，默认无

### easing

- **类型**: `function`
- **默认值**: `0`

动画的缓动函数，默认 `ol.easing.linear`

### speed

- **类型**: `Number`

功能的速度，如果为 0，则将使用持续时间参数，默认 0

### side

- **类型**: `Number`
- **默认值**: `0`

顶部或底部，默认顶部

# bc-style-icon

> 设置点图标的样式

在 `bc-style` 中使用它来设置点的样式

<script lang="ts" setup>
import IconDemo from "@demos/IconDemo.vue"
</script>

<ClientOnly>
<IconDemo />
</ClientOnly>

## 用法

::: code-group

<<< ../../../../src/demos/IconDemo.vue

:::

## 属性

### anchor

- **类型**: `Array`

### anchorOrigin

- **类型**: `String`
- **默认值**: `top-left`

### anchorXUnits

- **类型**: `String`
- **默认值**: `fraction`

### anchorYUnits

- **类型**: `String`
- **默认值**: `fraction`

### color

- **类型**: `String`

### crossOrigin

- **类型**: `String`

### img

- **类型**: `[HTMLImageElement, HTMLCanvasElement]`

### offset

- **类型**: `Array`
- **默认值**: `() => [0, 0]`

### displacement

- **类型**: `Array`
- **默认值**: `() => [0, 0]`

### offsetOrigin

- **类型**: `String`
- **默认值**: `top-left`

### opacity

- **类型**: `Number`
- **默认值**: `1`

### scale

- **类型**: `Number`
- **默认值**: `1`

### rotateWithView

- **类型**: `Boolean`
- **默认值**: `false`

### rotation

- **类型**: `Number`
- **默认值**: `0`

### size

- **类型**: `Array`

  ### imgSize

- **类型**: `Array`

### src

- **类型**: `String`

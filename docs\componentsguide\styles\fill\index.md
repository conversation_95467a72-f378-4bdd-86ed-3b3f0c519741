<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 13:59:28
-->

# bc-style-fill

> 为矢量图层中的多边形或多多边形的填充着色

在 `bc-style`、`bc-style-circle`、`bc-style-text` 和 `bc-style-lines` 中使用它来设置多边形和其他具有圆形区域的形状的样式。

<script lang="ts" setup>
import MultiPoint from "@demos/MultiPoint.vue"
</script>
<ClientOnly>
<MultiPoint />
</ClientOnly>

## 用法

设计要素的样式

::: code-group

<<< ../../../../src/demos/MultiPoint.vue

:::

## 属性

### color

- **类型**: `Number`

颜色为十六进制或 RGB 数组，红色、绿色和蓝色值介于 0 和 255 之间，alpha 介于 0 和 1 之间（含）。

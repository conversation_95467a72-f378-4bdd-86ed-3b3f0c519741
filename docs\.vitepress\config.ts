// https://vitepress.vuejs.org/reference/site-config
export default {
  title: "bcgis-ol",
  base: "/sdk-ol-doc/",
  lang: "zh-CN",
  description: "OpenLayers Wrapper for Vue3",
  lastUpdated: true,
  themeConfig: {
    nav: [
      { text: "首页", link: "/" },
      { text: "开始", link: "/get-started" },
      { text: "示例", link: "/demo" },
      // { text: "在线运行", link: "/playground" },
    ],
    sidebar: [
      {
        text: "开始",
        link: "/get-started",
      },
      {
        text: "示例",
        link: "/demo",
      },
      {
        text: "基础",
        items: [
          {
            text: "bc-map",
            link: "/componentsguide/map/",
          },
          {
            text: "bc-view",
            link: "/componentsguide/view/",
          },
          {
            text: "bc-overlay",
            link: "/componentsguide/overlay/",
          },
          {
            text: "bc-geolocation",
            link: "/componentsguide/geolocation/",
          },
          {
            text: "bc-projection-register",
            link: "/componentsguide/projection/",
          },
        ],
      },
      {
        text: "图层",
        collapsed: true,
        items: [
          {
            text: "bc-layer-group",
            link: "/componentsguide/layers/group/",
          },
          {
            text: "bc-animated-clusterlayer",
            link: "/componentsguide/layers/animatedclusterlayer/",
          },
          {
            text: "bc-heatmap-layer",
            link: "/componentsguide/layers/heatmaplayer/",
          },
          {
            text: "bc-image-layer",
            link: "/componentsguide/layers/imagelayer/",
          },
          {
            text: "bc-tile-layer",
            link: "/componentsguide/layers/tilelayer/",
          },
          {
            text: "bc-vector-image-layer",
            link: "/componentsguide/layers/vectorimagelayer/",
          },
          {
            text: "bc-vector-layer",
            link: "/componentsguide/layers/vectorlayer/",
          },
          {
            text: "bc-vector-tile-layer",
            link: "/componentsguide/layers/vectortilelayer/",
          },
          {
            text: "bc-webgl-points-layer",
            link: "/componentsguide/layers/webglpointslayer/",
          },
          {
            text: "bc-webgl-tile-layer",
            link: "/componentsguide/layers/webgltilelayer/",
          },
        ],
      },
      {
        text: "资源",
        collapsed: true,
        items: [
          {
            text: "bc-source-bingmaps",
            link: "/componentsguide/sources/bing/",
          },
          {
            text: "bc-source-cluster",
            link: "/componentsguide/sources/cluster/",
          },
          {
            text: "bc-source-image-static",
            link: "/componentsguide/sources/imagestatic/",
          },
          {
            text: "bc-source-image-wms",
            link: "/componentsguide/sources/imagewms/",
          },
          {
            text: "bc-source-osm",
            link: "/componentsguide/sources/osm/",
          },
          {
            text: "bc-source-stamen",
            link: "/componentsguide/sources/stamen/",
          },
          {
            text: "bc-source-tianditu",
            link: "/componentsguide/sources/tianditu/",
          },
          {
            text: "bc-source-tile-arcgis-rest",
            link: "/componentsguide/sources/arcgisrest/",
          },
          {
            text: "bc-source-tile-json",
            link: "/componentsguide/sources/tilejson/",
          },
          {
            text: "bc-source-tile-wms",
            link: "/componentsguide/sources/tilewms/",
          },
          {
            text: "bc-source-vector",
            link: "/componentsguide/sources/vector/",
          },
          {
            text: "bc-source-vector-tile",
            link: "/componentsguide/sources/vectortile/",
          },
          {
            text: "bc-source-webglpoints",
            link: "/componentsguide/sources/webglpoints/",
          },
          {
            text: "bc-source-wmts",
            link: "/componentsguide/sources/wmts/",
          },
          {
            text: "bc-source-xyz",
            link: "/componentsguide/sources/xyz/",
          },
        ],
      },
      {
        text: "矢量数据",
        collapsed: true,
        items: [
          {
            text: "bc-feature",
            link: "/componentsguide/feature/",
          },
          {
            text: "几何",
            collapsed: true,
            items: [
              {
                text: "bc-geom-circle",
                link: "/componentsguide/geometries/geomcircle/",
              },
              {
                text: "bc-geom-line-string",
                link: "/componentsguide/geometries/geomlinestring/",
              },
              {
                text: "bc-geom-point",
                link: "/componentsguide/geometries/geompoint/",
              },
              {
                text: "bc-geom-polygon",
                link: "/componentsguide/geometries/geompolygon/",
              },
              {
                text: "bc-geom-multi-line-string",
                link: "/componentsguide/geometries/geommultilinestring/",
              },
              {
                text: "bc-geom-multi-point",
                link: "/componentsguide/geometries/geommultipoint/",
              },
              {
                text: "bc-geom-multi-polygon",
                link: "/componentsguide/geometries/geommultipolygon/",
              },
            ],
          },
          {
            text: "风格",
            collapsed: true,
            items: [
              {
                text: "bc-style",
                link: "/componentsguide/styles/style/",
              },
              {
                text: "bc-style-circle",
                link: "/componentsguide/styles/circle/",
              },
              {
                text: "bc-style-fill",
                link: "/componentsguide/styles/fill/",
              },
              {
                text: "bc-style-flowline",
                link: "/componentsguide/styles/flowline/",
              },
              {
                text: "bc-style-icon",
                link: "/componentsguide/styles/icon/",
              },
              {
                text: "bc-style-stroke",
                link: "/componentsguide/styles/stroke/",
              },
              {
                text: "bc-style-text",
                link: "/componentsguide/styles/text/",
              },
            ],
          },
          {
            text: "互动",
            collapsed: true,
            items: [
              {
                text: "bc-interaction-clusterselect",
                link: "/componentsguide/interactions/clusterselect/",
              },
              {
                text: "bc-interaction-draw",
                link: "/componentsguide/interactions/draw/",
              },
              {
                text: "bc-interaction-dragrotate",
                link: "/componentsguide/interactions/dragrotate/",
              },
              {
                text: "bc-interaction-dragrotatezoom",
                link: "/componentsguide/interactions/dragrotatezoom/",
              },
              {
                text: "bc-interaction-modify",
                link: "/componentsguide/interactions/modify/",
              },
              {
                text: "bc-interaction-select",
                link: "/componentsguide/interactions/select/",
              },
              {
                text: "bc-interaction-snap",
                link: "/componentsguide/interactions/snap/",
              },
              {
                text: "bc-interaction-transform",
                link: "/componentsguide/interactions/transform/",
              },
            ],
          },
          {
            text: "动画",
            collapsed: true,
            items: [
              {
                text: "bc-animation-drop",
                link: "/componentsguide/animations/drop/",
              },
              {
                text: "bc-animation-fade",
                link: "/componentsguide/animations/fade/",
              },
              {
                text: "bc-animation-path",
                link: "/componentsguide/animations/path/",
              },
              {
                text: "bc-animation-shake",
                link: "/componentsguide/animations/shake/",
              },
              {
                text: "bc-animation-slide",
                link: "/componentsguide/animations/slide/",
              },
              {
                text: "bc-animation-teleport",
                link: "/componentsguide/animations/teleport/",
              },
              {
                text: "bc-animation-zoom",
                link: "/componentsguide/animations/zoom/",
              },
            ],
          },
        ],
      },
      {
        text: "地图控制",
        collapsed: true,
        items: [
          {
            text: "Demo",
            link: "/componentsguide/mapcontrols/",
          },
          {
            text: "bc-attribution-control",
            link: "/componentsguide/mapcontrols/attribution/",
          },
          {
            text: "bc-context-menu-control",
            link: "/componentsguide/mapcontrols/contextmenu/",
          },
          {
            text: "bc-fullscreen-control",
            link: "/componentsguide/mapcontrols/fullscreen/",
          },
          {
            text: "bc-layerswitcher-control",
            link: "/componentsguide/mapcontrols/layerswitcher/",
          },
          {
            text: "bc-layerswitcherimage-control",
            link: "/componentsguide/mapcontrols/layerswitcherimage/",
          },
          {
            text: "bc-mouseposition-control",
            link: "/componentsguide/mapcontrols/mouseposition/",
          },
          {
            text: "bc-overviewmap-control",
            link: "/componentsguide/mapcontrols/overviewmap/",
          },
          {
            text: "bc-printdialog-control",
            link: "/componentsguide/mapcontrols/printdialog/",
          },
          {
            text: "bc-rotate-control",
            link: "/componentsguide/mapcontrols/rotate/",
          },
          {
            text: "bc-scaleline-control",
            link: "/componentsguide/mapcontrols/scaleline/",
          },
          {
            text: "bc-swipe-control",
            link: "/componentsguide/mapcontrols/swipe/",
          },
          {
            text: "bc-toggle-control",
            link: "/componentsguide/mapcontrols/toggle/",
          },
          {
            text: "bc-zone-control",
            link: "/componentsguide/mapcontrols/zone/",
          },
          {
            text: "bc-zoom-control",
            link: "/componentsguide/mapcontrols/zoom/",
          },
          {
            text: "bc-zoomslider-control",
            link: "/componentsguide/mapcontrols/zoomslider/",
          },
          {
            text: "bc-zoomtoextent-control",
            link: "/componentsguide/mapcontrols/zoomtoextent/",
          },
        ],
      },
    ],
    search: {
      provider: "local",
    },
  },
};

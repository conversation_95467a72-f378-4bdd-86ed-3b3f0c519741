<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 15:04:42
-->

# bc-heatmap-layer

`bc-heatmap-layer` 可以将来自各种后端服务的矢量数据渲染为热图。它应该与 `bc-source-vector` 组件一起使用。

<script lang="ts" setup>
import HeatmapLayerDemo from "@demos/HeatmapLayerDemo.vue"
</script>

<ClientOnly>
<HeatmapLayerDemo />
</ClientOnly>

## 用法

下面的示例展示了如何使用 `bc-heatmap-layer` 和 `bc-source-vector` 从后端源渲染热图。

::: code-group

<<< ../../../../src/demos/HeatmapLayerDemo.vue

:::

## 属性

### weight

- **类型**: `String` | `Function`
- **默认值**: `'weight'`

用于权重的要素属性或从要素返回权重的函数。权重值的范围应为 0 到 1（超出该范围的值将被限制在该范围内）。

### extent

- **类型**: `Array`

图层渲染的边界范围。该图层不会在此范围之外进行渲染。

### blur

- **类型**: `Number`
- **默认值**: `15`

模糊大小（以像素为单位）。

### radius

- **类型**: `Number`

半径大小（以像素为单位）。

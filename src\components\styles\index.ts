import type { App } from "vue";
import BcStyle from "./BcStyle.vue";
import BcStyleCircle from "./BcStyleCircle.vue";
import BcStyleStroke from "./BcStyleStroke.vue";
import BcStyleFill from "./BcStyleFill.vue";
import BcStyleIcon from "./BcStyleIcon.vue";
import BcStyleText from "./BcStyleText.vue";
import BcStyleFlowline from "./BcStyleFlowline.vue";
import type { FeatureLike } from "ol/Feature";
import type { Style } from "ol/style";

let installed = false;

type OverrideStyleFunction = (
  feature: FeatureLike,
  currentStyle: Style,
  resolution: number
) => Style | Style[] | void;

function install(app: App) {
  if (installed) return;
  installed = true;

  app.component("bc-style", BcStyle);
  app.component("bc-style-circle", BcStyleCircle);
  app.component("bc-style-stroke", BcStyleStroke);
  app.component("bc-style-fill", BcStyleFill);
  app.component("bc-style-icon", BcStyleIcon);
  app.component("bc-style-text", BcStyleText);
  app.component("bc-style-flowline", BcStyleFlowline);
}

export default install;

export {
  install,
  BcStyle,
  BcStyleStroke,
  BcStyleFill,
  BcStyleIcon,
  BcStyleText,
  BcStyleFlowline,
  BcStyleCircle,
  type OverrideStyleFunction,
};

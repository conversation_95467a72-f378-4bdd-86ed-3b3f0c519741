/*
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: G<PERSON>er<PERSON>
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 09:59:41
 */
import type { App } from "vue";
import BcSourceBingMaps from "./BcSourceBingMaps.vue";
import BcSourceCluster from "./BcSourceCluster.vue";
import BcSourceImageStatic from "./BcSourceImageStatic.vue";
import BcSourceImageWMS from "./BcSourceImageWMS.vue";
import BcSourceOSM from "./BcSourceOSM.vue";
import BcSourceStamen from "./BcSourceStamen.vue";
import BcSourceTianDiTu from "./BcSourceTianDiTu.vue";
import BcSourceTileArcGISRest from "@/components/sources/BcSourceTileArcGISRest.vue";
import BcSourceTileJSON from "./BcSourceTileJSON.vue";
import BcSourceTileWMS from "./BcSourceTileWMS.vue";
import BcSourceVector from "./BcSourceVector.vue";
import BcSourceVectorTile from "./BcSourceVectorTile.vue";
import BcSourceWebglPoints from "./BcSourceWebglPoints.vue";
import BcSourceXYZ from "./BcSourceXYZ.vue";
import BcSourceWMTS from "./BcSourceWMTS.vue";

let installed = false;

function install(app: App) {
  if (installed) return;
  installed = true;

  app.component("bc-source-bingmaps", BcSourceBingMaps);
  app.component("bc-source-cluster", BcSourceCluster);
  app.component("bc-source-image-static", BcSourceImageStatic);
  app.component("bc-source-image-wms", BcSourceImageWMS);
  app.component("bc-source-osm", BcSourceOSM);
  app.component("bc-source-stamen", BcSourceStamen);
  app.component("bc-source-tianditu", BcSourceTianDiTu);
  app.component("bc-source-tile-arcgis-rest", BcSourceTileArcGISRest);
  app.component("bc-source-tile-json", BcSourceTileJSON);
  app.component("bc-source-tile-wms", BcSourceTileWMS);
  app.component("bc-source-vector", BcSourceVector);
  app.component("bc-source-vector-tile", BcSourceVectorTile);
  app.component("bc-source-webglpoints", BcSourceWebglPoints);
  app.component("bc-source-xyz", BcSourceXYZ);
  app.component("bc-source-wmts", BcSourceWMTS);
}

export default install;

export {
  install,
  BcSourceBingMaps,
  BcSourceCluster,
  BcSourceImageStatic,
  BcSourceImageWMS,
  BcSourceOSM,
  BcSourceStamen,
  BcSourceTianDiTu,
  BcSourceTileArcGISRest,
  BcSourceTileJSON,
  BcSourceTileWMS,
  BcSourceVector,
  BcSourceWebglPoints,
  BcSourceXYZ,
  BcSourceWMTS,
};

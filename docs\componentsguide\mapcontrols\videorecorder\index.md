# bc-videorecorder-control

> 简单的切换控件 该控件可以通过交互来创建以控制其激活。

## Demo

请参阅[所有地图控件的演示页面](../index.md)

## 属性

### className

控制类名称

- **类型**: `String`

### framerate

视频的帧速率

- **类型**: `Number`
- **默认值**: `30`

### videoBitsPerSecond

视频的比特率

- **类型**: `Number`
- **默认值**: `5000000`

### videoTarget

video 元素或容器，用于在完成时添加视频，或`DIALOG`以在对话框中显示视频。

- **类型**: `String | DOMElement`

### downloadName

下载类名称

- **类型**: `String`
- **默认值**: `'mapVideo.mp4'`

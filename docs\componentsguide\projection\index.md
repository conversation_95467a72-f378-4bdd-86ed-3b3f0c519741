<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 14:51:08
-->

# bc-projection-register

> 注册自定义投影

`bc-projection-register`组件创建给定的投影定义并使其可与 OpenLayers 一起使用。

<script lang="ts" setup>
import ProjectionRegisterDemo from "@demos/ProjectionRegisterDemo.vue"
</script>

<ClientOnly>
<ProjectionRegisterDemo />
</ClientOnly>

## 用法

下面的示例展示了如何在地图上注册和使用自定义投影。

::: code-group

<<< ../../../src/demos/ProjectionRegisterDemo.vue

:::

## 属性

### projectionName

投影的名称（例如`EPSG:32640`）

- **类型**: `string`
- **Required**

### projectionDef

投影定义为字符串（例如）或由[proj4](https://www.npmjs.com/package/proj4)"+proj=utm +zone=40 +datum=WGS84 +units=m +no_defs"形成的对象。

- **类型**: `string` | `ProjectionDefinition`
- **Required**

### projectionExtent

预测的可用范围

- **类型**: `[number, number, number, number]` | `Extend` | `undefined`

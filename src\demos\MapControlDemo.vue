<template>
  <ul class="checkbox-list">
    <li>
      <input
        type="checkbox"
        id="fullscreencontrol"
        v-model="fullscreencontrol"
      />
      <label for="fullscreencontrol">bc-fullscreen-control</label>
    </li>
    <li>
      <input type="checkbox" id="attribution" v-model="attributioncontrol" />
      <label for="attribution">bc-attribution-control</label>
    </li>
    <li>
      <input type="checkbox" id="zoom" v-model="zoomcontrol" />
      <label for="zoom">bc-zoom-control</label>
    </li>
    <li>
      <input type="checkbox" id="zoomtoextent" v-model="zoomtoextentcontrol" />
      <label for="zoomtoextent">bc-zoomtoextent-control</label>
    </li>
    <li>
      <input type="checkbox" id="zoomslider" v-model="zoomslidercontrol" />
      <label for="zoomslider">bc-zoomslider-control</label>
    </li>
    <li>
      <input type="checkbox" id="scaleline" v-model="scalelinecontrol" />
      <label for="scaleline">bc-scaleline-control</label>
    </li>
    <li>
      <input type="checkbox" id="overviewmap" v-model="overviewmapcontrol" />
      <label for="overviewmap">bc-overviewmap-control</label>
    </li>
    <li>
      <input
        type="checkbox"
        id="mousepositioncontrol"
        v-model="mousepositioncontrol"
      />

      <label for="mousepositioncontrol">bc-mouseposition-control</label>
    </li>
    <li>
      <input type="checkbox" id="rotatecontrol" v-model="rotatecontrol" />
      <label for="rotatecontrol">bc-rotate-control</label>
    </li>
    <li>
      <input type="checkbox" id="swipecontrol" v-model="showSwipeControl" />
      <label for="swipecontrol">bc-swipe-control</label>
    </li>
    <li>
      <input
        type="checkbox"
        id="layerswitchercontrol"
        v-model="showLayerSwitcherControl"
      />
      <label for="layerswitchercontrol">bc-layerswitcher-control</label>
    </li>
    <li>
      <input
        type="checkbox"
        id="layerswitcherimagecontrol"
        v-model="showLayerSwitcherImageControl"
      />
      <label for="layerswitcherimagecontrol"
        >bc-layerswitcherimage-control</label
      >
    </li>
    <li>
      <input
        type="checkbox"
        id="printdialogcontrol"
        v-model="showPrintDialogControl"
      />
      <label for="printdialogcontrol">bc-printdialog-control</label>
    </li>
    <li>
      <input type="checkbox" id="togglecontrol" v-model="showToggleControl" />
      <label for="togglecontrol">bc-toggle-control</label>
    </li>

    <li>
      <input
        type="checkbox"
        id="videorecordercontrol"
        v-model="showVideoRecorderControl"
      />
      <label for="videorecordercontrol">bc-videorecorder-control</label>
    </li>
  </ul>

  <bc-map
    ref="map"
    :loadTilesWhileAnimating="true"
    :loadTilesWhileInteracting="true"
    style="height: 400px"
  >
    <bc-view
      ref="view"
      :center="center"
      :rotation="rotation"
      :zoom="zoom"
      :projection="projection"
    />

    <bc-fullscreen-control v-if="fullscreencontrol" />
    <bc-mouseposition-control v-if="mousepositioncontrol" />
    <bc-attribution-control v-if="attributioncontrol" />

    <bc-overviewmap-control v-if="overviewmapcontrol">
      <bc-tile-layer>
        <bc-source-osm />
      </bc-tile-layer>
    </bc-overviewmap-control>

    <bc-scaleline-control v-if="scalelinecontrol" />
    <bc-rotate-control v-if="rotatecontrol" />
    <bc-zoom-control v-if="zoomcontrol" />
    <bc-zoomslider-control v-if="zoomslidercontrol" />
    <bc-zoomtoextent-control
      v-if="zoomtoextentcontrol"
      :extent="[23.906, 42.812, 46.934, 34.597]"
      tipLabel="Fit to Turkey"
    />

    <bc-swipe-control
      ref="swipeControl"
      v-if="showSwipeControl && layerList.length > 0"
      :layerList="layerList"
    />

    <bc-layerswitcher-control
      v-if="showLayerSwitcherControl && layerList.length > 0"
    />

    <bc-layerswitcherimage-control
      v-if="showLayerSwitcherImageControl && layerList.length > 0"
    />

    <bc-printdialog-control v-if="showPrintDialogControl" />

    <bc-toggle-control
      v-if="showToggleControl"
      :html="'log'"
      :onToggle="($event) => console.log('ol-toggle-control: onToggle', $event)"
    />

    <bc-videorecorder-control
      v-if="showVideoRecorderControl"
      @stop="videoStopped"
    />

    <bc-tile-layer ref="jawgLayer" title="JAWG">
      <bc-source-xyz
        crossOrigin="anonymous"
        url="https://c.tile.jawg.io/jawg-dark/{z}/{x}/{y}.png?access-token=87PWIbRaZAGNmYDjlYsLkeTVJpQeCfl2Y61mcHopxXqSdxXExoTLEv7dwqBwSWuJ"
      />
    </bc-tile-layer>

    <bc-tile-layer ref="bingLayer">
      <bc-source-bingmaps
        apiKey="AjtUzWJBHlI3Ma_Ke6Qv2fGRXEs0ua5hUQi54ECwfXTiWsitll4AkETZDihjcfeI"
        :imagerySet="'CanvasDark'"
      />
    </bc-tile-layer>

    <bc-tile-layer ref="osmLayer">
      <bc-source-osm />
    </bc-tile-layer>
  </bc-map>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
const center = ref([40, 40]);
const projection = ref("EPSG:4326");
const zoom = ref(8);
const rotation = ref(0);
const layerList = ref([]);
const jawgLayer = ref(null);
const osmLayer = ref(null);
const bingLayer = ref(null);
const swipeControl = ref(null);

const fullscreencontrol = ref(true);
const attributioncontrol = ref(true);
const zoomcontrol = ref(true);
const zoomslidercontrol = ref(true);
const zoomtoextentcontrol = ref(true);
const scalelinecontrol = ref(true);
const overviewmapcontrol = ref(true);
const mousepositioncontrol = ref(true);
const rotatecontrol = ref(true);
const showSwipeControl = ref(true);
const showLayerSwitcherControl = ref(true);
const showLayerSwitcherImageControl = ref(true);
const showPrintDialogControl = ref(true);
const showToggleControl = ref(true);
const showVideoRecorderControl = ref(true);

const videoStopped = (event) => {
  console.log(event);
};

onMounted(() => {
  layerList.value.push(jawgLayer.value.tileLayer);
  layerList.value.push(osmLayer.value.tileLayer);
  console.log(layerList.value);
});
</script>

<style>
ul.checkbox-list {
  columns: 2;
  padding: 0;
}
ul.checkbox-list > li {
  list-style: none;
}
</style>

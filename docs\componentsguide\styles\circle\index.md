<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 13:58:06
-->

# bc-style-circle

> 将点样式设置为圆形。

在 `bc-style` 中使用它可以将点设置为圆形。

<script lang="ts" setup>
import StyleDemo from "@demos/StyleDemo.vue"
</script>

<ClientOnly>
<StyleDemo/>
</ClientOnly>

## 用法

对矢量图层内的特征进行样式设计。

::: code-group

<<< ../../../../src/demos/StyleDemo.vue

:::

## 属性

### radius

- **类型**: `Number`

### scale

- **类型**: `Number`

<template>
  <bc-map
    :loadTilesWhileAnimating="true"
    :loadTilesWhileInteracting="true"
    style="height: 400px"
  >
    <bc-view
      ref="view"
      :center="center"
      :rotation="rotation"
      :zoom="zoom"
      :projection="projection"
    />

    <bc-tile-layer>
      <bc-source-osm />
    </bc-tile-layer>

    <bc-vector-layer>
      <bc-source-vector>
        <bc-feature>
          <bc-geom-point :coordinates="[40, 40]"></bc-geom-point>
          <bc-style>
            <bc-style-circle :radius="30">
              <bc-style-fill color="white"></bc-style-fill>
              <bc-style-stroke color="red" :width="10"></bc-style-stroke>
            </bc-style-circle>
          </bc-style>
        </bc-feature>
      </bc-source-vector>
    </bc-vector-layer>
  </bc-map>
</template>

<script lang="ts" setup>
import { ref } from "vue";

const center = ref([40, 40]);
const projection = ref("EPSG:4326");
const zoom = ref(8);
const rotation = ref(0);
</script>

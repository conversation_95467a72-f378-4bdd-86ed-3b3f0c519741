# bc-view

View 对象表示地图的简单 2D 视图。

这是用于更改地图的中心、分辨率和旋转的对象。

视图有一个投影。 投影决定了中心的坐标系，其单位决定了分辨率的单位（每像素的投影单位）。 默认投影为球面墨卡托投影 (EPSG:3857)。

<script lang="ts" setup>
import ViewDemo from "@demos/ViewDemo.vue"
</script>

<ClientOnly>
<ViewDemo />
</ClientOnly>

## 用法

EPSG:4326 投影视图的简单地图示例。另请参阅 bc-map 组件的文档

::: code-group

<<< ../../../src/demos/ViewDemo.vue

:::

## 属性

### center

- **类型**: `number[]`
- **默认值**: `[0, 0]`

所提供的`projection`中地图视图的中心坐标。

### zoom

- **类型**: `number`
- **默认值**: `0`

用于将视图分辨率计算为`int`值的缩放级别。仅在未定义`resolution`时使用。

### rotation

- **类型**: `number`
- **默认值**: `0`

视图的初始旋转，以**弧度**为单位（顺时针正旋转）。

### resolution

- **类型**: `number`
- **默认值**: `undefined`

视图的初始分辨率。 单位是每像素的投影单位（例如每像素米）。 设置此选项的另一种方法是设置`zoom`

### resolutions

- **类型**: `number[]`
- **默认值**: `undefined`

分辨率来确定分辨率约束。如果设置`max-resolution`、`min-resolution`、`min-zoom`、`max-zoom`，那么 `zoom-factor` 选项将被忽略。

### projection

- **类型**: `string` or `object (options projection)`
- **默认值**: `EPSG:3857`

视图内部投影。 这是 OpenLayers 组件的投影

### maxZoom

- **类型**: `number`
- **默认值**: `28`

用于确定分辨率约束的最大缩放级别。

### minZoom

- **类型**: `number`
- **默认值**: `0`

用于确定分辨率约束的最小缩放级别。

### maxResolution

- **类型**: `number`
- **默认值**: `undefined`

用于确定分辨率约束的最大分辨率。

### minResolution

- **类型**: `number`
- **默认值**: `undefined`

用于确定分辨率约束的最小分辨率。

### constrainRotation

- **类型**: `boolean | number`
- **默认值**: `true`

旋转约束。`false`意味着没有约束。`true`意味着没有约束，但会捕捉到接近零的零。数字将旋转限制为该数量的值。例如，`4`将旋转限制为`0`、`90`、`180`和`270`度。

### enableRotation

- **类型**: `boolean`
- **默认值**: `true`

启用旋转。默认为`true`. 如果`false`使用始终将旋转设置为零的旋转约束。

### extent

- **类型**: `number[leftBottomX, leftBottomY, rightTopX, rightTopY]`
- **默认值**: `undefined`

视图投影中定义的约束范围`center`（即中心）不能设置在此范围之外。

### zoomFactor

- **类型**: `number`
- **默认值**: `2`

用于确定分辨率约束的缩放系数。

### constrainOnlyCenter

- **类型**: `Boolean`
- **默认值**: `false`

如果为 `true`，则范围约束将仅应用于视图中心，而不是整个范围。

### smoothExtentConstraint

- **类型**: `Boolean`
- **默认值**: `true`

如果为 `true`，则范围约束将顺利应用，即允许视图稍微超出给定范围。

### multiWorld

- **类型**: `Boolean`
- **默认值**: `false`

如果为 `false`，则视图受到限制，因此只有一个世界可见，并且您无法平移到边缘。如果为 `true`，地图可能会以低缩放级别显示多个世界。仅当投影是全局时才使用。请注意，如果还提供了范围，则优先考虑范围。

### constrainResolution

- **类型**: `Boolean`
- **默认值**: `false`

如果为 `true`，则交互后视图将始终以最接近的缩放级别进行动画处理；`false` 表示允许中间缩放级别。

### smoothResolutionConstraint

- **类型**: `Boolean`
- **默认值**: `true`

如果为 `true`，则将平滑应用分辨率最小/最大值，即允许视图稍微超出给定的分辨率或缩放范围。

### showFullExtent

- **类型**: `Boolean`
- **默认值**: `false`

如果为 `true`，则将平滑应用分辨率最小/最大值，即允许视图稍微超出给定的分辨率或缩放范围。

### padding

- **类型**: `Array`
- **默认值**: `() => [0, 0, 0, 0]`

如果为 `true`，则将平滑应用分辨率最小/最大值，即允许视图稍微超出给定的分辨率或缩放范围。

## 事件

- `centerChanged`
- `zoomChanged`
- `resolutionChanged`
- `rotationChanged`

## 方法

### adjustCenter(deltaCoordinates)

将相对坐标添加到视图中心。任何范围限制都将适用。

- **参数**:
- `deltaCoordinates {module:ol/coordinate~Coordinate}`
  要添加的相对值。

### adjustResolution(ratio, opt_anchor)

将视图分辨率乘以一个比率，可以选择使用锚点。任何分辨率限制都将适用。

- **参数**:
- `ratio {number}`
  适用于视图分辨率的比率。
- `anchor {module:ol/coordinate~Coordinate}`
  转变的根源。

### adjustRotation(delta, opt_anchor)

向视图旋转添加一个值，可以选择使用锚点。任何旋转约束都将适用。

- **参数**:
- `delta {number}`
  添加到缩放旋转的相对值（以弧度为单位）。
- `anchor {module:ol/coordinate~Coordinate}`
  旋转中心。

### adjustZoom(delta, opt_anchor)

向视图缩放级别添加一个值，可以选择使用锚点。任何分辨率限制都将适用。

- **参数**:
- `delta {number}`
  添加到缩放级别的相对值。
- `anchor {module:ol/coordinate~Coordinate}`
  转变的根源。

### beginInteraction()

通知视图交互已经开始。如果需要的话，视图状态将被解析为稳定的状态（取决于其约束）。

### calculateExtent(opt_size)

计算当前视图状态的范围和传递的大小。大小是计算范围应适合的框的像素尺寸。大多数情况下你想要获取整个地图的范围，即`map.getSize()`。

- **参数**:
- `size {module:ol/size~Size}`
  框像素大小。如果未提供，则将使用使用此视图的地图的大小。

### cancelAnimations()

取消任何正在进行的动画。

### centerOn(coordinate, size, position)

以坐标和视图位置为中心。

- **参数**:
- `coordinate {module:ol/coordinate~Coordinate}`
  坐标。
- `size {module:ol/size~Size}`
  框像素大小。
- `position {module:ol/pixel~Pixel}`
  放置在视图的中心。

### endInteraction(opt_duration, opt_resolutionDirection, opt_anchor)

通知视图交互已结束。如果需要的话，视图状态将被解析为稳定的状态（取决于其约束）。

- **参数**:
- `duration {number}`
  动画持续时间（以毫秒为单位）。
- `resolutionDirection {number}`
  向哪个方向缩放。
- `anchor {module:ol/coordinate~Coordinate}}`
  转变的根源。

### getAnimating()

确定视图是否正在动画化。

**返回值**:
视图正在动画化。

### getCenter()

获取视图中心。

**返回值**:
视图的中心。

### getInteracting()

确定用户是否正在与视图交互，例如平移或缩放。

**返回值**:
正在与视图交互。

### getMaxResolution()

获取视图的最大分辨率。

**返回值**:
视图的最大分辨率。

### getMaxZoom()

获取视图的最大缩放级别。

**返回值**:
最大缩放级别。

### getMinResolution()

获取视图的最小分辨率。

**返回值**:
视图的最小分辨率。

### getMinZoom()

获取视图的最小缩放级别。

**返回值**:
最小缩放级别。

### getProjection()

获取视图投影。

**返回值**:
视图的投影。

### getResolution()

获取视图分辨率。

**返回值**:
视图的分辨率。

### getResolutionForExtent(extent, opt_size)

获取所提供范围的分辨率（以地图单位为单位）和大小（以像素为单位）。

- **参数**:
- `extent {module:ol/extent~Extent}`
  程度。
- `size {module:ol/size~Size}`
  框像素大小。

**返回值**:
所提供的范围将以给定大小呈现的分辨率。

### getResolutionForZoom(zoom)

获取缩放级别的分辨率。

- **参数**:
- `zoom {number}`
  缩放级别。

**返回值**:
所提供缩放级别的视图分辨率。

### getResolutions()

获取视图的分辨率。这将返回传递给视图构造函数的分辨率数组，如果没有给出，则返回未定义的数组。

**返回值**:
视图的分辨率。

### getRotation()

获取视图旋转。

**返回值**:
视图的旋转（以弧度为单位）。

### getZoom()

获取当前的缩放级别。如果视图不限制分辨率，或者正在进行交互或动画，则此方法可能返回非整数缩放级别。

**返回值**:
缩放级别。

### getZoomForResolution(resolution)

获取分辨率的缩放级别。

- **参数**:
- `resolution {number}`
  分辨率。

**返回值**:
当前分辨率的缩放级别。

### setCenter(center)

设置当前视图的中心。任何范围限制都将适用。

- **参数**:
- `center {	module:ol/coordinate~Coordinate | undefined}`
  视图的中心。

### setConstrainResolution(enabled)

设置视图是否应允许中间缩放级别。

- **参数**:
- `enabled {boolean}`
  分辨率是否受到限制。

### setMaxZoom(zoom)

为视图设置新的最大缩放级别。

- **参数**:
- `zoom {number}`
  最大缩放级别。

### setMinZoom(zoom)

为视图设置新的最小缩放级别。

- **参数**:
- `zoom {number}`
  最小缩放级别。

### setResolution(resolution)

设置该视图的分辨率。任何分辨率限制都将适用。

- **参数**:
- `resolution {number | undefined}`
  视图的分辨率。

### setRotation(rotation)

设置该视图的旋转。任何旋转约束都将适用。

- **参数**:
- `rotation {number}`
  视图的旋转（以弧度为单位）。

### setZoom(zoom)

Z 缩放至特定缩放级别。任何分辨率限制都将适用。

- **参数**:
- `zoom {number}`
  zoom {number}缩放级别。

### fit(geometryOrExtent, opt_options)

根据给定的地图大小和边界拟合给定的几何图形或范围。大小是适合范围的框的像素尺寸。在大多数情况下，您需要使用地图大小，即 `map.getSize()`。照顾地图角度。

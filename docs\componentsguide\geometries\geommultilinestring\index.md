<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 11:51:11
-->

# bc-geom-multi-line-string

`bc-geom-multi-line-string` 可以在 `bc-feature` 内部使用以在地图上绘制多条线。

<script lang="ts" setup>
import MultiLineString from "@demos/MultiLineString.vue"
</script>
<ClientOnly>
<MultiLineString />
</ClientOnly>

## 用法

::: code-group

<<< ../../../../src/demos/MultiLineString.vue

:::

## 属性

### coordinates

- **类型**: `number[][][]`
  线数组，每条线都是以地图投影为单位的线字符串。

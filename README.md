# bcgis-ol

> Web map [Vue](https://vuejs.org/ "Vue Homepage") components with the power of [OpenLayers](https://openlayers.org/ "OpenLayers Homepage")

OpenLayers Useful 3rd party libraries https://openlayers.org/3rd-party/

## Links

## Install

```bash
# install current bcgis-ol version
npm install bcgis-ol
or
yarn add bcgis-ol
```

## Overview

**bcgis-ol** is components library that brings the powerful **OpenLayers API** to the **Vue3** reactive world.
It can display maps with tiled, raster or vector layers loaded from different sources.

## Requirements

<!-- auto-generated-peer-dependency-requirements START -->

- **[ol](https://github.com/openlayers/openlayers)**: `^7.4.0`
- **[ol-contextmenu](https://github.com/jonataswalker/ol-contextmenu)**: `^5.2.1`
- **[ol-ext](https://github.com/Viglino/ol-ext)**: `^4.0.8`
- **[vue](https://github.com/vuejs/core)**: `^3.0.0`

<!-- auto-generated-peer-dependency-requirements END -->

## License

**MIT** (c) Melih Altıntaş

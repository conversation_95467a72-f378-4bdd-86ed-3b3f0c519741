# bc-webgl-tile-layer

此示例使用 `WebGL` 在地图上光栅化图块。

<script lang="ts" setup>
import WebglTileLayerDemo from "@demos/WebglTileLayerDemo.vue"
</script>
<ClientOnly>
<WebglTileLayerDemo />
</ClientOnly>

## 用法

::: code-group

<<< ../../../../src/demos/WebglTileLayerDemo.vue

:::

## 属性

### className

- **类型**: `string`
- **默认值**: `bc-layer`

要设置为图层元素的 CSS 类名称。

### opacity

- **类型**: `number `
- **默认值**: `1`

不透明度 (0, 1)。

### visible

- **类型**: `boolean`
- **默认值**: `true`

能见度。

### extent

- **类型**: `Array`

图层渲染的边界范围。该图层不会在此范围之外进行渲染。

### zIndex

- **类型**: `number`

图层渲染的 `z-index`。在渲染时，图层将首先按 Z 索引排序，然后按位置排序。

### minResolution

- **类型**: `number`

该图层可见的最小分辨率（含）。

### maxResolution

- **类型**: `number`

最大分辨率（独占），低于该分辨率该图层将可见。

### minZoom

- **类型**: `number`

最小视图缩放级别（不包括），高于该级别该图层将可见。

### maxZoom

- **类型**: `number`

该图层可见的最大视图缩放级别（含）。

### preload

- **类型**: `number`
- **默认值**: `0`
  将加载达到预加载级别的低分辨率图块。

<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 14:05:54
-->

# bc-style-stroke

> 设置直线或多边形的笔划样式。

在 `bc-style` 内部使用它来设置线条和多边形的样式，在 `bc-style-circle` 内部使用它来设置圆形样式，在 `bc-style-text` 内部使用它来设置文本样式。

<script lang="ts" setup>
import LineString from "@demos/LineString.vue"
</script>
<ClientOnly>
<LineString />
</ClientOnly>

## 用法

::: code-group

<<< ../../../../src/demos/LineString.vue

:::

## 属性

### color

- **类型**: `array`, `string`

描边颜色。可以是十六进制，也可以是 RGBA 数组，其中红色、绿色和蓝色值介于 0 和 255 之间，alpha 值介于 0 和 1 之间（包含 0 和 1）。

### lineCap

- **类型**: `string`
- **默认值**: `round`,

如何设置线条末端的样式（如果有）。选项有`round`、`butt`、 和`square`。

### lineJoin

- **类型**: `string`
- **默认值**: `round`,

如何设计线段接头的样式。选项有`round`, `bevel`,`miter`

### lineDash

- **类型**: `array`

指定交替绘制直线和间隙的距离的数字数组。

### lineDashOffset

- **类型**: `number`
- **默认值**: `0`

将虚线图案的起点偏移给定量。

### miterLimit

- **类型**: `number`
- **默认值**: `10`

何时在锐角上切角。作为参考，请查看 Mozilla 开发者网络上的[此条目](https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/miterLimit)。

### width

- **类型**: `Number`
- **默认值**: `1.25`

描边的宽度。

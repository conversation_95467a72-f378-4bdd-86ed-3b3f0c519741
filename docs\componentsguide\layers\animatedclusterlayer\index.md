# bc-animated-clusterlayer

`bc-animated-clusterlayer`是一个在缩放变化时对簇进行动画处理的图层。`bc-interaction-clusterselect`是一种选择交互。在选定的簇上弹开以显示特征。所显示的功能本身是可以选择的。显示的特征本身就是一个具有包含原始特征的属性“特征”的簇。

<script lang="ts" setup>
import AnimatedClusterDemo from "@demos/AnimatedClusterDemo.vue"
</script>

<ClientOnly>
<AnimatedClusterDemo />
</ClientOnly>

## 用法

::: code-group

<<< ../../../../src/demos/AnimatedClusterDemo.vue

:::

## 性能提示

具有大量标记/簇的地图的性能可能会受到数据传递到`bc-source-vector`. 如果您的地图无法正确渲染、交互元素被阻塞，或者最终出现内存泄漏，则可能值得从直接传递功能切换到从 URL 请求功能。您可以在的性能部分 ol-source-vector[性能部分`bc-source-vector`](../../sources/vector/#performance-hints)找到更多信息。

```vue
<template>
  <bc-map
    :loadTilesWhileAnimating="true"
    :loadTilesWhileInteracting="true"
    style="height: 400px"
  >
    <!-- ... -->

    <bc-animated-clusterlayer :animationDuration="500" :distance="40">
      <bc-source-vector :url="url" :format="geoJson" />
    </bc-animated-clusterlayer>
  </bc-map>
</template>

<script lang="ts" setup>
import { /* ... */, computed, inject } from "vue";

// ...

const format = inject("ol-format");
const geoJson = new format.GeoJSON();
const url = computed(() => {
  return `http://localhost:3000/${count.value}`;
});
</script>
```

## 属性

### className

- **类型**: `string`
- **默认值**: `bc-layer`

要设置为图层元素的 CSS 类名称。

### opacity

- **类型**: `number`
- **默认值**: `1`

不透明度 (0, 1)。

### visible

- **类型**: `boolean`
- **默认值**: `true`

能见度。

### extent

- **类型**: `Array`

图层渲染的边界范围。该图层不会在此范围之外进行渲染。

### zIndex

- **类型**: `number`

图层渲染的 `z-index`渲染时，图层将首先按 Z 索引排序，然后按位置排序。

### minResolution

- **类型**: `number`

该图层可见的最小分辨率（含）。

### maxResolution

- **类型**: `number`

最大分辨率（独占），低于该分辨率该图层将可见。

### minZoom

- **类型**: `number`

最小视图缩放级别（不包括），高于该级别该图层将可见。

### maxZoom

- **类型**: `number`

该图层可见的最大视图缩放级别（含）。

### renderBuffer

- **类型**: `number`
- **默认值**: `100`

从矢量源获取要素以进行渲染或命中检测时，渲染器使用的视口范围周围的缓冲区（以像素为单位）。推荐值：最大符号、线宽或标签的尺寸。

### updateWhileAnimating

- **类型**: `Boolean`
- **默认值**: `false`

设置为 `true` 时，将在动画期间重新创建要素批次。这意味着不会显示任何矢量被剪切，但该设置会对大量矢量数据的性能产生影响。当设置为 `false` 时，当没有动画处于活动状态时将重新创建批次。

### updateWhileInteracting

- **类型**: `Boolean`
- **默认值**: `false`

当设置为 `true` 时，将在交互期间重新创建特征批次。另请参见 updateWhileAnimating。

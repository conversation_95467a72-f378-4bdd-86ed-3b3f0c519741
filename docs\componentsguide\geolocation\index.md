<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 15:48:52
-->

# bc-geolocation

> HTML5 地理定位包装器

`bc-geoloc`作为 Vue 组件提供 HTML5 地理定位功能。[Geolocation API](https://www.w3.org/TR/geolocation-API/)用于定位用户的位置。您可以将其放置到组件的默认插槽`bc-map`中。

<script lang="ts" setup>
import GeoLocationDemo from "@demos/GeoLocationDemo.vue"
</script>

<ClientOnly>
<GeoLocationDemo />
</ClientOnly>

::: code-group

<<< ../../../src/demos/GeoLocationDemo.vue

:::

## 属性

### Props 继承自 OpenLayers

属性直接从 OpenLayers 传递。它们的类型和默认值可以在[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_Geolocation-Geolocation.html)中查看。只有部分属性是由 Vue / HTML 的保留关键字引起的。下面的部分描述了这种偏离的 props。

### 偏离属性

无

## 事件

您可以从底层 OpenLayers Geolocation API 访问所有事件。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_Geolocation-Geolocation.html)以查看将触发的可用事件。

```html
<bc-geolocation :projection="projection" @change:position="geoLocChange" />
```

## 方法

您可以从底层 OpenLayers Geolocation API 访问所有方法。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_Geolocation-Geolocation.html)以查看可用的方法。

要访问源代码，您可以使用 `ref()`，如下所示：

```vue
<template>
  <!-- ... -->
  <bc-geolocation :projection="projection" ref="geoLocRef" />
  <!-- ... -->
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import type Geolocation from "ol/Geolocation";

const geoLocRef = ref<{ geoLoc: Geolocation }>(null);

onMounted(() => {
  const geoLocation: Geolocation = geoLocRef.value?.geoLoc;
  // call your method on `geoLocation`
});
</script>
```

<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 16:34:02
-->

# bc-source-tianditu

> [天地图](https://www.tianditu.gov.cn/)图层源

`bc-source-tianditu`添加了显示天地图地图图块数据的功能。要使用此源，您应该在 https://console.tianditu.gov.cn/ 获取**API 密钥**。

<script lang="ts" setup>
import TiandituDemo from "@demos/TiandituDemo.vue"
</script>

<ClientOnly>
<TiandituDemo />
</ClientOnly>

## 用法

`bc-source-tianditu`使用示例

::: code-group

<<< ../../../../src/demos/TiandituDemo.vue

:::

## 属性

### Props 继承自 OpenLayers

属性直接从 OpenLayers 传递。它们的类型和默认值可以在[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_WMTS-WMTS.html)中查看。只有部分属性是由 Vue / HTML 的保留关键字引起的。下面的部分描述了这种偏离的 props。

### 偏离属性

### layerType

- **类型**: `string`
- **默认值**: `img`

选项：`img`、`vec`、`ter`、`eia`、`cta`

### tk

- **类型**: `string`

API 密钥

### isLabel

- **类型**: `Boolean`
- **默认值**: `false`

### culture

- **类型**: `String`
- **默认值**: `en-us`

## 事件

您可以从底层源访问所有事件。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_WMTS-WMTS.html)以查看将触发的可用事件。

```html
<bc-source-tianditu :url="url" @error="handleEvent" />
```

## 方法

您可以从底层源访问所有方法。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_WMTS-WMTS.html)以查看可用的方法。

要访问源代码，您可以使用`ref()`如下所示：

```vue
<template>
  <!-- ... -->
  <bc-source-tianditu :url="url" ref="sourceRef" />
  <!-- ... -->
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";

const sourceRef = ref(null);

onMounted(() => {
  const source = sourceRef.value?.source;
  // call your method on `source`
});
</script>
```

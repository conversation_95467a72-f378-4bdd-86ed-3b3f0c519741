<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 16:18:53
-->

# bc-source-vector-tile

`bc-source-vector-tile` 可以与 `bc-vector-tile-layer` 一起使用，在地图上绘制任何矢量切片数据。

<script lang="ts" setup>
import VectorTileLayerDemo from "@demos/VectorTileLayerDemo.vue"
</script>
<ClientOnly>
<VectorTileLayerDemo />
</ClientOnly>

## 用法

下面的示例展示了如何使用 `bc-vector-tile-layer` 和 `bc-source-vector-tile` 从远程后端渲染一些矢量瓦片功能。

::: code-group

<<< ../../../../src/demos/VectorTileLayerDemo.vue

:::

## 属性

### Props 继承自 OpenLayers

属性直接从 OpenLayers 传递。它们的类型和默认值可以在[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_VectorTile-VectorTile.html)中查看。只有部分属性是由 Vue / HTML 的保留关键字引起的。下面的部分描述了这种偏离的 props。

### 偏离属性

无

## 事件

您可以从底层源访问所有事件。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_VectorTile-VectorTile.html)以查看将触发的可用事件。

```html
<bc-source-vector-tile :url="url" @error="handleEvent" />
```

## 方法

您可以从底层源访问所有方法。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_VectorTile-VectorTile.html)以查看可用的方法。

<template>
  <bc-map
    :loadTilesWhileAnimating="true"
    :loadTilesWhileInteracting="true"
    style="height: 400px"
  >
    <bc-view
      ref="view"
      :center="center"
      :rotation="rotation"
      :zoom="zoom"
      :projection="projection"
    />

    <bc-tile-layer>
      <bc-source-osm />
    </bc-tile-layer>

    <bc-tile-layer>
      <bc-source-tile-arcgis-rest
        :url="arcgisUrl"
        :tileSize="[1024, 1024]"
        :params="requestParams"
      />
    </bc-tile-layer>
  </bc-map>
</template>

<script lang="ts" setup>
import { ref } from "vue";

const center = ref([4340075, 5628816]);
const projection = ref("EPSG:3857");
const zoom = ref(15);
const rotation = ref(0);
const arcgisUrl =
  "https://pkk.rosreestr.ru/arcgis/rest/services/PKK6/CadastreObjects/MapServer/export";
const requestParams = {
  layers: "show:30,27,24,23,22",
  format: "PNG32",
  f: "image",
  dpi: 96,
  transparent: true,
  bboxSR: 102100,
  imageSR: 102100,
  size: "1024,1024",
  _ts: false,
};
</script>

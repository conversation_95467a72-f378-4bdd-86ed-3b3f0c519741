<!--- Provide a general summary of your changes in the Title above -->

## Description

<!--- Describe your changes in detail -->

## Motivation and Context

<!--- Why is this change required? What problem does it solve? -->
<!--- If it fixes an open issue, please link to the issue here. -->

## How Has This Been Tested?

<!--- Please describe in detail how you tested your changes. -->
<!--- Include details of your testing environment, the tests you ran to -->
<!--- see how your change affects other areas of the code, etc. -->

## Screenshots (if appropriate):

## Types of Changes

<!--- What types of changes does your code introduce? Put an `x` in all the boxes that apply: -->

- [ ] Bug fix (non-breaking change that fixes an issue)
- [ ] New feature (non-breaking change that adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation Update
- [ ] Other (Tooling, Dependency Updates, etc.)

## Checklist:

<!--- Go over all the following points, and put an `x` in all the boxes that apply. -->
<!--- If you're unsure about any of these, don't hesitate to ask. We're here to help! -->

- [ ] My code follows the code style of this project.
- [ ] My change requires a change to the documentation.
- [ ] I have updated the documentation accordingly.

If you added a new component feature (layer, geom, source, etc.), please be sure to update the documentation:

- [ ] Add component to `output.globals` in `vite.config.ts`
- [ ] Create a `src/demos/<Component>Demo.vue`
- [ ] Create a `docs/componentsguide/<Category>/<Feature>/index.md` containing the Demo and documentation for the component
- [ ] Add the docs page to `docs/.vitepress/config.ts`
- [ ] Update the sitemap `docs/public/sitemap.xml`

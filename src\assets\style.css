.ol-viewport .ol-zoomslider {
  top: 7.5em;
  left: 0.5em;
  height: 240px;
  border: 2px solid rgba(0, 60, 136, 0.5);
}

.ol-viewport .ol-overviewmap {
  left: 3.5em;
  bottom: 2.5em;
}

.ol-viewport .ol-mouse-position {
  top: 0;
  left: 50%;
  transform: translate(-50%, 0);
  width: 70%;
  position: absolute;
  text-align: center;
}

.ol-viewport .ol-rotate {
  top: 0.5em;
  right: 2.5em;
  transition: opacity 0.25s linear, visibility 0s linear;
}

.ol-viewport .ol-layerswitcher-image .ol-layer-hidden {
  opacity: 1;
}

.ol-viewport .ol-overviewmap.ol-collapsed .ol-overviewmap-map,
.ol-viewport .ol-overviewmap.ol-uncollapsible button {
  display: block;
  opacity: 0;
}

.ol-viewport .ol-collapsed {
  background-color: rgba(255, 255, 255, 0);
}

.ol-viewport .ol-collapsed:hover {
  background-color: rgba(255, 255, 255, 0);
}

.ol-viewport .ol-layerswitcher-image button {
  background-color: transparent;
}

.ol-viewport .ol-control.ol-mapzone,
.ol-viewport .ol-control.ol-mapzone.ol-collapsed {
  top: 12.5em;
}

.ol-viewport .ol-control.ol-mapzone button {
  margin-top: 0em;
}

.ol-viewport .ol-control.ol-mapzone > div p {
  background-color: black;
}

<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 11:36:15
-->

# bc-feature

> A container for geometry

`bc-feature`可以与 一起使用`bc-vector-layer`，`bc-source-vector`将 GeoJSON 功能添加到地图中。必须`bc-feature`包含单个子元素（几何），例如。`bc-geom-point`和 `bc-style`定义了几何图形的样式。如果要显示多个 Geometry，则需要添加多个`bc-feature`。

<script lang="ts" setup>
import GeomPoint from "@demos/GeomPoint.vue"
</script>

## 用法

<ClientOnly>
<GeomPoint />
</ClientOnly>

::: code-group

<<< ../../../src/demos/GeomPoint.vue

:::

## 属性

### 属性

与功能连接的属性。

- **类型**: ` [Geometry, Object, Array]`

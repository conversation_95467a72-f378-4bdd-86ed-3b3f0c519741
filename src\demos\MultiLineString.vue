<template>
  <bc-map
    :loadTilesWhileAnimating="true"
    :loadTilesWhileInteracting="true"
    style="height: 400px"
  >
    <bc-view
      ref="view"
      :center="center"
      :rotation="rotation"
      :zoom="zoom"
      :projection="projection"
    />

    <bc-tile-layer>
      <bc-source-osm />
    </bc-tile-layer>

    <bc-vector-layer>
      <bc-source-vector>
        <bc-feature>
          <bc-geom-multi-line-string
            :coordinates="[
              [
                [116.544921, 40.451633],
                [116.545264, 40.451649],
                [116.545865, 40.451698],
                [116.546144, 40.451551],
                [116.546337, 40.451274],
                [116.546788, 40.451143],
                [116.547324, 40.451078],
              ],
              [
                [116.547839, 40.450719],
                [116.54844, 40.450506],
                [116.548933, 40.450604],
                [116.549448, 40.450604],
                [116.550242, 40.450376],
                [116.550865, 40.450163],
                [116.551702, 40.449935],
                [116.552581, 40.449576],
              ],
            ]"
          ></bc-geom-multi-line-string>
          <bc-style>
            <bc-style-stroke
              :color="strokeColor"
              :width="strokeWidth"
            ></bc-style-stroke>
          </bc-style>
        </bc-feature>
      </bc-source-vector>
    </bc-vector-layer>
  </bc-map>
</template>

<script lang="ts" setup>
import { ref } from "vue";

const center = ref([116.54875, 40.45064]);
const projection = ref("EPSG:4326");
const zoom = ref(17);
const rotation = ref(0);
const strokeWidth = ref(10);
const strokeColor = ref("red");
</script>

<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: G<PERSON>er<PERSON>
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 16:08:54
-->

# bc-source-tile-wms

提供平铺图像的 WMS 服务器的来源。

<script lang="ts" setup>
import TileWMSDemo from "@demos/TileWMSDemo.vue"
</script>

<ClientOnly>
<TileWMSDemo />
</ClientOnly>

## 用法

::: code-group

<<< ../../../../src/demos/TileWMSDemo.vue

:::

## 属性

### Props 继承自 OpenLayers

属性直接从 OpenLayers 传递。它们的类型和默认值可以在[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_TileWMS-TileWMS.html)中查看。只有部分属性是由 Vue / HTML 的保留关键字引起的。下面的部分描述了这种偏离的 props。

### 偏离属性

以下附加属性可用于设置特定的`params`.

#### layers

设置/覆盖该`params.LAYERS`属性。

- **类型**: `string` | `unknown[]`

#### styles

设置/覆盖该`params.STYLES`属性。

- **类型**: `string` | `unknown[]`

## 事件

您可以从底层源访问所有事件。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_TileWMS-TileWMS.html)以查看将触发的可用事件。

```html
<bc-source-tile-wms :url="imgUrl" @error="handleEvent" />
```

## 方法

您可以从底层源访问所有方法。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_TileWMS-TileWMS.html)以查看可用的方法。

要访问源代码，您可以使用`ref()`如下所示：

```vue
<template>
  <!-- ... -->
  <bc-source-tile-wms :url="url" ref="sourceRef" />
  <!-- ... -->
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import type TileWMS from "ol/source/TileWMS";

const sourceRef = ref<{ source: TileWMS }>(null);

onMounted(() => {
  const source: TileWMS = sourceRef.value?.source;
  // call your method on `source`
});
</script>
```

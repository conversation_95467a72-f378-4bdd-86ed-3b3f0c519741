# bc-vector-layer

`bc-vector-layer` 可以渲染来自各种后端服务的矢量。它应该与 `bc-source-vector` 组件一起使用。

<script lang="ts" setup>
import VectorSourceDemo1 from "@demos/VectorSourceDemo1.vue"
</script>

<ClientOnly>
<VectorSourceDemo1 />
</ClientOnly>

## 用法

下面的示例展示了如何使用 `bc-vector-layer` 和 `bc-source-vector` 从远程后端渲染一些矢量特征。

只需提供 `url` 值和格式 `GeoJSON` 即可加载功能

::: code-group

<<< ../../../../src/demos/VectorSourceDemo1.vue

:::

## 属性

### className

- **类型**: `string`
- **默认值**: `bc-layer`

要设置为图层元素的 CSS 类名称。

### opacity

- **类型**: `number `
- **默认值**: `1`

不透明度 (0, 1)。

### visible

- **类型**: `boolean`
- **默认值**: `true`

能见度。

### extent

- **类型**: `Array`

图层渲染的边界范围。该图层不会在此范围之外进行渲染。

### zIndex

- **类型**: `number`

图层渲染的 `z-index`。在渲染时，图层将首先按 Z 索引排序，然后按位置排序。

### minResolution

- **类型**: `number`

该图层可见的最小分辨率（含）。

### maxResolution

- **类型**: `number`

最大分辨率（独占），低于该分辨率该图层将可见。

### minZoom

- **类型**: `number`

最小视图缩放级别（不包括），高于该级别该图层将可见。

### maxZoom

- **类型**: `number`

该图层可见的最大视图缩放级别（含）。

### renderBuffer

- **类型**: `number`
- **默认值**: `100`
  渲染器从矢量源获取要素以进行渲染或命中检测时使用的视口范围周围的缓冲区（以像素为单位）。推荐值：最大符号、线宽或标签的尺寸。

### updateWhileAnimating

- **类型**: `Boolean`
- **默认值**: `false`
  设置为 `true` 时，将在动画期间重新创建特征批次。这意味着不会显示任何矢量被剪切，但该设置会对大量矢量数据的性能产生影响。当设置为 `false` 时，当没有动画处于活动状态时将重新创建批次。

### updateWhileInteracting

- **类型**: `Boolean`
- **默认值**: `false`
  设置为 `true` 时，将在交互过程中重新创建功能批次。另请参见 updateWhileAnimating。

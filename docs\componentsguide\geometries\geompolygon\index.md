<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 11:49:48
-->

# bc-geom-polygon

`bc-geom-polygon` 可以在 `bc-feature` 内部使用以在地图上绘制单个多边形。多边形中可能包含孔。多边形由正好一个 LinearRing 作为其周长，以及任意数量的附加 LinearRing 来定义，这些 LinearRing 表示切出的孔。检查使用部分以获取更多信息。

<script lang="ts" setup>
import PolygonDemo from "@demos/PolygonDemo.vue"
</script>
<ClientOnly>
<PolygonDemo />
</ClientOnly>

## 用法

::: code-group

<<< ../../../../src/demos/PolygonDemo.vue

:::

## 属性

### coordinates

- **类型**: `number[][][]`
  在地图投影中具有坐标的多边形。

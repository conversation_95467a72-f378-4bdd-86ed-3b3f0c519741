<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 11:25:43
-->

# bc-animation-shake

> 要素摇动动画

<script lang="ts" setup>
import ShakeAnimation from "@demos/ShakeAnimation.vue"
</script>

<ClientOnly>
<ShakeAnimation />
</ClientOnly>

::: code-group

<<< ../../../../src/demos/ShakeAnimation.vue

:::

## 属性

### duration

- **类型**: `Number`
- **默认值**: `1000`

动画持续时间（以毫秒为单位），默认 1000

### revers

- **类型**: `Boolean`
- **默认值**: `false`

反转动画方向

### repeat

- **类型**: `Number`
- **默认值**: `0`

动画重复次数，默认 0

### hiddenStyle

- **类型**: `ol.style.Style`

播放动画时显示功能的样式，用于使播放动画时可选择该功能，默认该功能将在播放时隐藏（且不可选择）

### fade

- **类型**: `function`
- **默认值**: `none`

用于淡入功能的缓动函数，默认无

### easing

- **类型**: `function`
- **默认值**: `0`

动画的缓动函数，默认 `ol.easing.linear`

### bounce

- **类型**: `Number`
- **默认值**: `6`

边界数，默认 6

### amplitude

- **类型**: `Number`
- **默认值**: `40`

动画幅度，默认 40

### horizontal

- **类型**: `Boolean`
- **默认值**: `false`

水平摇动默认 false（垂直）

<template>
  <bc-map
    :loadTilesWhileAnimating="true"
    :loadTilesWhileInteracting="true"
    style="height: 400px"
  >
    <bc-view
      ref="view"
      :center="center"
      :rotation="rotation"
      :zoom="zoom"
      :projection="projection"
    />

    <bc-tile-layer>
      <bc-source-osm />
    </bc-tile-layer>

    <bc-interaction-select
      @select="featureSelected"
      :condition="selectCondition"
      :filter="selectInteactionFilter"
    >
      <bc-style>
        <bc-style-stroke color="green" :width="10"></bc-style-stroke>
        <bc-style-fill color="rgba(255,255,255,0.5)"></bc-style-fill>
        <bc-style-icon :src="markerIcon" :scale="0.05"></bc-style-icon>
      </bc-style>
    </bc-interaction-select>

    <bc-vector-layer>
      <bc-source-vector
        ref="cities"
        url="https://raw.githubusercontent.com/alpers/Turkey-Maps-GeoJSON/master/tr-cities-airports.json"
        :format="geoJson"
        :projection="projection"
      >
      </bc-source-vector>

      <bc-style>
        <bc-style-stroke color="red" :width="2"></bc-style-stroke>
        <bc-style-fill color="rgba(255,255,255,0.1)"></bc-style-fill>
        <bc-style-circle :radius="7">
          <bc-style-fill color="blue"></bc-style-fill>
        </bc-style-circle>
      </bc-style>
    </bc-vector-layer>
  </bc-map>
</template>

<script lang="ts" setup>
import markerIcon from "@/assets/marker.png";
import { ref, inject } from "vue";

const center = ref([40, 40]);
const projection = ref("EPSG:4326");
const zoom = ref(8);
const rotation = ref(0);

const format = inject("ol-format");

const geoJson = new format.GeoJSON();

const selectConditions = inject("ol-selectconditions");

const selectCondition = selectConditions.pointerMove;

const featureSelected = (event) => {
  console.log(event);
};

const selectInteactionFilter = (feature) => {
  return feature.values_.name != undefined;
};
</script>

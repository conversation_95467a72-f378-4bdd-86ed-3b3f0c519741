<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: G<PERSON>er<PERSON>
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 11:52:51
-->

# bc-geom-multi-polygon

`bc-geom-multi-polygon` 可以在 `bc-feature` 内部使用，以在地图上一次绘制多个多边形。

<script lang="ts" setup>
import MultiPolygon from "@demos/MultiPolygon.vue"
</script>
<ClientOnly>
<MultiPolygon />
</ClientOnly>

## 用法

::: code-group

<<< ../../../../src/demos/MultiPolygon.vue

:::

## 属性

### coordinates

- **类型**: `number[][][][]`
  具有地图投影坐标的多边形数组。

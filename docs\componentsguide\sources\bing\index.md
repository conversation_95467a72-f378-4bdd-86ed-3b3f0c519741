<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 15:35:17
-->

# bc-source-bingmaps

> [Bing Maps API](https://www.bing.com/maps)的图层源

`bc-source-bingmaps`添加了显示 Bing 地图中的图块数据的功能。要使用此源，您应该在 https://www.bingmapsportal.com 获取**API 密钥**。

<script lang="ts" setup>
import BingMapsDemo from "@demos/BingMapsDemo.vue"
</script>

<ClientOnly>
<BingMapsDemo />
</ClientOnly>

## 用法

`bc-source-bingmaps`使用示例

::: code-group

<<< ../../../../src/demos/BingMapsDemo.vue

:::

## 属性

### Props 继承自 OpenLayers

属性直接从 OpenLayers 传递。它们的类型和默认值可以在[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_BingMaps-BingMaps.html)中查看。只有部分属性是由 Vue / HTML 的保留关键字引起的。下面的部分描述了这种偏离的 props。

### 偏离属性

### `key` ➡ `apiKey`

OpenLayers BingMaps 中的属性 key 被公开为`apiKey`, Vue`key`中保留的属性，并且不能用作组件属性。请参阅[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_BingMaps-BingMaps.html)中的`key`属性，但将其传递为`apiKey`

```html
<bc-source-bingmaps apiKey="YOUR_API_KEY" />
```

## 事件

您可以从底层源访问所有事件。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_BingMaps-BingMaps.html)以查看将触发的可用事件。

```html
<bc-source-bingmaps apiKey="YOUR_API_KEY" @error="handleEvent" />
```

## 方法

您可以从底层源访问所有方法。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_BingMaps-BingMaps.html)以查看可用的方法。

要访问源代码，您可以使用`ref()`如下所示：

```vue
<template>
  <!-- ... -->
  <bc-source-bingmaps apiKey="YOUR_API_KEY" ref="sourceRef" />
  <!-- ... -->
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import type BingMaps from "ol/source/BingMaps";

const sourceRef = ref<{ source: BingMaps }>(null);

onMounted(() => {
  const source: BingMaps = sourceRef.value?.source;
  // call your method on `source`
});
</script>
```

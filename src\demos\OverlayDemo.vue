<template>
  <bc-map style="height: 400px">
    <bc-view
      ref="view"
      :center="center"
      :zoom="zoom"
      :projection="projection"
    />

    <bc-tile-layer>
      <bc-source-osm />
    </bc-tile-layer>

    <bc-overlay
      :position="[item + 37.9, 40.1]"
      v-for="item in list"
      :key="item"
    >
      <div class="overlay-content">
        {{ item }}
      </div>
    </bc-overlay>
  </bc-map>
</template>

<script lang="ts" setup>
import { ref } from "vue";

const center = ref([40, 40]);
const projection = ref("EPSG:4326");
const zoom = ref(8);
const list = ref([2, 1, 3, 5, -1]);
</script>

<style>
.overlay-content {
  background: #efefef;
  box-shadow: 0 5px 10px rgb(2 2 2 / 20%);
  padding: 10px 20px;
  font-size: 16px;
}
</style>

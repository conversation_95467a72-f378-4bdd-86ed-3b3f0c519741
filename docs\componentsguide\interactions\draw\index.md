<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 13:53:20
-->

# bc-interaction-draw

> 用于绘制特征几何图形的交互。

`bc-interaction-draw` 处理地图上的点击事件，使绘制几何图形变得更加容易。

<script lang="ts" setup>
import DrawDemo from "@demos/DrawDemo.vue"
</script>

<ClientOnly>
<DrawDemo/>
</ClientOnly>

## 用法

::: code-group

<<< ../../../../src/demos/DrawDemo.vue

:::

## 属性

### type

- **类型**: `String`

### clickTolerance

- **类型**: `Number`
- **默认值**: `6`

### dragVertexDelay

- **类型**: `Number`
- **默认值**: `500`

### snapTolerance

- **类型**: `Number`
- **默认值**: `12`

### stopClick

- **类型**: `Boolean`
- **默认值**: `false`

### maxPoints

- **类型**: `Number`

### minPoints

- **类型**: `Number`

### finishCondition

- **类型**: `Function`

### geometryFunction

- **类型**: `Function`

### geometryName

- **类型**: `String`

### condition

- **类型**: `Function`

### freehand

- **类型**: `Boolean`
- **默认值**: `false`

### freehandCondition

- **类型**: `Function`

### wrapX

- **类型**: `Boolean`
- **默认值**: `false`

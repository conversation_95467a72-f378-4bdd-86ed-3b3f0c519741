{"name": "bcgis-ol", "version": "1.0.6", "description": "OpenLayers Wrapper for Vue3", "repository": {"type": "git", "url": "https://gitlab-bcht.vip.cpolar.cn/sdk/bcgis-sdk-ol"}, "homepage": "https://gitlab-bcht.vip.cpolar.cn/sdk/bcgis-sdk-ol", "bugs": {"url": "https://gitlab-bcht.vip.cpolar.cn/sdk/bcgis-sdk-ol/issues"}, "keywords": ["front-end", "web", "vue3", "openlayers", "bcgis-ol"], "author": "GISerZ", "license": "MIT", "private": false, "main": "./dist/bcgis-ol.umd.js", "module": "./dist/bcgis-ol.es.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/bcgis-ol.es.js", "require": "./dist/bcgis-ol.umd.js", "types": "./dist/index.d.ts"}, "./dist/bcgis-ol.css": {"import": "./dist/styles.css", "require": "./dist/styles.css"}, "./styles.css": {"import": "./dist/styles.css", "require": "./dist/styles.css"}}, "files": ["dist"], "scripts": {"dev": "vite", "build": "vite build", "serve": "vite preview", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs", "docs:update-requirements": "node scripts/insert-required-versions.mjs README.md docs/get-started.md", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --ignore-path .gitignore", "lint-fix": "npm run lint -- --fix", "prettier": "prettier --write .", "prepare": "husky install", "release": "release-it", "test": "vitest", "coverage": "vitest run --coverage"}, "dependencies": {"file-saver": "^2.0.5", "jspdf": "^2.5.1", "proj4": "^2.9.0"}, "peerDependencies": {"ol": "^7.4.0", "ol-contextmenu": "^5.2.1", "ol-ext": "^4.0.8", "vue": "^3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "devDependencies": {"@babel/eslint-parser": "^7.22.9", "@release-it/conventional-changelog": "^7.0.0", "@types/file-saver": "^2.0.5", "@types/node": "^18.17.1", "@types/ol-ext": "npm:@siedlerchr/types-ol-ext@^3.2.0", "@types/proj4": "^2.5.2", "@vitejs/plugin-vue": "^4.2.3", "@vitest/coverage-v8": "^0.33.0", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/test-utils": "^2.4.1", "@vue/tsconfig": "^0.4.0", "eslint": "^8.45.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.15.1", "get-repository-url": "^2.0.0", "husky": "^8.0.3", "jsdom": "^22.1.0", "prettier": "^2.8.8", "pretty-quick": "^3.1.3", "release-it": "^16.1.3", "typescript": "~5.1.6", "vite": "^4.4.7", "vite-plugin-dts": "^3.3.1", "vitepress": "^1.0.0-rc.4", "vitest": "^0.33.0", "vue": "^3.3.4"}}
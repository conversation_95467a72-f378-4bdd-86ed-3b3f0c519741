<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: <PERSON><PERSON>er<PERSON>
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 13:52:18
-->

# bc-interaction-select

> 选择矢量特征的交互

<script lang="ts" setup>
import SelectDemo from "@demos/SelectDemo.vue"
</script>

<ClientOnly>
<SelectDemo/>
</ClientOnly>

## 用法

::: code-group

<<< ../../../../src/demos/SelectDemo.vue

:::

## 属性

### multi

- **类型**: `String`

### features

- **类型**: `Collection`

### filter

- **类型**: `Function`

### condition

- **类型**: `Function`

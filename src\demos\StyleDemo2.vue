<template>
  <bc-map
    :loadTilesWhileAnimating="true"
    :loadTilesWhileInteracting="true"
    style="height: 400px"
  >
    <bc-view
      ref="view"
      :center="center"
      :rotation="rotation"
      :zoom="zoom"
      :projection="projection"
    />

    <bc-tile-layer>
      <bc-source-osm />
    </bc-tile-layer>

    <bc-vector-layer>
      <bc-source-vector :url="url" :format="geoJson"> </bc-source-vector>
      <bc-style>
        <bc-style-stroke color="red" :width="2"></bc-style-stroke>
        <bc-style-fill color="rgba(255,255,255,0.1)"></bc-style-fill>
      </bc-style>
    </bc-vector-layer>
  </bc-map>
</template>

<script lang="ts" setup>
import { ref, inject } from "vue";

const center = ref([0, 0]);
const projection = ref("EPSG:4326");
const zoom = ref(3);
const rotation = ref(0);

const url = ref("https://openlayers.org/data/vector/ecoregions.json");
const format = inject("ol-format");
const geoJson = new format.GeoJSON();
</script>

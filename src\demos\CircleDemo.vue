<template>
  <bc-map
    :loadTilesWhileAnimating="true"
    :loadTilesWhileInteracting="true"
    style="height: 400px"
  >
    <bc-view
      ref="view"
      :center="center"
      :rotation="rotation"
      :zoom="zoom"
      :projection="projection"
    />

    <bc-tile-layer>
      <bc-source-osm />
    </bc-tile-layer>

    <bc-vector-layer>
      <bc-source-vector>
        <bc-feature>
          <bc-geom-circle :center="[30, 40]" :radius="0.2"></bc-geom-circle>
          <bc-style>
            <bc-style-stroke color="red" :width="3"></bc-style-stroke>
            <bc-style-fill color="rgba(255,200,0,0.2)"></bc-style-fill>
          </bc-style>
        </bc-feature>
      </bc-source-vector>
    </bc-vector-layer>
  </bc-map>
</template>

<script lang="ts" setup>
import { ref } from "vue";

const center = ref([30, 40]);
const projection = ref("EPSG:4326");
const zoom = ref(10);
const rotation = ref(0);
</script>

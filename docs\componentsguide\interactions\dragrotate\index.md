<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: G<PERSON>er<PERSON>
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 13:50:49
-->

# bc-interaction-dragrotate

> 通过在地图上单击并拖动来旋转地图，通常与将`ol/events/condition`其限制为按住`alt`和`shift`键时结合使用。

<script lang="ts" setup>
import DragRotateDemo from "@demos/DragRotateDemo.vue"
</script>

<ClientOnly>
<DragRotateDemo/>
</ClientOnly>

## 用法

::: code-group

<<< ../../../../src/demos/DragRotateDemo.vue

:::

## 属性

### condition

- **类型**: `Function`

### duration

- **类型**: `Number`
- **默认值**: `250`

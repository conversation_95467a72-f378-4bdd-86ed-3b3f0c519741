# bc-layerswitcherimage-control

> 用于在图层之间切换的控件。

要查看正确的缩略图，您可能必须设置该属性`imagerySet`（例如与[BingMaps](../../sources/bing/)一起使用）。

## Demo

请参阅[所有地图控件的演示页面](../index.md)

## 属性

### selection

单击标题时启用图层选择

- **类型**: `boolean`

### displayInLayerSwitcher

如果该图层显示在切换器中，则接受图层并返回布尔值的函数，默认测试 `displayInLayerSwitcher` 图层属性

- **类型**: `function`

### show_progress

在图块层上显示进度条

- **类型**: `boolean`
- **默认值**: `false`

### mouseover

鼠标悬停时显示面板

- **类型**: `boolean`
- **默认值**: `false`

### reordering

允许图层重新排序

- **类型**: `boolean`
- **默认值**: `true`

### trash

添加垃圾桶按钮以删除图层

- **类型**: `boolean`
- **默认值**: `false`

### oninfo

单击信息按钮时的回调，如果没有，则不显示任何信息按钮 不建议使用：使用 `on(info)` 代替

- **类型**: `function`

### extent

添加范围按钮以缩放至图层范围

- **类型**: `boolean`

### onextent

单击范围时的回调，默认使视图适合范围

- **类型**: `function`

### drawDelay

重绘图层的延迟毫秒（有助于防止操作图层时闪烁）

- **类型**: `number`

### collapsed

在开始时折叠图层切换器

- **类型**: `boolean`
- **默认值**: `true`

### layerGroup

在切换器中显示的图层组，默认显示地图的所有图层

- **类型**: `ol.layerGroup`

### noScroll

防止手柄滚动

- **类型**: `boolean`
- **默认值**: `false`

### onchangeCheck

单击复选框时的可选回调，您可以在选中/取消选中图层后调用此方法进行操作。控制切换器的图层属性。

- `allwaysOnTop` {`boolean`} `true` 表示在重新排序时强制图层保持在其他图层之上，默认`false`
- `displayInLayerSwitcher` {`boolean`} 在切换器中显示图层，默认`true`
- `noSwitcherDelete` {`boolean`} 防止图层删除（带垃圾箱选项 = `true`），默认`false`

- **类型**: `function`

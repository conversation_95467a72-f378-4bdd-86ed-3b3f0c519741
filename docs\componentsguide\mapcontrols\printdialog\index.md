# bc-printdialog-control

> 用于打印当前地图画布内容的打印对话框。

## Demo

请参阅[所有地图控件的演示页面](../index.md)

## 属性

The properties are reflected 1:1 from [ol-ext/control/PrintDialog](https://viglino.github.io/ol-ext/doc/doc-pages/ol.control.PrintDialog.html).
这些属性以[ol-ext/control/PrintDialog](https://viglino.github.io/ol-ext/doc/doc-pages/ol.control.PrintDialog.html) 1:1 的方式反映。

### lang

控制语言，默认 en

- **类型**: `String`
- **默认值**: `en`

### title

按钮标题

- **类型**: `String`

### className

控制类

- **类型**: `String`

### imageType

表示图像格式的字符串，默认`image/jpeg`

- **类型**: `String`
- **默认值**: `image/jpeg`

### quality

0 到 1 之间的数字，指示用于有损压缩的图像格式（例如 `image/jpeg` 和 `image/webp`）的图像质量。

- **类型**: `Number`

### orientation

页面方向（`'landscape'`/ `'portrait'`），默认访客最好

- **类型**: `String`

### immediate

即使渲染未完成也强制打印，默认 `false`

- **类型**: `Boolean`
- **默认值**: `false`

### openWindow

打印时在新窗口中打开文件

- **类型**: `Boolean`
- **默认值**: `false`

### copy

添加复制选择选项

- **类型**: `Boolean`
- **默认值**: `true`

### print

添加打印选择选项

- **类型**: `Boolean`
- **默认值**: `true`

### pdf

添加 pdf 选择选项

- **类型**: `Boolean`
- **默认值**: `true`

### saveAs

将图像保存为 blob 的函数

- **类型**: `Function`

### jsPDF

[jsPDF](https://www.npmjs.com/package/jspdf)对象将地图保存为 pdf

- **类型**: `Object`

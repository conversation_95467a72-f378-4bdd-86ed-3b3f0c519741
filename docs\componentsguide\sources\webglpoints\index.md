<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 16:20:22
-->

# bc-source-webglpoints

> 使用 WebGL 渲染点的矢量源

请注意，您不能`bc-style`在此处使用相关样式组件作为子组件。您必须直接通过组件 style 上的 prop 应用样式`bc-webgl-points-layer`。

<script lang="ts" setup>
import WebglPointsLayerDemo from "@demos/WebglPointsLayerDemo.vue"
import WebglPointsSourceDemo from "@demos/WebglPointsSourceDemo.vue"
</script>

## 用法

### with `url` prop

<ClientOnly>
<WebglPointsLayerDemo />
</ClientOnly>

::: code-group

<<< ../../../../src/demos/WebglPointsLayerDemo.vue

:::

### `bc-feature` child component

<ClientOnly>
<WebglPointsSourceDemo />
</ClientOnly>

::: code-group

<<< ../../../../src/demos/WebglPointsSourceDemo.vue

:::

## 属性

### Props 继承自 OpenLayers

属性直接从 OpenLayers 传递。它们的类型和默认值可以在[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_Vector-VectorSource.html)中查看。只有部分属性是由 Vue / HTML 的保留关键字引起的。下面的部分描述了这种偏离的 props。

### 偏离属性

无

## 事件

您可以从底层源访问所有事件。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_Vector-VectorSource.html)以查看将触发的可用事件。

```html
<bc-source-webglpoints :url="url" @error="handleEvent" />
```

## 方法

您可以从底层源访问所有方法。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_Vector-VectorSource.html)以查看可用的方法。

要访问源代码，您可以使用`ref()`如下所示：

```vue
<template>
  <!-- ... -->
  <bc-source-webglpoints :url="url" ref="sourceRef" />
  <!-- ... -->
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import type VectorSource from "ol/source/vector";

const sourceRef = ref<{ source: VectorSource }>(null);

onMounted(() => {
  const source: VectorSource = sourceRef.value?.source;
  // call your method on `source`
});
</script>
```

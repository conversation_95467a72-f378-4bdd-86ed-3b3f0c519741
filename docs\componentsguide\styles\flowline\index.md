<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: G<PERSON>er<PERSON>
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 14:02:33
-->

# bc-style-flowline

> 矢量层中流线的样式

<script lang="ts" setup>
import FlowLineDemo from "@demos/FlowLineDemo.vue"
</script>
<ClientOnly>
<FlowLineDemo />
</ClientOnly>

## 用法

::: code-group

<<< ../../../../src/demos/FlowLineDemo.vue

:::

## 属性

### Props 继承自 OpenLayers

属性直接从 OpenLayers 传递。它们的类型和默认值可以在[ OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_OSM-OSM.html)中查看。只有部分属性是由 Vue / HTML 的保留关键字引起的。下面的部分描述了这种偏离的 props。

### 偏离属性

### `overrideStyleFunction`

- **类型**: `OverrideStyleFunction`

更改/覆盖应用的样式。该函数有三个参数：

1. `feature: FeatureLike`: 与样式相关的功能。
2. `currentStyle: Style`: 当前应用的样式（您可以在此处覆盖它）
3. `resolution?: number`: 代表视图分辨率的数字

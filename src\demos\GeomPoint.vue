<template>
  <button
    @click="() => (coordinate = coordinate.map((a) => a + 0.01))"
    type="button"
  >
    change coordinates
  </button>
  <bc-map
    :loadTilesWhileAnimating="true"
    :loadTilesWhileInteracting="true"
    style="height: 400px"
  >
    <bc-view
      ref="view"
      :center="center"
      :rotation="rotation"
      :zoom="zoom"
      :projection="projection"
    />

    <bc-tile-layer>
      <bc-source-osm />
    </bc-tile-layer>

    <bc-vector-layer>
      <bc-source-vector>
        <bc-feature>
          <bc-geom-point :coordinates="coordinate"></bc-geom-point>
          <bc-style>
            <bc-style-circle :radius="radius">
              <bc-style-fill :color="fillColor"></bc-style-fill>
              <bc-style-stroke
                :color="strokeColor"
                :width="strokeWidth"
              ></bc-style-stroke>
            </bc-style-circle>
          </bc-style>
        </bc-feature>
      </bc-source-vector>
    </bc-vector-layer>
  </bc-map>
</template>

<script lang="ts" setup>
import { ref } from "vue";

const center = ref([40, 40]);
const projection = ref("EPSG:4326");
const zoom = ref(8);
const rotation = ref(0);
const radius = ref(40);
const strokeWidth = ref(10);
const strokeColor = ref("red");
const fillColor = ref("white");
const coordinate = ref([40, 40]);
</script>

<style scoped>
button {
  border: 1px solid black;
  margin: 0.5rem 0;
  padding: 0.5rem;
}
button:hover,
button:focus {
  background-color: lightgray;
}
</style>

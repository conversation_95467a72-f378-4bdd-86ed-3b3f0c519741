<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 11:47:04
-->

# bc-geom-line-string

`bc-geom-line-string` 可以在 `bc-feature` 内部使用以在地图上绘制线条。

<script lang="ts" setup>
import LineString from "@demos/LineString.vue"
</script>
<ClientOnly>
<LineString />
</ClientOnly>

## 用法

::: code-group

<<< ../../../../src/demos/LineString.vue

:::

## 属性

### coordinates

- **类型**: `number[][]`
  由 geojson 规范指定的坐标对数组，以地图投影为单位。

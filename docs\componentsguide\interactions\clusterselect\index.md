<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 13:47:16
-->

# bc-interaction-clusterselect

> 选择向量簇特征的交互

交互组件封装了[著名的 `ol-ext` OpenLayers 扩展](http://viglino.github.io/ol-ext/)`SelectCluster`的交互。

<script lang="ts" setup>
import AnimatedClusterDemo from "@demos/AnimatedClusterDemo.vue"
</script>

<ClientOnly>
<AnimatedClusterDemo />
</ClientOnly>

## 用法

::: code-group

<<< ../../../../src/demos/AnimatedClusterDemo.vue

:::

## 属性

### Props 继承自 OpenLayers

属性是直接传递的`ol-ext`。它们的类型和默认值可以在[ OpenLayers 官方文档](http://viglino.github.io/ol-ext/doc/doc-pages/ol.interaction.SelectCluster.html)中查看。只有部分属性是由 Vue / HTML 的保留关键字引起的。下面的部分描述了这种偏离的 props。

### 偏离特性

没有任何。

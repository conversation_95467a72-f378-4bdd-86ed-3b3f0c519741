<template>
  <bc-map
    :loadTilesWhileAnimating="true"
    :loadTilesWhileInteracting="true"
    style="height: 400px"
  >
    <bc-view ref="view" :center="center" :zoom="zoom" />
    <bc-tile-layer>
      <bc-source-stamen layer="watercolor" />
    </bc-tile-layer>
    <bc-tile-layer>
      <bc-source-stamen layer="terrain-labels" />
    </bc-tile-layer>
  </bc-map>
</template>

<script setup lang="ts">
import { fromLonLat } from "ol/proj";
import { ref } from "vue";

const zoom = ref(12);
const center = ref(fromLonLat([-122.416667, 37.783333]));
</script>

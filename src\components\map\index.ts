/*
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 09:34:19
 */
import type { App } from "vue";
import BcFeature from "./BcFeature.vue";
import BcGeoLocation from "./BcGeoLocation.vue";
import BcMap from "./BcMap.vue";
import BcOverlay from "./BcOverlay.vue";
import BcProjectionRegister from "./BcProjectionRegister.vue";
import BcView from "./BcView.vue";

let installed = false;

function install(app: App) {
  if (installed) return;
  installed = true;

  app.component("bc-feature", BcFeature);
  app.component("bc-geolocation", BcGeoLocation);
  app.component("bc-map", BcMap);
  app.component("bc-overlay", BcOverlay);
  app.component("bc-projection-register", BcProjectionRegister);
  app.component("bc-view", BcView);
}

export default install;

export {
  install,
  BcFeature,
  BcGeoLocation,
  BcMap,
  BcOverlay,
  BcProjectionRegister,
  BcView,
};

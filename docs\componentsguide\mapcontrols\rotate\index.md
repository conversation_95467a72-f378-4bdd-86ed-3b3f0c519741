<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 14:30:37
-->

# bc-rotate-control

> OpenLayers 的旋转控件。

## Demo

请参阅[所有地图控件的演示页面](../index.md)

## 属性

### className

- **类型**: `String`
- **默认值**: `bc-rotate`

### label

- **类型**: `String`
- **默认值**: `⇧`

### tipLabel

- **类型**: `String`
- **默认值**: `Reset rotation`

### compassClassName

- **类型**: `String`
- **默认值**: `bc-compass`

### duration

- **类型**: `Number`
- **默认值**: `250`

### autoHide

- **类型**: `Boolean`
- **默认值**: `false`

### render

- **类型**: `Function`

### resetNorth

- **类型**: `Function`

### target

- **类型**: `HTMLElement`

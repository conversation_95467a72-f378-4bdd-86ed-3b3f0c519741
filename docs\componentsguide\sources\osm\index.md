<!--
 * @Description:
 * @Date: 2023-08-17 14:38:18
 * @Author: G<PERSON>er<PERSON>
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 15:53:51
-->

# bc-source-osm

源层已准备好加载 OpenStreetMaps 图块

<script lang="ts" setup>
import MapDemo from "@demos/MapDemo.vue"
</script>

<ClientOnly>
<MapDemo />
</ClientOnly>

## 用法

加载一个简单的 OSM 基础层。

::: code-group

<<< ../../../../src/demos/MapDemo.vue

:::

## 属性

### Props 继承自 OpenLayers

属性直接从 OpenLayers 传递。它们的类型和默认值可以在[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_OSM-OSM.html)中查看。只有部分属性是由 Vue / HTML 的保留关键字引起的。下面的部分描述了这种偏离的 props。

### 偏离属性

无

## 事件

您可以从底层源访问所有事件。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_OSM-OSM.html)以查看将触发的可用事件。

```html
<bc-source-osm :url="url" @error="handleEvent" />
```

## 方法

您可以从底层源访问所有方法。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_OSM-OSM.html)以查看可用的方法。

要访问源代码，您可以使用`ref()`如下所示：

```vue
<template>
  <!-- ... -->
  <bc-source-osm :url="url" ref="sourceRef" />
  <!-- ... -->
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import type OSM from "ol/source/OSM";

const sourceRef = ref<{ source: OSM }>(null);

onMounted(() => {
  const source: OSM = sourceRef.value?.source;
  // call your method on `source`
});
</script>
```

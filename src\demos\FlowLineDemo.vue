<template>
  <bc-map ref="map" style="height: 400px">
    <bc-view
      ref="view"
      :center="center"
      :zoom="zoom"
      :projection="projection"
    />

    <bc-tile-layer title="OSM">
      <bc-source-osm />
    </bc-tile-layer>

    <bc-vector-layer>
      <bc-source-vector>
        <bc-feature ref="animationPath">
          <bc-geom-line-string :coordinates="path"></bc-geom-line-string>
          <bc-style-flowline
            color="red"
            color2="yellow"
            :width="10"
            :width2="10"
            :arrow="1"
          />
        </bc-feature>
        <bc-animation-path
          v-if="animationPath"
          :path="animationPath?.feature"
          :duration="4000"
          :repeat="10"
        >
          <bc-feature>
            <bc-geom-point :coordinates="path[0]"></bc-geom-point>
            <bc-style>
              <bc-style-circle :radius="10">
                <bc-style-fill color="blue"></bc-style-fill>
                <bc-style-stroke color="blue" :width="2"></bc-style-stroke>
              </bc-style-circle>
            </bc-style>
          </bc-feature>
        </bc-animation-path>
      </bc-source-vector>
    </bc-vector-layer>
  </bc-map>
</template>

<script setup lang="ts">
import { ref } from "vue";
import type AnimationPath from "ol-ext/featureanimation/Path";

const center = ref([29, 44.5]);
const projection = ref("EPSG:4326");
const zoom = ref(6);
const animationPath = ref<{ feature: AnimationPath } | null>(null);

const path = ref([
  [25.6064453125, 44.73302734375001],
  [27.759765625, 44.75500000000001],
  [28.287109375, 43.32677734375001],
  [30.55029296875, 46.40294921875001],
  [31.69287109375, 43.04113281250001],
]);
</script>

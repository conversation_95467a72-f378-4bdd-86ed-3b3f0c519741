/*
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: <PERSON><PERSON>er<PERSON>
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 10:01:06
 */
import type { App } from "vue";
import BcClusterSelectInteraction from "./BcClusterSelectInteraction.vue";
import BcDragRotateInteraction from "./BcDragRotateInteraction.vue";
import BcDragRotateZoomInteraction from "./BcDragRotateZoomInteraction.vue";
import BcSelectInteraction from "./BcSelectInteraction.vue";
import BcDrawInteraction from "./BcDrawInteraction.vue";
import BcModifyInteraction from "./BcModifyInteraction.vue";
import BcSnapInteraction from "./BcSnapInteraction.vue";
import BcTransformInteraction from "./BcTransformInteraction.vue";

let installed = false;

function install(app: App) {
  if (installed) return;
  installed = true;

  app.component("bc-interaction-clusterselect", BcClusterSelectInteraction);
  app.component("bc-interaction-dragrotate", BcDragRotateInteraction);
  app.component("bc-interaction-dragrotatezoom", BcDragRotateZoomInteraction);
  app.component("bc-interaction-select", BcSelectInteraction);
  app.component("bc-interaction-draw", BcDrawInteraction);
  app.component("bc-interaction-modify", BcModifyInteraction);
  app.component("bc-interaction-snap", BcSnapInteraction);
  app.component("bc-interaction-transform", BcTransformInteraction);
}

export default install;

export {
  install,
  BcClusterSelectInteraction,
  BcDragRotateInteraction,
  BcDragRotateZoomInteraction,
  BcSelectInteraction,
  BcDrawInteraction,
  BcModifyInteraction,
  BcSnapInteraction,
  BcTransformInteraction,
};

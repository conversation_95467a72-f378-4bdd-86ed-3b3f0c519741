<template>
  <bc-map
    :loadTilesWhileAnimating="true"
    :loadTilesWhileInteracting="true"
    style="height: 400px"
  >
    <bc-view
      ref="view"
      :center="center"
      :rotation="rotation"
      :zoom="zoom"
      :projection="projection"
    />

    <bc-tile-layer>
      <bc-source-osm />
    </bc-tile-layer>

    <bc-vector-layer>
      <bc-source-vector>
        <bc-feature>
          <bc-geom-multi-polygon
            :coordinates="[
              [
                [
                  [-98.844959, 19.691586],
                  [-98.842749, 19.69098],
                  [-98.84217, 19.693122],
                  [-98.844358, 19.693667],
                  [-98.844959, 19.691586],
                ],
              ],
              [
                [
                  [-98.84777, 19.684212],
                  [-98.849079, 19.680596],
                  [-98.845453, 19.679585],
                  [-98.844466, 19.683384],
                  [-98.84777, 19.684212],
                ],
              ],
            ]"
          ></bc-geom-multi-polygon>
          <bc-style>
            <bc-style-stroke
              :color="strokeColor"
              :width="strokeWidth"
            ></bc-style-stroke>
          </bc-style>
        </bc-feature>
      </bc-source-vector>
    </bc-vector-layer>
  </bc-map>
</template>

<script lang="ts" setup>
import { ref } from "vue";

const center = ref([-98.8449, 19.6869]);
const projection = ref("EPSG:4326");
const zoom = ref(15);
const rotation = ref(0);
const strokeWidth = ref(10);
const strokeColor = ref("red");
</script>

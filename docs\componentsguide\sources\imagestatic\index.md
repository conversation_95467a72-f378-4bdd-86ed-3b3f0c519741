# bc-source-image-static

> 用于显示单个静态图像的图层源。

<script lang="ts" setup>
import ImageLayerDemo from "@demos/ImageLayerDemo.vue"
</script>

<ClientOnly>
<ImageLayerDemo />
</ClientOnly>

## 用法

下面的示例展示了如何使用 `bc-image-layer` 组件和 `bc-source-image-static` 在地图上渲染自定义图像。地图视图配置有自定义投影，可将图像坐标直接转换为地图坐标。

::: code-group

<<< ../../../../src/demos/ImageLayerDemo.vue

:::

## 属性

### Props 继承自 OpenLayers

属性直接从 OpenLayers 传递。它们的类型和默认值可以在[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_ImageStatic-Static.html)中查看。只有部分属性是由 Vue / HTML 的保留关键字引起的。下面的部分描述了这种偏离的 props。

### 偏离属性

无.

## 事件

您可以从底层源访问所有事件。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_ImageStatic-Static.html)以查看将触发的可用事件。

```html
<bc-source-image-static :url="imgUrl" @error="handleEvent" />
```

## 方法

您可以从底层源访问所有方法。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_ImageStatic-Static.html)以查看可用的方法。

要访问源代码，您可以使用`ref()`如下所示：

```vue
<template>
  <!-- ... -->
  <bc-source-image-static :url="url" ref="sourceRef" />
  <!-- ... -->
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import type ImageStatic from "ol/source/ImageStatic";

const sourceRef = ref<{ source: ImageStatic }>(null);

onMounted(() => {
  const source: ImageStatic = sourceRef.value?.source;
  // call your method on `source`
});
</script>
```

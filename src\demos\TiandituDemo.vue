<template>
  <bc-map
    :loadTilesWhileAnimating="true"
    :loadTilesWhileInteracting="true"
    style="height: 400px"
  >
    <bc-view
      ref="view"
      :center="center"
      :rotation="rotation"
      :zoom="zoom"
      :projection="projection"
    />

    <bc-tile-layer>
      <bc-source-tianditu
        layerType="vec"
        projection="EPSG:4326"
        tk="dbed7e0f96194affd82763e159de4c50"
        :hidpi="true"
      ></bc-source-tianditu>
    </bc-tile-layer>

    <bc-tile-layer>
      <bc-source-tianditu
        :isLabel="true"
        layerType="vec"
        projection="EPSG:4326"
        tk="dbed7e0f96194affd82763e159de4c50"
        :hidpi="true"
      ></bc-source-tianditu>
    </bc-tile-layer>
  </bc-map>
</template>

<script lang="ts" setup>
import { ref } from "vue";

const center = ref([116.41124529391394, 39.953530444730816]);
const projection = ref("EPSG:4326");
const zoom = ref(8);
const rotation = ref(0);
</script>

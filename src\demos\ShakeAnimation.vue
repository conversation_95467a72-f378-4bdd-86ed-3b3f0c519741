<template>
  <bc-map
    :loadTilesWhileAnimating="true"
    :loadTilesWhileInteracting="true"
    style="height: 700px"
  >
    <bc-view
      ref="view"
      :center="center"
      :rotation="rotation"
      :zoom="zoom"
      :projection="projection"
    />

    <bc-tile-layer>
      <bc-source-osm />
    </bc-tile-layer>

    <bc-vector-layer
      :updateWhileAnimating="true"
      :updateWhileInteracting="true"
    >
      <bc-source-vector ref="vectorsource">
        <bc-animation-shake :duration="2000" :repeat="5">
          <bc-feature v-for="index in 20" :key="index">
            <bc-geom-point
              :coordinates="[
                getRandomInRange(24, 45, 3),
                getRandomInRange(35, 41, 3),
              ]"
            ></bc-geom-point>

            <bc-style>
              <bc-style-icon :src="starIcon" :scale="0.1"></bc-style-icon>
            </bc-style>
          </bc-feature>
        </bc-animation-shake>
      </bc-source-vector>
    </bc-vector-layer>
  </bc-map>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import starIcon from "@/assets/star.png";

const center = ref([40, 40]);
const projection = ref("EPSG:4326");
const zoom = ref(6);
const rotation = ref(0);
const getRandomInRange = (from, to, fixed) => {
  return (Math.random() * (to - from) + from).toFixed(fixed) * 1;
};
</script>

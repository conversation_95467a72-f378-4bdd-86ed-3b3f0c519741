<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISer<PERSON>
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 14:17:42
-->

# bc-context-menu-control

> OpenLayers 的上下文菜单扩展。

右键单击地图以打开上下文菜单。

<script lang="ts" setup>
import ContextMenuDemo from "@demos/ContextMenuDemo.vue"
</script>
<ClientOnly>
<ContextMenuDemo />
</ClientOnly>

## 用法

添加上下文菜单到地图

::: code-group

<<< ../../../../src/demos/ContextMenuDemo.vue

:::

## 属性

### eventType

- **类型**: `String`
- **默认值**: `contextmenu`

监听事件类型（可以使用`click`、`dblclick`）

### defaultItems

- **类型**: `Boolean`
- **默认值**: `true`

是否启用默认项目（即：放大/缩小）

### width

- **类型**: `Number`
- **默认值**: `150`

菜单的宽度

### items

- **类型**: `Array`
- **默认值**: `[]`

对象|字符串数组

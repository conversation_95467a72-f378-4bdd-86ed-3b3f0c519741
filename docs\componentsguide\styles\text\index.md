<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: G<PERSON>er<PERSON>
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 14:07:54
-->

# bc-style-text

> 设置形状文本的样式

<script lang="ts" setup>
import TextDemo from "@demos/TextDemo.vue"
</script>

<ClientOnly>
<TextDemo/>
</ClientOnly>

## 用法

将文本添加到形状

::: code-group

<<< ../../../../src/demos/TextDemo.vue

:::

## 属性

### font

- **类型**: `String`

### maxAngle

- **类型**: `Number`
- **默认值**: `Math.PI / 4`

### offsetX

- **类型**: `Number`
- **默认值**: `0`

### offsetY

- **类型**: `Number`
- **默认值**: `0`

### overflow

- **类型**: `Boolean`
- **默认值**: `false`

### placement

- **类型**: `String`
- **默认值**: `point`

### scale

- **类型**: `Number`

### rotateWithView

- **类型**: `Boolean`
- **默认值**: `false`

### rotation

- **类型**: `Number`
- **默认值**: `0`

### text

- **类型**: `String`

### textAlign

- **类型**: `String`

### textBaseline

- **类型**: `String`
- **默认值**: `middle`

### padding

- **类型**: `Array`
- **默认值**: `() => [0, 0, 0, 0]`

### backgroundFill

- **类型**: `array`, `string`

`placement`当为`point`时，为文本背景填充颜色。默认不填充。可以是十六进制，也可以是 RGBA 数组，其中红色、绿色和蓝色值介于 0 和 255 之间，alpha 值介于 0 和 1 之间（包含 0 和 1）。

### backgroundStroke

- **类型**: `Object`

Stroke style for the text background when `placement` is 'point'. Default is no stroke. Please see [ol-style-stroke](/componentsguide/styles/stroke/#properties) for available options.
为`point`时文本背景的描边样式`placement`。默认为无行程。请参阅[ol-style-stroke](/componentsguide/styles/stroke/#properties)以了解可用选项。

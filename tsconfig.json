{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "compilerOptions": {
    "baseUrl": ".",
    "declaration": true,
    "outDir": "dist",
    "declarationDir": "dist",
    "allowSyntheticDefaultImports": true,
    "noFallthroughCasesInSwitch": true,
    "allowJs": false,
    // `baseUrl` must be placed on the extending configuration in devland, or paths won't be recognized
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    // Needed to address https://github.com/quasarframework/app-extension-typescript/issues/36
    "noEmit": true,
    "resolveJsonModule": true,
    // Avoid cross-os errors due to inconsistent file casing
    "forceConsistentCasingInFileNames": true,
    "sourceMap": true,
    "strict": true,
    "target": "esnext",
    "isolatedModules": true,
    "useDefineForClassFields": true,
    // Fix Volar issue https://github.com/johnsoncodehk/volar/issues/1153
    "jsx": "preserve",
    "lib": ["esnext", "dom"],
    "paths": {
      "@/*": ["./src/*"],
      "@components/*": ["./src/components/*"],
      "@demos/*": ["./src/demos/*"]
    },
    "checkJs": false
  },
  "include": [
    "components.d.ts",
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue"
  ]
}

<template>
  <bc-map
    :loadTilesWhileAnimating="true"
    :loadTilesWhileInteracting="true"
    style="height: 400px"
  >
    <bc-projection-register
      :projectionName="projectionName"
      :projectionDef="projectionDef"
      :projectionExtent="projectionExtent"
    />
    <bc-view
      ref="view"
      :center="center"
      :projection="projectionName"
      :extend="projectionExtent"
      :zoom="3"
    />

    <bc-tile-layer>
      <bc-source-osm />
    </bc-tile-layer>
  </bc-map>
</template>

<script lang="ts" setup>
const center = [312234.8270497762, 2636116.2213047906];
const projectionName = "EPSG:32640";
const projectionDef = "+proj=utm +zone=40 +datum=WGS84 +units=m +no_defs";
const projectionExtent = [
  -98570.85212537996, 2468701.5790765425, 683268.1076887846, 2874585.9453238174,
];
</script>

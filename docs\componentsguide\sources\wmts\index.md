<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISer<PERSON>
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 16:24:39
-->

# bc-source-wmts

来自 WMTS 服务器的切片数据的图层源。

<script lang="ts" setup>
import TileLayerDemo from "@demos/TileLayerDemo.vue"
</script>

<ClientOnly>
<TileLayerDemo />
</ClientOnly>

## 用法

下面的示例展示了如何将 `bc-layer-tile` 组件与 ol-source-wmts 和 `bc-source-osm` 一起使用。

::: code-group

<<< ../../../../src/demos/TileLayerDemo.vue

:::

## 属性

### Props 继承自 OpenLayers

属性直接从 OpenLayers 传递。它们的类型和默认值可以在[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_WMTS-WMTS.html)中查看。只有部分属性是由 Vue / HTML 的保留关键字引起的。下面的部分描述了这种偏离的 props。

### 偏离属性

#### `styles`

设置 WMTS 源`style`属性。

- **类型**: `string`

#### `tileZoomLevel`

设置缩放级别以计算使用的`WMTSTileGrid`。`tileGrid`仅当未设置属性时才使用它。

- **类型**: `number`
- **默认值**: `30`

#### `tileMatrixPrefix`

设置矩阵前缀字符串以创建使用的`WMTSTileGrid`. `tileGrid`仅当未设置属性时才使用它。

- **类型**: `string`
- **默认值**: `""`

## 事件

您可以从底层源访问所有事件。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_WMTS-WMTS.html)以查看将触发的可用事件。

```html
<bc-source-wmts :url="imgUrl" @error="handleEvent" />
```

## 方法

您可以从底层源访问所有方法。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_WMTS-WMTS.html)以查看可用的方法。

要访问源代码，您可以使用`ref()`如下所示：

```vue
<template>
  <!-- ... -->
  <bc-source-wmts :url="url" ref="sourceRef" />
  <!-- ... -->
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import type WMTS from "ol/source/WMTS";

const sourceRef = ref<{ source: WMTS }>(null);

onMounted(() => {
  const source: WMTS = sourceRef.value?.source;
  // call your method on `source`
});
</script>
```

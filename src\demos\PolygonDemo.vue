<template>
  <bc-map
    :loadTilesWhileAnimating="true"
    :loadTilesWhileInteracting="true"
    style="height: 400px"
  >
    <bc-view
      ref="view"
      :center="center"
      :rotation="rotation"
      :zoom="zoom"
      :projection="projection"
    />

    <bc-tile-layer>
      <bc-source-osm />
    </bc-tile-layer>

    <bc-vector-layer>
      <bc-source-vector>
        <bc-feature>
          <bc-geom-polygon
            :coordinates="[
              [
                [-98.844959, 19.691586],
                [-98.842749, 19.69098],
                [-98.84217, 19.693122],
                [-98.844358, 19.693667],
                [-98.844959, 19.691586],
              ],
              [
                [-98.84573034297229, 19.691206743587088],
                [-98.84236332851385, 19.69026183008994],
                [-98.84133866368556, 19.69351739026062],
                [-98.8447693829186, 19.694312532467134],
                [-98.84573034297229, 19.691206743587088],
              ],
            ]"
          ></bc-geom-polygon>
          <bc-style>
            <bc-style-stroke color="red" :width="2"></bc-style-stroke>
            <bc-style-fill color="rgba(255,0,0,0.2)"></bc-style-fill>
          </bc-style>
        </bc-feature>
      </bc-source-vector>
    </bc-vector-layer>
  </bc-map>
</template>

<script lang="ts" setup>
import { ref } from "vue";

const center = ref([-98.8449, 19.6869]);
const projection = ref("EPSG:4326");
const zoom = ref(15);
const rotation = ref(0);
</script>

import type { App } from "vue";
import BcAttributionControl from "./BcAttributionControl.vue";
import BcButtonControl from "./BcButtonControl.vue";
import BcContextMenuControl from "./BcContextMenuControl.vue";
import BcControlBar from "./BcControlBar.vue";
import BcFullScreenControl from "./BcFullScreenControl.vue";
import BcLayerSwitcherControl from "./BcLayerSwitcherControl.vue";
import BcLayerSwitcherImageControl from "./BcLayerSwitcherImageControl.vue";
import BcMousePositionControl from "./BcMousePositionControl.vue";
import BcOverviewMapControl from "./BcOverviewMapControl.vue";
import BcPrintDialogControl from "./BcPrintDialogControl.vue";
import BcRotateControl from "./BcRotateControl.vue";
import BcScaleLineControl from "./BcScaleLineControl.vue";
import BcSwipeControl from "./BcSwipeControl.vue";
import BcToggleControl from "./BcToggleControl.vue";
import BcVideoRecorderControl from "./BcVideoRecorderControl.vue";
import BcZoneControl from "./BcZoneControl.vue";
import BcZoomControl from "./BcZoomControl.vue";
import BcZoomSliderControl from "./BcZoomSliderControl.vue";
import BcZoomToExtentControl from "./BcZoomToExtentControl.vue";

let installed = false;

function install(app: App) {
  if (installed) return;
  installed = true;

  app.component("bc-attribution-control", BcAttributionControl);
  app.component("bc-fullscreen-control", BcFullScreenControl);
  app.component("bc-mouseposition-control", BcMousePositionControl);
  app.component("bc-overviewmap-control", BcOverviewMapControl);
  app.component("bc-scaleline-control", BcScaleLineControl);
  app.component("bc-zoom-control", BcZoomControl);
  app.component("bc-zoomslider-control", BcZoomSliderControl);
  app.component("bc-zoomtoextent-control", BcZoomToExtentControl);
  app.component("bc-rotate-control", BcRotateControl);
  app.component("bc-context-menu-control", BcContextMenuControl);
  app.component("bc-swipe-control", BcSwipeControl);
  app.component("bc-control-bar", BcControlBar);
  app.component("bc-toggle-control", BcToggleControl);
  app.component("bc-button-control", BcButtonControl);
  app.component("bc-printdialog-control", BcPrintDialogControl);
  app.component("bc-videorecorder-control", BcVideoRecorderControl);
  app.component("bc-layerswitcher-control", BcLayerSwitcherControl);
  app.component("bc-layerswitcherimage-control", BcLayerSwitcherImageControl);
  app.component("bc-zone-control", BcZoneControl);
}

export default install;

export {
  install,
  BcAttributionControl,
  BcButtonControl,
  BcContextMenuControl,
  BcControlBar,
  BcFullScreenControl,
  BcLayerSwitcherControl,
  BcLayerSwitcherImageControl,
  BcMousePositionControl,
  BcOverviewMapControl,
  BcPrintDialogControl,
  BcRotateControl,
  BcScaleLineControl,
  BcSwipeControl,
  BcToggleControl,
  BcVideoRecorderControl,
  BcZoneControl,
  BcZoomControl,
  BcZoomSliderControl,
  BcZoomToExtentControl,
};

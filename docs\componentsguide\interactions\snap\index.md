# bc-interaction-snap

> 在修改或绘制矢量特征时处理它们的捕捉。

## 用法

<script lang="ts" setup>
import SnapModifyDemo from "@demos/SnapModifyDemo.vue"
</script>

<ClientOnly>
<SnapModifyDemo/>
</ClientOnly>

::: code-group

<<< ../../../../src/demos/SnapModifyDemo.vue

:::

## 属性

### pixelTolerance

- **类型**: `Number`
- **默认值**: `10`

### edge

- **类型**: `Boolean`
- **默认值**: `false`

### vertex

- **类型**: `Boolean`
- **默认值**: `false`

<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 16:03:31
-->

# bc-source-tile-arcgis-rest

来自 `ArcGIS Rest` 服务的切片数据的图层源。支持地图和图像服务。

对于缓存的 `ArcGIS` 服务，使用 `XYZ` 数据源可以获得更好的性能。

<script lang="ts" setup>
import TileArcGISRestSourceDemo from "@demos/TileArcGISRestSourceDemo.vue"
</script>

<ClientOnly>
<TileArcGISRestSourceDemo />
</ClientOnly>

## 用法

使用示例`bc-source-tile-arcgis-rest`。有关所使用的 arcgis 服务的信息，请访问https://pkk.rosreestr.ru/arcgis/rest/services/PKK6/CadastreObjects/MapServer。

::: code-group

<<< ../../../../src/demos/TileArcGISRestSourceDemo.vue

:::

## 属性

### Props 继承自 OpenLayers

属性直接从 OpenLayers 传递。它们的类型和默认值可以在[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_TileArcGISRest-TileArcGISRest.html)中查看。只有部分属性是由 Vue / HTML 的保留关键字引起的。下面的部分描述了这种偏离的 props。

### 偏离属性

### `tileSize`

访问平铺图像服务器的源的网格图案的平铺大小。`tileGrid`仅当未定义该属性时才会使用该属性。它使用该[`createXYZ`](https://openlayers.org/en/latest/apidoc/module-ol_tilegrid.html#.createXYZ)函数创建具有给定的平铺网格`tileSize`。

- **类型**: `Array[Number]`
- **默认值**: `[256, 256]`

## 事件

您可以从底层源访问所有事件。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_TileArcGISRest-TileArcGISRest.html)以查看将触发的可用事件。

```html
<bc-source-tile-arcgis-rest :url="url" @error="handleEvent" />
```

## 方法

您可以从底层源访问所有方法。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_TileArcGISRest-TileArcGISRest.html)以查看可用的方法。

要访问源代码，您可以使用`ref()`如下所示：

```vue
<template>
  <!-- ... -->
  <bc-source-tile-arcgis-rest :url="url" ref="sourceRef" />
  <!-- ... -->
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import type TileArcGISRest from "ol/source/TileArcGISRest";

const sourceRef = ref<{ source: TileArcGISRest }>(null);

onMounted(() => {
  const source: TileArcGISRest = sourceRef.value?.source;
  // call your method on `source`
});
</script>
```

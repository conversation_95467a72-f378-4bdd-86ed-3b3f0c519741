<template>
  <bc-map
    ref="map"
    :loadTilesWhileAnimating="true"
    :loadTilesWhileInteracting="true"
    style="height: 400px"
  >
    <bc-view
      ref="view"
      :center="center"
      :rotation="rotation"
      :zoom="zoom"
      :projection="projection"
    />

    <bc-tile-layer>
      <bc-source-osm />
    </bc-tile-layer>

    <bc-heatmap-layer
      title="heatmap"
      :blur="20"
      :radius="20"
      :weight="heatmapWeight"
      :zIndex="1"
    >
      <bc-source-vector
        ref="earthquakes"
        url="https://raw.githubusercontent.com/openlayers/openlayers/main/examples/data/kml/2012_Earthquakes_Mag5.kml"
        :format="kml"
      >
      </bc-source-vector>
    </bc-heatmap-layer>
  </bc-map>
</template>

<script lang="ts" setup>
import { ref, inject } from "vue";

const center = ref([101.97, 4.21]);
const projection = ref("EPSG:4326");
const zoom = ref(5);
const rotation = ref(0);
const format = inject("ol-format");
const kml = new format.KML({ extractStyles: false });
const heatmapWeight = function (feature) {
  // 2012_Earthquakes_Mag5.kml stores the magnitude of each earthquake in a
  // standards-violating <magnitude> tag in each Placemark.  We extract it from
  // the Placemark's name instead.
  const name = feature.get("name");
  const magnitude = parseFloat(name.substr(2));
  return magnitude - 5;
};
</script>

<style>
.overlay-content {
  background: red !important;
  color: white;
  box-shadow: 0 5px 10px rgb(2 2 2 / 20%);
  padding: 10px 20px;
  font-size: 16px;
}
</style>

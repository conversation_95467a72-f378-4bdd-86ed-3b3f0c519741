{"name": "bcgis-ol", "version": "1.0.6", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "bcgis-ol", "version": "1.0.6", "license": "MIT", "dependencies": {"file-saver": "^2.0.5", "jspdf": "^2.5.1", "proj4": "^2.9.0"}, "devDependencies": {"@babel/eslint-parser": "^7.22.9", "@release-it/conventional-changelog": "^7.0.0", "@types/file-saver": "^2.0.5", "@types/node": "^18.17.1", "@types/ol-ext": "npm:@siedlerchr/types-ol-ext@^3.2.0", "@types/proj4": "^2.5.2", "@vitejs/plugin-vue": "^4.2.3", "@vitest/coverage-v8": "^0.33.0", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/test-utils": "^2.4.1", "@vue/tsconfig": "^0.4.0", "eslint": "^8.45.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.15.1", "get-repository-url": "^2.0.0", "husky": "^8.0.3", "jsdom": "^22.1.0", "prettier": "^2.8.8", "pretty-quick": "^3.1.3", "release-it": "^16.1.3", "typescript": "~5.1.6", "vite": "^4.4.7", "vite-plugin-dts": "^3.3.1", "vitepress": "^1.0.0-rc.4", "vitest": "^0.33.0", "vue": "^3.3.4"}, "peerDependencies": {"ol": "^7.4.0", "ol-contextmenu": "^5.2.1", "ol-ext": "^4.0.8", "vue": "^3.0.0"}}, "node_modules/@aashutoshrathi/word-wrap": {"version": "1.2.6", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/@algolia/autocomplete-core": {"version": "1.9.3", "dev": true, "license": "MIT", "dependencies": {"@algolia/autocomplete-plugin-algolia-insights": "1.9.3", "@algolia/autocomplete-shared": "1.9.3"}}, "node_modules/@algolia/autocomplete-plugin-algolia-insights": {"version": "1.9.3", "dev": true, "license": "MIT", "dependencies": {"@algolia/autocomplete-shared": "1.9.3"}, "peerDependencies": {"search-insights": ">= 1 < 3"}}, "node_modules/@algolia/autocomplete-preset-algolia": {"version": "1.9.3", "dev": true, "license": "MIT", "dependencies": {"@algolia/autocomplete-shared": "1.9.3"}, "peerDependencies": {"@algolia/client-search": ">= 4.9.1 < 6", "algoliasearch": ">= 4.9.1 < 6"}}, "node_modules/@algolia/autocomplete-shared": {"version": "1.9.3", "dev": true, "license": "MIT", "peerDependencies": {"@algolia/client-search": ">= 4.9.1 < 6", "algoliasearch": ">= 4.9.1 < 6"}}, "node_modules/@algolia/cache-browser-local-storage": {"version": "4.18.0", "dev": true, "license": "MIT", "dependencies": {"@algolia/cache-common": "4.18.0"}}, "node_modules/@algolia/cache-common": {"version": "4.18.0", "dev": true, "license": "MIT"}, "node_modules/@algolia/cache-in-memory": {"version": "4.18.0", "dev": true, "license": "MIT", "dependencies": {"@algolia/cache-common": "4.18.0"}}, "node_modules/@algolia/client-account": {"version": "4.18.0", "dev": true, "license": "MIT", "dependencies": {"@algolia/client-common": "4.18.0", "@algolia/client-search": "4.18.0", "@algolia/transporter": "4.18.0"}}, "node_modules/@algolia/client-analytics": {"version": "4.18.0", "dev": true, "license": "MIT", "dependencies": {"@algolia/client-common": "4.18.0", "@algolia/client-search": "4.18.0", "@algolia/requester-common": "4.18.0", "@algolia/transporter": "4.18.0"}}, "node_modules/@algolia/client-common": {"version": "4.18.0", "dev": true, "license": "MIT", "dependencies": {"@algolia/requester-common": "4.18.0", "@algolia/transporter": "4.18.0"}}, "node_modules/@algolia/client-personalization": {"version": "4.18.0", "dev": true, "license": "MIT", "dependencies": {"@algolia/client-common": "4.18.0", "@algolia/requester-common": "4.18.0", "@algolia/transporter": "4.18.0"}}, "node_modules/@algolia/client-search": {"version": "4.18.0", "dev": true, "license": "MIT", "dependencies": {"@algolia/client-common": "4.18.0", "@algolia/requester-common": "4.18.0", "@algolia/transporter": "4.18.0"}}, "node_modules/@algolia/logger-common": {"version": "4.18.0", "dev": true, "license": "MIT"}, "node_modules/@algolia/logger-console": {"version": "4.18.0", "dev": true, "license": "MIT", "dependencies": {"@algolia/logger-common": "4.18.0"}}, "node_modules/@algolia/requester-browser-xhr": {"version": "4.18.0", "dev": true, "license": "MIT", "dependencies": {"@algolia/requester-common": "4.18.0"}}, "node_modules/@algolia/requester-common": {"version": "4.18.0", "dev": true, "license": "MIT"}, "node_modules/@algolia/requester-node-http": {"version": "4.18.0", "dev": true, "license": "MIT", "dependencies": {"@algolia/requester-common": "4.18.0"}}, "node_modules/@algolia/transporter": {"version": "4.18.0", "dev": true, "license": "MIT", "dependencies": {"@algolia/cache-common": "4.18.0", "@algolia/logger-common": "4.18.0", "@algolia/requester-common": "4.18.0"}}, "node_modules/@ampproject/remapping": {"version": "2.2.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@jridgewell/gen-mapping": "^0.3.0", "@jridgewell/trace-mapping": "^0.3.9"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/code-frame": {"version": "7.22.10", "resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.22.10.tgz", "integrity": "sha512-/KKIMG4UEL35WmI9OlvMhurwtytjvXoFcGNrOvyG9zIzA8YmPjVtIZUf7b05+TPO7G7/GEmLHDaoCgACHl9hhA==", "dev": true, "dependencies": {"@babel/highlight": "^7.22.10", "chalk": "^2.4.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/compat-data": {"version": "7.22.9", "resolved": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.22.9.tgz", "integrity": "sha512-5UamI7xkUcJ3i9qVDS+KFDEK8/7oJ55/sJMB1Ge7IEapr7KfdfV/HErR+koZwOfd+SgtFKOKRhRakdg++DcJpQ==", "dev": true, "peer": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core": {"version": "7.22.10", "resolved": "https://registry.npmjs.org/@babel/core/-/core-7.22.10.tgz", "integrity": "sha512-fTmqbbUBAwCcre6zPzNngvsI0aNrPZe77AeqvDxWM9Nm+04RrJ3CAmGHA9f7lJQY6ZMhRztNemy4uslDxTX4Qw==", "dev": true, "peer": true, "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.22.10", "@babel/generator": "^7.22.10", "@babel/helper-compilation-targets": "^7.22.10", "@babel/helper-module-transforms": "^7.22.9", "@babel/helpers": "^7.22.10", "@babel/parser": "^7.22.10", "@babel/template": "^7.22.5", "@babel/traverse": "^7.22.10", "@babel/types": "^7.22.10", "convert-source-map": "^1.7.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.2", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@babel/eslint-parser": {"version": "7.22.9", "dev": true, "license": "MIT", "dependencies": {"@nicolo-ribaudo/eslint-scope-5-internals": "5.1.1-v1", "eslint-visitor-keys": "^2.1.0", "semver": "^6.3.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || >=14.0.0"}, "peerDependencies": {"@babel/core": ">=7.11.0", "eslint": "^7.5.0 || ^8.0.0"}}, "node_modules/@babel/generator": {"version": "7.22.10", "resolved": "https://registry.npmjs.org/@babel/generator/-/generator-7.22.10.tgz", "integrity": "sha512-79KIf7YiWjjdZ81JnLujDRApWtl7BxTqWD88+FFdQEIOG8LJ0etDOM7CXuIgGJa55sGOwZVwuEsaLEm0PJ5/+A==", "dev": true, "peer": true, "dependencies": {"@babel/types": "^7.22.10", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17", "jsesc": "^2.5.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.22.10", "resolved": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.22.10.tgz", "integrity": "sha512-JMSwHD4J7SLod0idLq5PKgI+6g/hLD/iuWBq08ZX49xE14VpVEojJ5rHWptpirV2j020MvypRLAXAO50igCJ5Q==", "dev": true, "peer": true, "dependencies": {"@babel/compat-data": "^7.22.9", "@babel/helper-validator-option": "^7.22.5", "browserslist": "^4.21.9", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-environment-visitor": {"version": "7.22.5", "resolved": "https://registry.npmjs.org/@babel/helper-environment-visitor/-/helper-environment-visitor-7.22.5.tgz", "integrity": "sha512-XGmhECfVA/5sAt+H+xpSg0mfrHq6FzNr9Oxh7PSEBBRUb/mL7Kz3NICXb194rCqAEdxkhPT1a88teizAFyvk8Q==", "dev": true, "peer": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-function-name": {"version": "7.22.5", "resolved": "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.22.5.tgz", "integrity": "sha512-wtHSq6jMRE3uF2otvfuD3DIvVhOsSNshQl0Qrd7qC9oQJzHvOL4qQXlQn2916+CXGywIjpGuIkoyZRRxHPiNQQ==", "dev": true, "peer": true, "dependencies": {"@babel/template": "^7.22.5", "@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-hoist-variables": {"version": "7.22.5", "resolved": "https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.22.5.tgz", "integrity": "sha512-wGjk9QZVzvknA6yKIUURb8zY3grXCcOZt+/7Wcy8O2uctxhplmUPkOdlgoNhmdVee2c92JXbf1xpMtVNbfoxRw==", "dev": true, "peer": true, "dependencies": {"@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.22.5", "resolved": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.22.5.tgz", "integrity": "sha512-8Dl6+HD/cKifutF5qGd/8ZJi84QeAKh+CEe1sBzz8UayBBGg1dAIJrdHOcOM5b2MpzWL2yuotJTtGjETq0qjXg==", "dev": true, "peer": true, "dependencies": {"@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.22.9", "resolved": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.22.9.tgz", "integrity": "sha512-t+WA2Xn5K+rTeGtC8jCsdAH52bjggG5TKRuRrAGNM/mjIbO4GxvlLMFOEz9wXY5I2XQ60PMFsAG2WIcG82dQMQ==", "dev": true, "peer": true, "dependencies": {"@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-module-imports": "^7.22.5", "@babel/helper-simple-access": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/helper-validator-identifier": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-simple-access": {"version": "7.22.5", "resolved": "https://registry.npmjs.org/@babel/helper-simple-access/-/helper-simple-access-7.22.5.tgz", "integrity": "sha512-n0H99E/K+Bika3++WNL17POvo4rKWZ7lZEp1Q+fStVbUi8nxPQEBOlTmCOxW/0JsS56SKKQ+ojAe2pHKJHN35w==", "dev": true, "peer": true, "dependencies": {"@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-split-export-declaration": {"version": "7.22.6", "resolved": "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.22.6.tgz", "integrity": "sha512-AsUnxuLhRYsisFiaJwvp1QF+I3KjD5FOxut14q/GzovUe6orHLesW2C7d754kRm53h5gqrz6sFl6sxc4BVtE/g==", "dev": true, "peer": true, "dependencies": {"@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.22.5", "resolved": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.22.5.tgz", "integrity": "sha512-mM4COjgZox8U+JcXQwPijIZLElkgEpO5rsERVDJTc2qfCDfERyob6k5WegS14SX18IIjv+XD+GrqNumY5JRCDw==", "dev": true, "peer": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.22.5", "resolved": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.5.tgz", "integrity": "sha512-aJXu+6lErq8ltp+JhkJUfk1MTGyuA4v7f3pA+BJ5HLfNC6nAQ0Cpi9uOquUj8Hehg0aUiHzWQbOVJGao6ztBAQ==", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.22.5", "resolved": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.22.5.tgz", "integrity": "sha512-R3oB6xlIVKUnxNUxbmgq7pKjxpru24zlimpE8WK47fACIlM0II/Hm1RS8IaOI7NgCr6LNS+jl5l75m20npAziw==", "dev": true, "peer": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers": {"version": "7.22.10", "resolved": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.22.10.tgz", "integrity": "sha512-a41J4NW8HyZa1I1vAndrraTlPZ/eZoga2ZgS7fEr0tZJGVU4xqdE80CEm0CcNjha5EZ8fTBYLKHF0kqDUuAwQw==", "dev": true, "peer": true, "dependencies": {"@babel/template": "^7.22.5", "@babel/traverse": "^7.22.10", "@babel/types": "^7.22.10"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/highlight": {"version": "7.22.10", "resolved": "https://registry.npmjs.org/@babel/highlight/-/highlight-7.22.10.tgz", "integrity": "sha512-78aUtVcT7MUscr0K5mIEnkwxPE0MaxkR5RxRwuHaQ+JuU5AmTPhY+do2mdzVTnIJJpyBglql2pehuBIWHug+WQ==", "dev": true, "dependencies": {"@babel/helper-validator-identifier": "^7.22.5", "chalk": "^2.4.2", "js-tokens": "^4.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.22.10", "resolved": "https://registry.npmjs.org/@babel/parser/-/parser-7.22.10.tgz", "integrity": "sha512-lNbdGsQb9ekfsnjFGhEiF4hfFqGgfOP3H3d27re3n+CGhNuTSUEQdfWk556sTLNTloczcdM5TYF2LhzmDQKyvQ==", "dev": true, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/runtime": {"version": "7.21.0", "license": "MIT", "dependencies": {"regenerator-runtime": "^0.13.11"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/template": {"version": "7.22.5", "resolved": "https://registry.npmjs.org/@babel/template/-/template-7.22.5.tgz", "integrity": "sha512-X7yV7eiwAxdj9k94NEylvbVHLiVG1nvzCV2EAowhxLTwODV1jl9UzZ48leOC0sH7OnuHrIkllaBgneUykIcZaw==", "dev": true, "peer": true, "dependencies": {"@babel/code-frame": "^7.22.5", "@babel/parser": "^7.22.5", "@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.22.10", "resolved": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.22.10.tgz", "integrity": "sha512-Q/urqV4pRByiNNpb/f5OSv28ZlGJiFiiTh+GAHktbIrkPhPbl90+uW6SmpoLyZqutrg9AEaEf3Q/ZBRHBXgxig==", "dev": true, "peer": true, "dependencies": {"@babel/code-frame": "^7.22.10", "@babel/generator": "^7.22.10", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-function-name": "^7.22.5", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/parser": "^7.22.10", "@babel/types": "^7.22.10", "debug": "^4.1.0", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/types": {"version": "7.22.10", "resolved": "https://registry.npmjs.org/@babel/types/-/types-7.22.10.tgz", "integrity": "sha512-obaoigiLrlDZ7TUQln/8m4mSqIW2QFeOrCQc9r+xsaHGNoplVNYlRVpsfE8Vj35GEm2ZH4ZhrNYogs/3fj85kg==", "dev": true, "peer": true, "dependencies": {"@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.5", "to-fast-properties": "^2.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@bcoe/v8-coverage": {"version": "0.2.3", "dev": true, "license": "MIT"}, "node_modules/@docsearch/css": {"version": "3.5.1", "dev": true, "license": "MIT"}, "node_modules/@docsearch/js": {"version": "3.5.1", "dev": true, "license": "MIT", "dependencies": {"@docsearch/react": "3.5.1", "preact": "^10.0.0"}}, "node_modules/@docsearch/react": {"version": "3.5.1", "dev": true, "license": "MIT", "dependencies": {"@algolia/autocomplete-core": "1.9.3", "@algolia/autocomplete-preset-algolia": "1.9.3", "@docsearch/css": "3.5.1", "algoliasearch": "^4.0.0"}, "peerDependencies": {"@types/react": ">= 16.8.0 < 19.0.0", "react": ">= 16.8.0 < 19.0.0", "react-dom": ">= 16.8.0 < 19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "react": {"optional": true}, "react-dom": {"optional": true}}}, "node_modules/@esbuild/win32-x64": {"version": "0.18.11", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=12"}}, "node_modules/@eslint-community/eslint-utils": {"version": "4.4.0", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "node_modules/@eslint-community/eslint-utils/node_modules/eslint-visitor-keys": {"version": "3.4.0", "dev": true, "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint-community/regexpp": {"version": "4.5.0", "dev": true, "license": "MIT", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "node_modules/@eslint/eslintrc": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "globals": "^13.19.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint/eslintrc/node_modules/globals": {"version": "13.20.0", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@eslint/js": {"version": "8.44.0", "dev": true, "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/@humanwhocodes/config-array": {"version": "0.11.10", "dev": true, "license": "Apache-2.0", "dependencies": {"@humanwhocodes/object-schema": "^1.2.1", "debug": "^4.1.1", "minimatch": "^3.0.5"}, "engines": {"node": ">=10.10.0"}}, "node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/object-schema": {"version": "1.2.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@hutson/parse-repository-url": {"version": "3.0.2", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=6.9.0"}}, "node_modules/@iarna/toml": {"version": "2.2.5", "dev": true, "license": "ISC"}, "node_modules/@istanbuljs/schema": {"version": "0.1.3", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@jest/schemas": {"version": "29.6.0", "dev": true, "license": "MIT", "dependencies": {"@sinclair/typebox": "^0.27.8"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.3", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/set-array": "^1.0.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.9"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.1.2", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.4.15", "dev": true, "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.18", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "3.1.0", "@jridgewell/sourcemap-codec": "1.4.14"}}, "node_modules/@jridgewell/trace-mapping/node_modules/@jridgewell/sourcemap-codec": {"version": "1.4.14", "dev": true, "license": "MIT"}, "node_modules/@mapbox/jsonlint-lines-primitives": {"version": "2.0.2", "peer": true, "engines": {"node": ">= 0.6"}}, "node_modules/@mapbox/mapbox-gl-style-spec": {"version": "13.28.0", "license": "ISC", "peer": true, "dependencies": {"@mapbox/jsonlint-lines-primitives": "~2.0.2", "@mapbox/point-geometry": "^0.1.0", "@mapbox/unitbezier": "^0.0.0", "csscolorparser": "~1.0.2", "json-stringify-pretty-compact": "^2.0.0", "minimist": "^1.2.6", "rw": "^1.3.3", "sort-object": "^0.3.2"}, "bin": {"gl-style-composite": "bin/gl-style-composite.js", "gl-style-format": "bin/gl-style-format.js", "gl-style-migrate": "bin/gl-style-migrate.js", "gl-style-validate": "bin/gl-style-validate.js"}}, "node_modules/@mapbox/point-geometry": {"version": "0.1.0", "license": "ISC", "peer": true}, "node_modules/@mapbox/unitbezier": {"version": "0.0.0", "license": "BSD-2-<PERSON><PERSON>", "peer": true}, "node_modules/@microsoft/api-extractor": {"version": "7.36.2", "dev": true, "license": "MIT", "dependencies": {"@microsoft/api-extractor-model": "7.27.4", "@microsoft/tsdoc": "0.14.2", "@microsoft/tsdoc-config": "~0.16.1", "@rushstack/node-core-library": "3.59.5", "@rushstack/rig-package": "0.4.0", "@rushstack/ts-command-line": "4.15.1", "colors": "~1.2.1", "lodash": "~4.17.15", "resolve": "~1.22.1", "semver": "~7.3.0", "source-map": "~0.6.1", "typescript": "~5.0.4"}, "bin": {"api-extractor": "bin/api-extractor"}}, "node_modules/@microsoft/api-extractor-model": {"version": "7.27.4", "dev": true, "license": "MIT", "dependencies": {"@microsoft/tsdoc": "0.14.2", "@microsoft/tsdoc-config": "~0.16.1", "@rushstack/node-core-library": "3.59.5"}}, "node_modules/@microsoft/api-extractor/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@microsoft/api-extractor/node_modules/semver": {"version": "7.3.8", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@microsoft/api-extractor/node_modules/typescript": {"version": "5.0.4", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=12.20"}}, "node_modules/@microsoft/api-extractor/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/@microsoft/tsdoc": {"version": "0.14.2", "dev": true, "license": "MIT"}, "node_modules/@microsoft/tsdoc-config": {"version": "0.16.2", "dev": true, "license": "MIT", "dependencies": {"@microsoft/tsdoc": "0.14.2", "ajv": "~6.12.6", "jju": "~1.4.0", "resolve": "~1.19.0"}}, "node_modules/@microsoft/tsdoc-config/node_modules/resolve": {"version": "1.19.0", "dev": true, "license": "MIT", "dependencies": {"is-core-module": "^2.1.0", "path-parse": "^1.0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@nicolo-ribaudo/eslint-scope-5-internals": {"version": "5.1.1-v1", "dev": true, "license": "MIT", "dependencies": {"eslint-scope": "5.1.1"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@octokit/auth-token": {"version": "3.0.4", "dev": true, "license": "MIT", "engines": {"node": ">= 14"}}, "node_modules/@octokit/core": {"version": "4.2.4", "dev": true, "license": "MIT", "dependencies": {"@octokit/auth-token": "^3.0.0", "@octokit/graphql": "^5.0.0", "@octokit/request": "^6.0.0", "@octokit/request-error": "^3.0.0", "@octokit/types": "^9.0.0", "before-after-hook": "^2.2.0", "universal-user-agent": "^6.0.0"}, "engines": {"node": ">= 14"}}, "node_modules/@octokit/endpoint": {"version": "7.0.6", "dev": true, "license": "MIT", "dependencies": {"@octokit/types": "^9.0.0", "is-plain-object": "^5.0.0", "universal-user-agent": "^6.0.0"}, "engines": {"node": ">= 14"}}, "node_modules/@octokit/graphql": {"version": "5.0.6", "dev": true, "license": "MIT", "dependencies": {"@octokit/request": "^6.0.0", "@octokit/types": "^9.0.0", "universal-user-agent": "^6.0.0"}, "engines": {"node": ">= 14"}}, "node_modules/@octokit/openapi-types": {"version": "18.0.0", "dev": true, "license": "MIT"}, "node_modules/@octokit/plugin-paginate-rest": {"version": "6.1.2", "dev": true, "license": "MIT", "dependencies": {"@octokit/tsconfig": "^1.0.2", "@octokit/types": "^9.2.3"}, "engines": {"node": ">= 14"}, "peerDependencies": {"@octokit/core": ">=4"}}, "node_modules/@octokit/plugin-request-log": {"version": "1.0.4", "dev": true, "license": "MIT", "peerDependencies": {"@octokit/core": ">=3"}}, "node_modules/@octokit/plugin-rest-endpoint-methods": {"version": "7.2.3", "dev": true, "license": "MIT", "dependencies": {"@octokit/types": "^10.0.0"}, "engines": {"node": ">= 14"}, "peerDependencies": {"@octokit/core": ">=3"}}, "node_modules/@octokit/plugin-rest-endpoint-methods/node_modules/@octokit/types": {"version": "10.0.0", "dev": true, "license": "MIT", "dependencies": {"@octokit/openapi-types": "^18.0.0"}}, "node_modules/@octokit/request": {"version": "6.2.8", "dev": true, "license": "MIT", "dependencies": {"@octokit/endpoint": "^7.0.0", "@octokit/request-error": "^3.0.0", "@octokit/types": "^9.0.0", "is-plain-object": "^5.0.0", "node-fetch": "^2.6.7", "universal-user-agent": "^6.0.0"}, "engines": {"node": ">= 14"}}, "node_modules/@octokit/request-error": {"version": "3.0.3", "dev": true, "license": "MIT", "dependencies": {"@octokit/types": "^9.0.0", "deprecation": "^2.0.0", "once": "^1.4.0"}, "engines": {"node": ">= 14"}}, "node_modules/@octokit/request/node_modules/node-fetch": {"version": "2.6.12", "dev": true, "license": "MIT", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/@octokit/rest": {"version": "19.0.13", "dev": true, "license": "MIT", "dependencies": {"@octokit/core": "^4.2.1", "@octokit/plugin-paginate-rest": "^6.1.2", "@octokit/plugin-request-log": "^1.0.4", "@octokit/plugin-rest-endpoint-methods": "^7.1.2"}, "engines": {"node": ">= 14"}}, "node_modules/@octokit/tsconfig": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/@octokit/types": {"version": "9.3.2", "dev": true, "license": "MIT", "dependencies": {"@octokit/openapi-types": "^18.0.0"}}, "node_modules/@one-ini/wasm": {"version": "0.1.1", "dev": true, "license": "MIT"}, "node_modules/@petamoriken/float16": {"version": "3.8.3", "license": "MIT", "peer": true}, "node_modules/@pnpm/config.env-replace": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=12.22.0"}}, "node_modules/@pnpm/network.ca-file": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "4.2.10"}, "engines": {"node": ">=12.22.0"}}, "node_modules/@pnpm/network.ca-file/node_modules/graceful-fs": {"version": "4.2.10", "dev": true, "license": "ISC"}, "node_modules/@pnpm/npm-conf": {"version": "2.1.1", "dev": true, "license": "MIT", "dependencies": {"@pnpm/config.env-replace": "^1.1.0", "@pnpm/network.ca-file": "^1.0.1", "config-chain": "^1.1.11"}, "engines": {"node": ">=12"}}, "node_modules/@release-it/conventional-changelog": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"concat-stream": "^2.0.0", "conventional-changelog": "^4.0.0", "conventional-recommended-bump": "^7.0.1", "semver": "7.5.1"}, "engines": {"node": ">=16"}, "peerDependencies": {"release-it": "^16.0.0"}}, "node_modules/@release-it/conventional-changelog/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@release-it/conventional-changelog/node_modules/semver": {"version": "7.5.1", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@release-it/conventional-changelog/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/@rollup/pluginutils": {"version": "5.0.2", "dev": true, "license": "MIT", "dependencies": {"@types/estree": "^1.0.0", "estree-walker": "^2.0.2", "picomatch": "^2.3.1"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"rollup": "^1.20.0||^2.0.0||^3.0.0"}, "peerDependenciesMeta": {"rollup": {"optional": true}}}, "node_modules/@rushstack/node-core-library": {"version": "3.59.5", "dev": true, "license": "MIT", "dependencies": {"colors": "~1.2.1", "fs-extra": "~7.0.1", "import-lazy": "~4.0.0", "jju": "~1.4.0", "resolve": "~1.22.1", "semver": "~7.3.0", "z-schema": "~5.0.2"}, "peerDependencies": {"@types/node": "*"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "node_modules/@rushstack/node-core-library/node_modules/fs-extra": {"version": "7.0.1", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.1.2", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}, "engines": {"node": ">=6 <7 || >=8"}}, "node_modules/@rushstack/node-core-library/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@rushstack/node-core-library/node_modules/semver": {"version": "7.3.8", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@rushstack/node-core-library/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/@rushstack/rig-package": {"version": "0.4.0", "dev": true, "license": "MIT", "dependencies": {"resolve": "~1.22.1", "strip-json-comments": "~3.1.1"}}, "node_modules/@rushstack/ts-command-line": {"version": "4.15.1", "dev": true, "license": "MIT", "dependencies": {"@types/argparse": "1.0.38", "argparse": "~1.0.9", "colors": "~1.2.1", "string-argv": "~0.3.1"}}, "node_modules/@rushstack/ts-command-line/node_modules/argparse": {"version": "1.0.10", "dev": true, "license": "MIT", "dependencies": {"sprintf-js": "~1.0.2"}}, "node_modules/@sinclair/typebox": {"version": "0.27.8", "dev": true, "license": "MIT"}, "node_modules/@sindresorhus/is": {"version": "5.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sindresorhus/is?sponsor=1"}}, "node_modules/@szmarczak/http-timer": {"version": "5.0.1", "dev": true, "license": "MIT", "dependencies": {"defer-to-connect": "^2.0.1"}, "engines": {"node": ">=14.16"}}, "node_modules/@tootallnate/quickjs-emscripten": {"version": "0.23.0", "dev": true, "license": "MIT"}, "node_modules/@types/argparse": {"version": "1.0.38", "dev": true, "license": "MIT"}, "node_modules/@types/chai": {"version": "4.3.5", "dev": true, "license": "MIT"}, "node_modules/@types/chai-subset": {"version": "1.3.3", "dev": true, "license": "MIT", "dependencies": {"@types/chai": "*"}}, "node_modules/@types/estree": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/@types/file-saver": {"version": "2.0.5", "dev": true, "license": "MIT"}, "node_modules/@types/http-cache-semantics": {"version": "4.0.1", "dev": true, "license": "MIT"}, "node_modules/@types/istanbul-lib-coverage": {"version": "2.0.4", "dev": true, "license": "MIT"}, "node_modules/@types/json-schema": {"version": "7.0.12", "dev": true, "license": "MIT"}, "node_modules/@types/minimatch": {"version": "3.0.5", "dev": true, "license": "MIT"}, "node_modules/@types/minimist": {"version": "1.2.2", "dev": true, "license": "MIT"}, "node_modules/@types/node": {"version": "18.17.1", "dev": true, "license": "MIT"}, "node_modules/@types/normalize-package-data": {"version": "2.4.1", "dev": true, "license": "MIT"}, "node_modules/@types/ol-ext": {"name": "@siedlerchr/types-ol-ext", "version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"jspdf": "^2.5.1"}}, "node_modules/@types/proj4": {"version": "2.5.2", "dev": true, "license": "MIT"}, "node_modules/@types/raf": {"version": "3.4.0", "license": "MIT", "optional": true}, "node_modules/@types/semver": {"version": "7.5.0", "dev": true, "license": "MIT"}, "node_modules/@types/web-bluetooth": {"version": "0.0.17", "resolved": "https://registry.npmjs.org/@types/web-bluetooth/-/web-bluetooth-0.0.17.tgz", "integrity": "sha512-4p9vcSmxAayx72yn70joFoL44c9MO/0+iVEBIQXe3v2h2SiAsEIo/G5v6ObFWvNKRFjbrVadNf9LqEEZeQPzdA==", "dev": true}, "node_modules/@typescript-eslint/eslint-plugin": {"version": "5.59.9", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/regexpp": "^4.4.0", "@typescript-eslint/scope-manager": "5.59.9", "@typescript-eslint/type-utils": "5.59.9", "@typescript-eslint/utils": "5.59.9", "debug": "^4.3.4", "grapheme-splitter": "^1.0.4", "ignore": "^5.2.0", "natural-compare-lite": "^1.4.0", "semver": "^7.3.7", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"@typescript-eslint/parser": "^5.0.0", "eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/eslint-plugin/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@typescript-eslint/eslint-plugin/node_modules/semver": {"version": "7.5.1", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@typescript-eslint/eslint-plugin/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/@typescript-eslint/parser": {"version": "5.59.9", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/scope-manager": "5.59.9", "@typescript-eslint/types": "5.59.9", "@typescript-eslint/typescript-estree": "5.59.9", "debug": "^4.3.4"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/scope-manager": {"version": "5.59.9", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "5.59.9", "@typescript-eslint/visitor-keys": "5.59.9"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/type-utils": {"version": "5.59.9", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/typescript-estree": "5.59.9", "@typescript-eslint/utils": "5.59.9", "debug": "^4.3.4", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/types": {"version": "5.59.9", "dev": true, "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/typescript-estree": {"version": "5.59.9", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/types": "5.59.9", "@typescript-eslint/visitor-keys": "5.59.9", "debug": "^4.3.4", "globby": "^11.1.0", "is-glob": "^4.0.3", "semver": "^7.3.7", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/typescript-estree/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@typescript-eslint/typescript-estree/node_modules/semver": {"version": "7.5.1", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@typescript-eslint/typescript-estree/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/@typescript-eslint/utils": {"version": "5.59.9", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@types/json-schema": "^7.0.9", "@types/semver": "^7.3.12", "@typescript-eslint/scope-manager": "5.59.9", "@typescript-eslint/types": "5.59.9", "@typescript-eslint/typescript-estree": "5.59.9", "eslint-scope": "^5.1.1", "semver": "^7.3.7"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/@typescript-eslint/utils/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@typescript-eslint/utils/node_modules/semver": {"version": "7.5.1", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@typescript-eslint/utils/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/@typescript-eslint/visitor-keys": {"version": "5.59.9", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "5.59.9", "eslint-visitor-keys": "^3.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/visitor-keys/node_modules/eslint-visitor-keys": {"version": "3.4.1", "dev": true, "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@vitejs/plugin-vue": {"version": "4.2.3", "dev": true, "license": "MIT", "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependencies": {"vite": "^4.0.0", "vue": "^3.2.25"}}, "node_modules/@vitest/coverage-v8": {"version": "0.33.0", "dev": true, "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.2.1", "@bcoe/v8-coverage": "^0.2.3", "istanbul-lib-coverage": "^3.2.0", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.1", "istanbul-reports": "^3.1.5", "magic-string": "^0.30.1", "picocolors": "^1.0.0", "std-env": "^3.3.3", "test-exclude": "^6.0.0", "v8-to-istanbul": "^9.1.0"}, "funding": {"url": "https://opencollective.com/vitest"}, "peerDependencies": {"vitest": ">=0.32.0 <1"}}, "node_modules/@vitest/expect": {"version": "0.33.0", "dev": true, "license": "MIT", "dependencies": {"@vitest/spy": "0.33.0", "@vitest/utils": "0.33.0", "chai": "^4.3.7"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/runner": {"version": "0.33.0", "dev": true, "license": "MIT", "dependencies": {"@vitest/utils": "0.33.0", "p-limit": "^4.0.0", "pathe": "^1.1.1"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/runner/node_modules/p-limit": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"yocto-queue": "^1.0.0"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@vitest/runner/node_modules/yocto-queue": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12.20"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@vitest/snapshot": {"version": "0.33.0", "dev": true, "license": "MIT", "dependencies": {"magic-string": "^0.30.1", "pathe": "^1.1.1", "pretty-format": "^29.5.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/spy": {"version": "0.33.0", "dev": true, "license": "MIT", "dependencies": {"tinyspy": "^2.1.1"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/utils": {"version": "0.33.0", "dev": true, "license": "MIT", "dependencies": {"diff-sequences": "^29.4.3", "loupe": "^2.3.6", "pretty-format": "^29.5.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@volar/language-core": {"version": "1.9.0", "dev": true, "license": "MIT", "dependencies": {"@volar/source-map": "1.9.0"}}, "node_modules/@volar/source-map": {"version": "1.9.0", "dev": true, "license": "MIT", "dependencies": {"muggle-string": "^0.3.1"}}, "node_modules/@volar/typescript": {"version": "1.9.0", "dev": true, "license": "MIT", "dependencies": {"@volar/language-core": "1.9.0"}}, "node_modules/@vue/compiler-core": {"version": "3.3.4", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.21.3", "@vue/shared": "3.3.4", "estree-walker": "^2.0.2", "source-map-js": "^1.0.2"}}, "node_modules/@vue/compiler-dom": {"version": "3.3.4", "dev": true, "license": "MIT", "dependencies": {"@vue/compiler-core": "3.3.4", "@vue/shared": "3.3.4"}}, "node_modules/@vue/compiler-sfc": {"version": "3.3.4", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.20.15", "@vue/compiler-core": "3.3.4", "@vue/compiler-dom": "3.3.4", "@vue/compiler-ssr": "3.3.4", "@vue/reactivity-transform": "3.3.4", "@vue/shared": "3.3.4", "estree-walker": "^2.0.2", "magic-string": "^0.30.0", "postcss": "^8.1.10", "source-map-js": "^1.0.2"}}, "node_modules/@vue/compiler-ssr": {"version": "3.3.4", "dev": true, "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.3.4", "@vue/shared": "3.3.4"}}, "node_modules/@vue/devtools-api": {"version": "6.5.0", "dev": true, "license": "MIT"}, "node_modules/@vue/eslint-config-prettier": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0"}, "peerDependencies": {"eslint": ">= 7.28.0", "prettier": ">= 2.0.0"}}, "node_modules/@vue/eslint-config-typescript": {"version": "11.0.3", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/eslint-plugin": "^5.59.1", "@typescript-eslint/parser": "^5.59.1", "vue-eslint-parser": "^9.1.1"}, "engines": {"node": "^14.17.0 || >=16.0.0"}, "peerDependencies": {"eslint": "^6.2.0 || ^7.0.0 || ^8.0.0", "eslint-plugin-vue": "^9.0.0", "typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@vue/language-core": {"version": "1.8.5", "dev": true, "license": "MIT", "dependencies": {"@volar/language-core": "~1.9.0", "@volar/source-map": "~1.9.0", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@vue/language-core/node_modules/brace-expansion": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/@vue/language-core/node_modules/minimatch": {"version": "9.0.3", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@vue/reactivity": {"version": "3.3.4", "dev": true, "license": "MIT", "dependencies": {"@vue/shared": "3.3.4"}}, "node_modules/@vue/reactivity-transform": {"version": "3.3.4", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.20.15", "@vue/compiler-core": "3.3.4", "@vue/shared": "3.3.4", "estree-walker": "^2.0.2", "magic-string": "^0.30.0"}}, "node_modules/@vue/runtime-core": {"version": "3.3.4", "dev": true, "license": "MIT", "dependencies": {"@vue/reactivity": "3.3.4", "@vue/shared": "3.3.4"}}, "node_modules/@vue/runtime-dom": {"version": "3.3.4", "dev": true, "license": "MIT", "dependencies": {"@vue/runtime-core": "3.3.4", "@vue/shared": "3.3.4", "csstype": "^3.1.1"}}, "node_modules/@vue/server-renderer": {"version": "3.3.4", "dev": true, "license": "MIT", "dependencies": {"@vue/compiler-ssr": "3.3.4", "@vue/shared": "3.3.4"}, "peerDependencies": {"vue": "3.3.4"}}, "node_modules/@vue/shared": {"version": "3.3.4", "dev": true, "license": "MIT"}, "node_modules/@vue/test-utils": {"version": "2.4.1", "dev": true, "license": "MIT", "dependencies": {"js-beautify": "1.14.9", "vue-component-type-helpers": "1.8.4"}, "peerDependencies": {"@vue/server-renderer": "^3.0.1", "vue": "^3.0.1"}, "peerDependenciesMeta": {"@vue/server-renderer": {"optional": true}}}, "node_modules/@vue/tsconfig": {"version": "0.4.0", "dev": true, "license": "MIT"}, "node_modules/@vue/typescript": {"version": "1.8.5", "dev": true, "license": "MIT", "dependencies": {"@volar/typescript": "~1.9.0", "@vue/language-core": "1.8.5"}}, "node_modules/@vueuse/core": {"version": "10.3.0", "resolved": "https://registry.npmjs.org/@vueuse/core/-/core-10.3.0.tgz", "integrity": "sha512-B<PERSON>5yxcFKb5btFjTSAFjTu5jmwoW66fyV9uJIP4wUXXU8aR5Hl44gndaaXp7dC5HSObmgbnR2RN+Un1p68Mf5Q==", "dev": true, "dependencies": {"@types/web-bluetooth": "^0.0.17", "@vueuse/metadata": "10.3.0", "@vueuse/shared": "10.3.0", "vue-demi": ">=0.14.5"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/@vueuse/core/node_modules/vue-demi": {"version": "0.14.5", "resolved": "https://registry.npmjs.org/vue-demi/-/vue-demi-0.14.5.tgz", "integrity": "sha512-o9NUVpl/YlsGJ7t+xuqJKx8EBGf1quRhCiT6D/J0pfwmk9zUwYkC7yrF4SZCe6fETvSM3UNL2edcbYrSyc4QHA==", "dev": true, "hasInstallScript": true, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/@vueuse/integrations": {"version": "10.3.0", "resolved": "https://registry.npmjs.org/@vueuse/integrations/-/integrations-10.3.0.tgz", "integrity": "sha512-Jgiv7oFyIgC6BxmDtiyG/fxyGysIds00YaY7sefwbhCZ2/tjEx1W/1WcsISSJPNI30in28+HC2J4uuU8184ekg==", "dev": true, "dependencies": {"@vueuse/core": "10.3.0", "@vueuse/shared": "10.3.0", "vue-demi": ">=0.14.5"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"async-validator": "*", "axios": "*", "change-case": "*", "drauu": "*", "focus-trap": "*", "fuse.js": "*", "idb-keyval": "*", "jwt-decode": "*", "nprogress": "*", "qrcode": "*", "sortablejs": "*", "universal-cookie": "*"}, "peerDependenciesMeta": {"async-validator": {"optional": true}, "axios": {"optional": true}, "change-case": {"optional": true}, "drauu": {"optional": true}, "focus-trap": {"optional": true}, "fuse.js": {"optional": true}, "idb-keyval": {"optional": true}, "jwt-decode": {"optional": true}, "nprogress": {"optional": true}, "qrcode": {"optional": true}, "sortablejs": {"optional": true}, "universal-cookie": {"optional": true}}}, "node_modules/@vueuse/integrations/node_modules/vue-demi": {"version": "0.14.5", "resolved": "https://registry.npmjs.org/vue-demi/-/vue-demi-0.14.5.tgz", "integrity": "sha512-o9NUVpl/YlsGJ7t+xuqJKx8EBGf1quRhCiT6D/J0pfwmk9zUwYkC7yrF4SZCe6fETvSM3UNL2edcbYrSyc4QHA==", "dev": true, "hasInstallScript": true, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/@vueuse/metadata": {"version": "10.3.0", "resolved": "https://registry.npmjs.org/@vueuse/metadata/-/metadata-10.3.0.tgz", "integrity": "sha512-Ema3YhNOa4swDsV0V7CEY5JXvK19JI/o1szFO1iWxdFg3vhdFtCtSTP26PCvbUpnUtNHBY2wx5y3WDXND5Pvnw==", "dev": true, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/@vueuse/shared": {"version": "10.3.0", "resolved": "https://registry.npmjs.org/@vueuse/shared/-/shared-10.3.0.tgz", "integrity": "sha512-kGqCTEuFPMK4+fNWy6dUOiYmxGcUbtznMwBZLC1PubidF4VZY05B+Oht7Jh7/6x4VOWGpvu3R37WHi81cKpiqg==", "dev": true, "dependencies": {"vue-demi": ">=0.14.5"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/@vueuse/shared/node_modules/vue-demi": {"version": "0.14.5", "resolved": "https://registry.npmjs.org/vue-demi/-/vue-demi-0.14.5.tgz", "integrity": "sha512-o9NUVpl/YlsGJ7t+xuqJKx8EBGf1quRhCiT6D/J0pfwmk9zUwYkC7yrF4SZCe6fETvSM3UNL2edcbYrSyc4QHA==", "dev": true, "hasInstallScript": true, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/abab": {"version": "2.0.6", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/abbrev": {"version": "1.1.1", "dev": true, "license": "ISC"}, "node_modules/acorn": {"version": "8.10.0", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "dev": true, "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/acorn-walk": {"version": "8.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/add-stream": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/agent-base": {"version": "6.0.2", "dev": true, "license": "MIT", "dependencies": {"debug": "4"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/ajv": {"version": "6.12.6", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/algoliasearch": {"version": "4.18.0", "dev": true, "license": "MIT", "dependencies": {"@algolia/cache-browser-local-storage": "4.18.0", "@algolia/cache-common": "4.18.0", "@algolia/cache-in-memory": "4.18.0", "@algolia/client-account": "4.18.0", "@algolia/client-analytics": "4.18.0", "@algolia/client-common": "4.18.0", "@algolia/client-personalization": "4.18.0", "@algolia/client-search": "4.18.0", "@algolia/logger-common": "4.18.0", "@algolia/logger-console": "4.18.0", "@algolia/requester-browser-xhr": "4.18.0", "@algolia/requester-common": "4.18.0", "@algolia/requester-node-http": "4.18.0", "@algolia/transporter": "4.18.0"}}, "node_modules/ansi-align": {"version": "3.0.1", "dev": true, "license": "ISC", "dependencies": {"string-width": "^4.1.0"}}, "node_modules/ansi-align/node_modules/emoji-regex": {"version": "8.0.0", "dev": true, "license": "MIT"}, "node_modules/ansi-align/node_modules/string-width": {"version": "4.2.3", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/ansi-escapes": {"version": "4.3.2", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.21.3"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-escapes/node_modules/type-fest": {"version": "0.21.3", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-regex": {"version": "5.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-sequence-parser": {"version": "1.1.0", "dev": true, "license": "MIT"}, "node_modules/ansi-styles": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==", "dev": true, "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/argparse": {"version": "2.0.1", "dev": true, "license": "Python-2.0"}, "node_modules/array-buffer-byte-length": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "is-array-buffer": "^3.0.1"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array-differ": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/array-ify": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/array-union": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/array.prototype.map": {"version": "1.0.5", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4", "es-array-method-boxes-properly": "^1.0.0", "is-string": "^1.0.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/arrify": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/assertion-error": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/ast-types": {"version": "0.13.4", "dev": true, "license": "MIT", "dependencies": {"tslib": "^2.0.1"}, "engines": {"node": ">=4"}}, "node_modules/ast-types/node_modules/tslib": {"version": "2.6.1", "dev": true, "license": "0BSD"}, "node_modules/async-retry": {"version": "1.3.3", "dev": true, "license": "MIT", "dependencies": {"retry": "0.13.1"}}, "node_modules/asynckit": {"version": "0.4.0", "dev": true, "license": "MIT"}, "node_modules/atob": {"version": "2.1.2", "license": "(MIT OR Apache-2.0)", "bin": {"atob": "bin/atob.js"}, "engines": {"node": ">= 4.5.0"}}, "node_modules/available-typed-arrays": {"version": "1.0.5", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/axios": {"version": "0.18.1", "dev": true, "license": "MIT", "dependencies": {"follow-redirects": "1.5.10", "is-buffer": "^2.0.2"}}, "node_modules/balanced-match": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/base64-arraybuffer": {"version": "1.0.2", "license": "MIT", "optional": true, "engines": {"node": ">= 0.6.0"}}, "node_modules/base64-js": {"version": "1.5.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/basic-ftp": {"version": "5.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=10.0.0"}}, "node_modules/before-after-hook": {"version": "2.2.3", "dev": true, "license": "Apache-2.0"}, "node_modules/big-integer": {"version": "1.6.51", "dev": true, "license": "Unlicense", "engines": {"node": ">=0.6"}}, "node_modules/bl": {"version": "5.1.0", "dev": true, "license": "MIT", "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}}, "node_modules/body-scroll-lock": {"version": "4.0.0-beta.0", "dev": true, "license": "MIT"}, "node_modules/boolbase": {"version": "1.0.0", "dev": true, "license": "ISC"}, "node_modules/boxen": {"version": "7.0.2", "dev": true, "license": "MIT", "dependencies": {"ansi-align": "^3.0.1", "camelcase": "^7.0.0", "chalk": "^5.0.1", "cli-boxes": "^3.0.0", "string-width": "^5.1.2", "type-fest": "^2.13.0", "widest-line": "^4.0.1", "wrap-ansi": "^8.0.1"}, "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/boxen/node_modules/chalk": {"version": "5.2.0", "dev": true, "license": "MIT", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/boxen/node_modules/type-fest": {"version": "2.19.0", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=12.20"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/bplist-parser": {"version": "0.2.0", "dev": true, "license": "MIT", "dependencies": {"big-integer": "^1.6.44"}, "engines": {"node": ">= 5.10.0"}}, "node_modules/brace-expansion": {"version": "1.1.11", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.2", "dev": true, "license": "MIT", "dependencies": {"fill-range": "^7.0.1"}, "engines": {"node": ">=8"}}, "node_modules/browserslist": {"version": "4.21.10", "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.10.tgz", "integrity": "sha512-bipEBdZfVH5/pwrvqc+Ub0kUPVfGUhlKxbvfD+z1BDnPEO/X98ruXGA1WP5ASpAFKan7Qr6j736IacbZQuAlKQ==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "peer": true, "dependencies": {"caniuse-lite": "^1.0.30001517", "electron-to-chromium": "^1.4.477", "node-releases": "^2.0.13", "update-browserslist-db": "^1.0.11"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/btoa": {"version": "1.2.1", "license": "(MIT OR Apache-2.0)", "bin": {"btoa": "bin/btoa.js"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/buffer": {"version": "6.0.3", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "node_modules/buffer-from": {"version": "1.1.2", "dev": true, "license": "MIT"}, "node_modules/bundle-name": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"run-applescript": "^5.0.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cac": {"version": "6.7.14", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/cacheable-lookup": {"version": "7.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=14.16"}}, "node_modules/cacheable-request": {"version": "10.2.9", "dev": true, "license": "MIT", "dependencies": {"@types/http-cache-semantics": "^4.0.1", "get-stream": "^6.0.1", "http-cache-semantics": "^4.1.1", "keyv": "^4.5.2", "mimic-response": "^4.0.0", "normalize-url": "^8.0.0", "responselike": "^3.0.0"}, "engines": {"node": ">=14.16"}}, "node_modules/cacheable-request/node_modules/get-stream": {"version": "6.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/call-bind": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/callsites": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/camelcase": {"version": "7.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/camelcase-keys": {"version": "6.2.2", "dev": true, "license": "MIT", "dependencies": {"camelcase": "^5.3.1", "map-obj": "^4.0.0", "quick-lru": "^4.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/camelcase-keys/node_modules/camelcase": {"version": "5.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/camelcase-keys/node_modules/quick-lru": {"version": "4.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/caniuse-lite": {"version": "1.0.30001521", "resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001521.tgz", "integrity": "sha512-fnx1grfpEOvDGH+V17eccmNjucGUnCbP6KL+l5KqBIerp26WK/+RQ7CIDE37KGJjaPyqWXXlFUyKiWmvdNNKmQ==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "peer": true}, "node_modules/canvg": {"version": "3.0.10", "license": "MIT", "optional": true, "dependencies": {"@babel/runtime": "^7.12.5", "@types/raf": "^3.4.0", "core-js": "^3.8.3", "raf": "^3.4.1", "regenerator-runtime": "^0.13.7", "rgbcolor": "^1.0.1", "stackblur-canvas": "^2.0.0", "svg-pathdata": "^6.0.3"}, "engines": {"node": ">=10.0.0"}}, "node_modules/chai": {"version": "4.3.7", "dev": true, "license": "MIT", "dependencies": {"assertion-error": "^1.1.0", "check-error": "^1.0.2", "deep-eql": "^4.1.2", "get-func-name": "^2.0.0", "loupe": "^2.3.1", "pathval": "^1.1.1", "type-detect": "^4.0.5"}, "engines": {"node": ">=4"}}, "node_modules/chalk": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "integrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==", "dev": true, "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/chardet": {"version": "0.7.0", "dev": true, "license": "MIT"}, "node_modules/check-error": {"version": "1.0.2", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/ci-info": {"version": "3.8.0", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/cli-boxes": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cli-cursor": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"restore-cursor": "^3.1.0"}, "engines": {"node": ">=8"}}, "node_modules/cli-spinners": {"version": "2.9.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cli-width": {"version": "4.0.0", "dev": true, "license": "ISC", "engines": {"node": ">= 12"}}, "node_modules/cliui": {"version": "7.0.4", "dev": true, "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^7.0.0"}}, "node_modules/cliui/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/cliui/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/cliui/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/cliui/node_modules/emoji-regex": {"version": "8.0.0", "dev": true, "license": "MIT"}, "node_modules/cliui/node_modules/string-width": {"version": "4.2.3", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/cliui/node_modules/wrap-ansi": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/clone": {"version": "1.0.4", "dev": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/color-convert": {"version": "1.9.3", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz", "integrity": "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==", "dev": true, "dependencies": {"color-name": "1.1.3"}}, "node_modules/color-name": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz", "integrity": "sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==", "dev": true}, "node_modules/colors": {"version": "1.2.5", "dev": true, "license": "MIT", "engines": {"node": ">=0.1.90"}}, "node_modules/combined-stream": {"version": "1.0.8", "dev": true, "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "10.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=14"}}, "node_modules/compare-func": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"array-ify": "^1.0.0", "dot-prop": "^5.1.0"}}, "node_modules/compare-func/node_modules/dot-prop": {"version": "5.3.0", "dev": true, "license": "MIT", "dependencies": {"is-obj": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/concat-map": {"version": "0.0.1", "dev": true, "license": "MIT"}, "node_modules/concat-stream": {"version": "2.0.0", "dev": true, "engines": ["node >= 6.0"], "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.0.2", "typedarray": "^0.0.6"}}, "node_modules/config-chain": {"version": "1.1.13", "dev": true, "license": "MIT", "dependencies": {"ini": "^1.3.4", "proto-list": "~1.2.1"}}, "node_modules/config-chain/node_modules/ini": {"version": "1.3.8", "dev": true, "license": "ISC"}, "node_modules/configstore": {"version": "6.0.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dot-prop": "^6.0.1", "graceful-fs": "^4.2.6", "unique-string": "^3.0.0", "write-file-atomic": "^3.0.3", "xdg-basedir": "^5.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/yeoman/configstore?sponsor=1"}}, "node_modules/conventional-changelog": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"conventional-changelog-angular": "^6.0.0", "conventional-changelog-atom": "^3.0.0", "conventional-changelog-codemirror": "^3.0.0", "conventional-changelog-conventionalcommits": "^6.0.0", "conventional-changelog-core": "^5.0.0", "conventional-changelog-ember": "^3.0.0", "conventional-changelog-eslint": "^4.0.0", "conventional-changelog-express": "^3.0.0", "conventional-changelog-jquery": "^4.0.0", "conventional-changelog-jshint": "^3.0.0", "conventional-changelog-preset-loader": "^3.0.0"}, "engines": {"node": ">=14"}}, "node_modules/conventional-changelog-angular": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"compare-func": "^2.0.0"}, "engines": {"node": ">=14"}}, "node_modules/conventional-changelog-atom": {"version": "3.0.0", "dev": true, "license": "ISC", "engines": {"node": ">=14"}}, "node_modules/conventional-changelog-codemirror": {"version": "3.0.0", "dev": true, "license": "ISC", "engines": {"node": ">=14"}}, "node_modules/conventional-changelog-conventionalcommits": {"version": "6.1.0", "dev": true, "license": "ISC", "dependencies": {"compare-func": "^2.0.0"}, "engines": {"node": ">=14"}}, "node_modules/conventional-changelog-core": {"version": "5.0.2", "dev": true, "license": "MIT", "dependencies": {"add-stream": "^1.0.0", "conventional-changelog-writer": "^6.0.0", "conventional-commits-parser": "^4.0.0", "dateformat": "^3.0.3", "get-pkg-repo": "^4.2.1", "git-raw-commits": "^3.0.0", "git-remote-origin-url": "^2.0.0", "git-semver-tags": "^5.0.0", "normalize-package-data": "^3.0.3", "read-pkg": "^3.0.0", "read-pkg-up": "^3.0.0"}, "engines": {"node": ">=14"}}, "node_modules/conventional-changelog-ember": {"version": "3.0.0", "dev": true, "license": "ISC", "engines": {"node": ">=14"}}, "node_modules/conventional-changelog-eslint": {"version": "4.0.0", "dev": true, "license": "ISC", "engines": {"node": ">=14"}}, "node_modules/conventional-changelog-express": {"version": "3.0.0", "dev": true, "license": "ISC", "engines": {"node": ">=14"}}, "node_modules/conventional-changelog-jquery": {"version": "4.0.0", "dev": true, "license": "ISC", "engines": {"node": ">=14"}}, "node_modules/conventional-changelog-jshint": {"version": "3.0.0", "dev": true, "license": "ISC", "dependencies": {"compare-func": "^2.0.0"}, "engines": {"node": ">=14"}}, "node_modules/conventional-changelog-preset-loader": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=14"}}, "node_modules/conventional-changelog-writer": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"conventional-commits-filter": "^3.0.0", "dateformat": "^3.0.3", "handlebars": "^4.7.7", "json-stringify-safe": "^5.0.1", "meow": "^8.1.2", "semver": "^7.0.0", "split": "^1.0.1"}, "bin": {"conventional-changelog-writer": "cli.js"}, "engines": {"node": ">=14"}}, "node_modules/conventional-changelog-writer/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/conventional-changelog-writer/node_modules/semver": {"version": "7.5.4", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/conventional-changelog-writer/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/conventional-commits-filter": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"lodash.ismatch": "^4.4.0", "modify-values": "^1.0.1"}, "engines": {"node": ">=14"}}, "node_modules/conventional-commits-parser": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"is-text-path": "^1.0.1", "JSONStream": "^1.3.5", "meow": "^8.1.2", "split2": "^3.2.2"}, "bin": {"conventional-commits-parser": "cli.js"}, "engines": {"node": ">=14"}}, "node_modules/conventional-recommended-bump": {"version": "7.0.1", "dev": true, "license": "MIT", "dependencies": {"concat-stream": "^2.0.0", "conventional-changelog-preset-loader": "^3.0.0", "conventional-commits-filter": "^3.0.0", "conventional-commits-parser": "^4.0.0", "git-raw-commits": "^3.0.0", "git-semver-tags": "^5.0.0", "meow": "^8.1.2"}, "bin": {"conventional-recommended-bump": "cli.js"}, "engines": {"node": ">=14"}}, "node_modules/convert-source-map": {"version": "1.9.0", "dev": true, "license": "MIT"}, "node_modules/core-js": {"version": "3.30.1", "hasInstallScript": true, "license": "MIT", "optional": true, "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/core-util-is": {"version": "1.0.3", "dev": true, "license": "MIT"}, "node_modules/cosmiconfig": {"version": "8.2.0", "dev": true, "license": "MIT", "dependencies": {"import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "parse-json": "^5.0.0", "path-type": "^4.0.0"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/d-fischer"}}, "node_modules/cross-spawn": {"version": "7.0.3", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/crypto-random-string": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^1.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/crypto-random-string/node_modules/type-fest": {"version": "1.4.0", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/css-line-break": {"version": "2.1.0", "license": "MIT", "optional": true, "dependencies": {"utrie": "^1.0.2"}}, "node_modules/csscolorparser": {"version": "1.0.3", "license": "MIT", "peer": true}, "node_modules/cssesc": {"version": "3.0.0", "dev": true, "license": "MIT", "bin": {"cssesc": "bin/cssesc"}, "engines": {"node": ">=4"}}, "node_modules/cssstyle": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"rrweb-cssom": "^0.6.0"}, "engines": {"node": ">=14"}}, "node_modules/csstype": {"version": "3.1.2", "dev": true, "license": "MIT"}, "node_modules/dargs": {"version": "7.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/data-uri-to-buffer": {"version": "4.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 12"}}, "node_modules/data-urls": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"abab": "^2.0.6", "whatwg-mimetype": "^3.0.0", "whatwg-url": "^12.0.0"}, "engines": {"node": ">=14"}}, "node_modules/data-urls/node_modules/tr46": {"version": "4.1.1", "dev": true, "license": "MIT", "dependencies": {"punycode": "^2.3.0"}, "engines": {"node": ">=14"}}, "node_modules/data-urls/node_modules/webidl-conversions": {"version": "7.0.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}}, "node_modules/data-urls/node_modules/whatwg-url": {"version": "12.0.1", "dev": true, "license": "MIT", "dependencies": {"tr46": "^4.1.1", "webidl-conversions": "^7.0.0"}, "engines": {"node": ">=14"}}, "node_modules/dateformat": {"version": "3.0.3", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/de-indent": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/debug": {"version": "4.3.4", "dev": true, "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decamelize": {"version": "1.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/decamelize-keys": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"decamelize": "^1.1.0", "map-obj": "^1.0.0"}, "engines": {"node": ">=0.10.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/decamelize-keys/node_modules/map-obj": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/decimal.js": {"version": "10.4.3", "dev": true, "license": "MIT"}, "node_modules/decompress-response": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"mimic-response": "^3.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/decompress-response/node_modules/mimic-response": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/deep-eql": {"version": "4.1.3", "dev": true, "license": "MIT", "dependencies": {"type-detect": "^4.0.0"}, "engines": {"node": ">=6"}}, "node_modules/deep-extend": {"version": "0.6.0", "dev": true, "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/deep-is": {"version": "0.1.4", "dev": true, "license": "MIT"}, "node_modules/default-browser": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"bundle-name": "^3.0.0", "default-browser-id": "^3.0.0", "execa": "^7.1.1", "titleize": "^3.0.0"}, "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/default-browser-id": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"bplist-parser": "^0.2.0", "untildify": "^4.0.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/default-browser/node_modules/execa": {"version": "7.1.1", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.1", "human-signals": "^4.3.0", "is-stream": "^3.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^5.1.0", "onetime": "^6.0.0", "signal-exit": "^3.0.7", "strip-final-newline": "^3.0.0"}, "engines": {"node": "^14.18.0 || ^16.14.0 || >=18.0.0"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/default-browser/node_modules/get-stream": {"version": "6.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/default-browser/node_modules/human-signals": {"version": "4.3.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=14.18.0"}}, "node_modules/default-browser/node_modules/is-stream": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/default-browser/node_modules/mimic-fn": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/default-browser/node_modules/npm-run-path": {"version": "5.1.0", "dev": true, "license": "MIT", "dependencies": {"path-key": "^4.0.0"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/default-browser/node_modules/onetime": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"mimic-fn": "^4.0.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/default-browser/node_modules/path-key": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/default-browser/node_modules/strip-final-newline": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/defaults": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"clone": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/defer-to-connect": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/define-lazy-prop": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/define-properties": {"version": "1.2.0", "dev": true, "license": "MIT", "dependencies": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/degenerator": {"version": "5.0.1", "dev": true, "license": "MIT", "dependencies": {"ast-types": "^0.13.4", "escodegen": "^2.1.0", "esprima": "^4.0.1"}, "engines": {"node": ">= 14"}}, "node_modules/delayed-stream": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/deprecation": {"version": "2.3.1", "dev": true, "license": "ISC"}, "node_modules/diff-sequences": {"version": "29.4.3", "dev": true, "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/dir-glob": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"path-type": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/doctrine": {"version": "3.0.0", "dev": true, "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/domexception": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"webidl-conversions": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/domexception/node_modules/webidl-conversions": {"version": "7.0.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}}, "node_modules/dompurify": {"version": "2.4.5", "license": "(MPL-2.0 OR Apache-2.0)", "optional": true}, "node_modules/dot-prop": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"is-obj": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/earcut": {"version": "2.2.4", "license": "ISC", "peer": true}, "node_modules/eastasianwidth": {"version": "0.2.0", "dev": true, "license": "MIT"}, "node_modules/editorconfig": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"@one-ini/wasm": "0.1.1", "commander": "^10.0.0", "minimatch": "9.0.1", "semver": "^7.5.3"}, "bin": {"editorconfig": "bin/editorconfig"}, "engines": {"node": ">=14"}}, "node_modules/editorconfig/node_modules/brace-expansion": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/editorconfig/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/editorconfig/node_modules/minimatch": {"version": "9.0.1", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/editorconfig/node_modules/semver": {"version": "7.5.4", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/editorconfig/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/electron-to-chromium": {"version": "1.4.495", "resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.4.495.tgz", "integrity": "sha512-mwknuemBZnoOCths4GtpU/SDuVMp3uQHKa2UNJT9/aVD6WVRjGpXOxRGX7lm6ILIenTdGXPSTCTDaWos5tEU8Q==", "dev": true, "peer": true}, "node_modules/emoji-regex": {"version": "9.2.2", "dev": true, "license": "MIT"}, "node_modules/end-of-stream": {"version": "1.4.4", "dev": true, "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/entities": {"version": "4.5.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/error-ex": {"version": "1.3.2", "dev": true, "license": "MIT", "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/es-abstract": {"version": "1.21.2", "dev": true, "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.0", "available-typed-arrays": "^1.0.5", "call-bind": "^1.0.2", "es-set-tostringtag": "^2.0.1", "es-to-primitive": "^1.2.1", "function.prototype.name": "^1.1.5", "get-intrinsic": "^1.2.0", "get-symbol-description": "^1.0.0", "globalthis": "^1.0.3", "gopd": "^1.0.1", "has": "^1.0.3", "has-property-descriptors": "^1.0.0", "has-proto": "^1.0.1", "has-symbols": "^1.0.3", "internal-slot": "^1.0.5", "is-array-buffer": "^3.0.2", "is-callable": "^1.2.7", "is-negative-zero": "^2.0.2", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.2", "is-string": "^1.0.7", "is-typed-array": "^1.1.10", "is-weakref": "^1.0.2", "object-inspect": "^1.12.3", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.4.3", "safe-regex-test": "^1.0.0", "string.prototype.trim": "^1.2.7", "string.prototype.trimend": "^1.0.6", "string.prototype.trimstart": "^1.0.6", "typed-array-length": "^1.0.4", "unbox-primitive": "^1.0.2", "which-typed-array": "^1.1.9"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es-array-method-boxes-properly": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/es-get-iterator": {"version": "1.1.3", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.3", "has-symbols": "^1.0.3", "is-arguments": "^1.1.1", "is-map": "^2.0.2", "is-set": "^2.0.2", "is-string": "^1.0.7", "isarray": "^2.0.5", "stop-iteration-iterator": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es-set-tostringtag": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"get-intrinsic": "^1.1.3", "has": "^1.0.3", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-to-primitive": {"version": "1.2.1", "dev": true, "license": "MIT", "dependencies": {"is-callable": "^1.1.4", "is-date-object": "^1.0.1", "is-symbol": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/esbuild": {"version": "0.18.11", "dev": true, "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=12"}, "optionalDependencies": {"@esbuild/android-arm": "0.18.11", "@esbuild/android-arm64": "0.18.11", "@esbuild/android-x64": "0.18.11", "@esbuild/darwin-arm64": "0.18.11", "@esbuild/darwin-x64": "0.18.11", "@esbuild/freebsd-arm64": "0.18.11", "@esbuild/freebsd-x64": "0.18.11", "@esbuild/linux-arm": "0.18.11", "@esbuild/linux-arm64": "0.18.11", "@esbuild/linux-ia32": "0.18.11", "@esbuild/linux-loong64": "0.18.11", "@esbuild/linux-mips64el": "0.18.11", "@esbuild/linux-ppc64": "0.18.11", "@esbuild/linux-riscv64": "0.18.11", "@esbuild/linux-s390x": "0.18.11", "@esbuild/linux-x64": "0.18.11", "@esbuild/netbsd-x64": "0.18.11", "@esbuild/openbsd-x64": "0.18.11", "@esbuild/sunos-x64": "0.18.11", "@esbuild/win32-arm64": "0.18.11", "@esbuild/win32-ia32": "0.18.11", "@esbuild/win32-x64": "0.18.11"}}, "node_modules/escalade": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-goat": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/escape-string-regexp": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==", "dev": true, "engines": {"node": ">=0.8.0"}}, "node_modules/escodegen": {"version": "2.1.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esprima": "^4.0.1", "estraverse": "^5.2.0", "esutils": "^2.0.2"}, "bin": {"escodegen": "bin/escodegen.js", "esgenerate": "bin/esgenerate.js"}, "engines": {"node": ">=6.0"}, "optionalDependencies": {"source-map": "~0.6.1"}}, "node_modules/escodegen/node_modules/estraverse": {"version": "5.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/eslint": {"version": "8.45.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.4.0", "@eslint/eslintrc": "^2.1.0", "@eslint/js": "8.44.0", "@humanwhocodes/config-array": "^0.11.10", "@humanwhocodes/module-importer": "^1.0.1", "@nodelib/fs.walk": "^1.2.8", "ajv": "^6.10.0", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.3.2", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.2.0", "eslint-visitor-keys": "^3.4.1", "espree": "^9.6.0", "esquery": "^1.4.2", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "globals": "^13.19.0", "graphemer": "^1.4.0", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "is-path-inside": "^3.0.3", "js-yaml": "^4.1.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3", "strip-ansi": "^6.0.1", "text-table": "^0.2.0"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-config-prettier": {"version": "8.8.0", "dev": true, "license": "MIT", "bin": {"eslint-config-prettier": "bin/cli.js"}, "peerDependencies": {"eslint": ">=7.0.0"}}, "node_modules/eslint-plugin-prettier": {"version": "4.2.1", "dev": true, "license": "MIT", "dependencies": {"prettier-linter-helpers": "^1.0.0"}, "engines": {"node": ">=12.0.0"}, "peerDependencies": {"eslint": ">=7.28.0", "prettier": ">=2.0.0"}, "peerDependenciesMeta": {"eslint-config-prettier": {"optional": true}}}, "node_modules/eslint-plugin-vue": {"version": "9.15.1", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.3.0", "natural-compare": "^1.4.0", "nth-check": "^2.0.1", "postcss-selector-parser": "^6.0.9", "semver": "^7.3.5", "vue-eslint-parser": "^9.3.0", "xml-name-validator": "^4.0.0"}, "engines": {"node": "^14.17.0 || >=16.0.0"}, "peerDependencies": {"eslint": "^6.2.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/eslint-plugin-vue/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/eslint-plugin-vue/node_modules/semver": {"version": "7.4.0", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/eslint-plugin-vue/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/eslint-scope": {"version": "5.1.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/eslint-visitor-keys": {"version": "2.1.0", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=10"}}, "node_modules/eslint/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/eslint/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/eslint/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/eslint/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/eslint/node_modules/escape-string-regexp": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint/node_modules/eslint-scope": {"version": "7.2.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint/node_modules/eslint-visitor-keys": {"version": "3.4.1", "dev": true, "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint/node_modules/estraverse": {"version": "5.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/eslint/node_modules/globals": {"version": "13.20.0", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/eslint/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/espree": {"version": "9.6.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.9.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/espree/node_modules/eslint-visitor-keys": {"version": "3.4.1", "dev": true, "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/esprima": {"version": "4.0.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/esquery": {"version": "1.5.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esquery/node_modules/estraverse": {"version": "5.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/esrecurse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/esrecurse/node_modules/estraverse": {"version": "5.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/estree-walker": {"version": "2.0.2", "dev": true, "license": "MIT"}, "node_modules/esutils": {"version": "2.0.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/execa": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.0", "get-stream": "^5.0.0", "human-signals": "^1.1.1", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "onetime": "^5.1.0", "signal-exit": "^3.0.2", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/external-editor": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"chardet": "^0.7.0", "iconv-lite": "^0.4.24", "tmp": "^0.0.33"}, "engines": {"node": ">=4"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "dev": true, "license": "MIT"}, "node_modules/fast-diff": {"version": "1.2.0", "dev": true, "license": "Apache-2.0"}, "node_modules/fast-glob": {"version": "3.3.0", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-glob/node_modules/glob-parent": {"version": "5.1.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "dev": true, "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "dev": true, "license": "MIT"}, "node_modules/fastq": {"version": "1.15.0", "dev": true, "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/fetch-blob": {"version": "3.2.0", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "license": "MIT", "dependencies": {"node-domexception": "^1.0.0", "web-streams-polyfill": "^3.0.3"}, "engines": {"node": "^12.20 || >= 14.13"}}, "node_modules/fflate": {"version": "0.4.8", "license": "MIT"}, "node_modules/figures": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"escape-string-regexp": "^5.0.0", "is-unicode-supported": "^1.2.0"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/figures/node_modules/escape-string-regexp": {"version": "5.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/file-entry-cache": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"flat-cache": "^3.0.4"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/file-saver": {"version": "2.0.5", "license": "MIT"}, "node_modules/fill-range": {"version": "7.0.1", "dev": true, "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/find-up": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/flat-cache": {"version": "3.0.4", "dev": true, "license": "MIT", "dependencies": {"flatted": "^3.1.0", "rimraf": "^3.0.2"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/flatted": {"version": "3.2.7", "dev": true, "license": "ISC"}, "node_modules/focus-trap": {"version": "7.5.2", "resolved": "https://registry.npmjs.org/focus-trap/-/focus-trap-7.5.2.tgz", "integrity": "sha512-p6vGNNWLDGwJCiEjkSK6oERj/hEyI9ITsSwIUICBoKLlWiTWXJRfQibCwcoi50rTZdbi87qDtUlMCmQwsGSgPw==", "dev": true, "dependencies": {"tabbable": "^6.2.0"}}, "node_modules/follow-redirects": {"version": "1.5.10", "dev": true, "license": "MIT", "dependencies": {"debug": "=3.1.0"}, "engines": {"node": ">=4.0"}}, "node_modules/follow-redirects/node_modules/debug": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/follow-redirects/node_modules/ms": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/for-each": {"version": "0.3.3", "dev": true, "license": "MIT", "dependencies": {"is-callable": "^1.1.3"}}, "node_modules/form-data": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/form-data-encoder": {"version": "2.1.4", "dev": true, "license": "MIT", "engines": {"node": ">= 14.17"}}, "node_modules/formdata-polyfill": {"version": "4.0.10", "dev": true, "license": "MIT", "dependencies": {"fetch-blob": "^3.1.2"}, "engines": {"node": ">=12.20.0"}}, "node_modules/fs-extra": {"version": "8.1.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}, "engines": {"node": ">=6 <7 || >=8"}}, "node_modules/fs.realpath": {"version": "1.0.0", "dev": true, "license": "ISC"}, "node_modules/function-bind": {"version": "1.1.1", "dev": true, "license": "MIT"}, "node_modules/function.prototype.name": {"version": "1.1.5", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.3", "es-abstract": "^1.19.0", "functions-have-names": "^1.2.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/functions-have-names": {"version": "1.2.3", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gensync": {"version": "1.0.0-beta.2", "resolved": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz", "integrity": "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==", "dev": true, "peer": true, "engines": {"node": ">=6.9.0"}}, "node_modules/geotiff": {"version": "2.0.7", "license": "MIT", "peer": true, "dependencies": {"@petamoriken/float16": "^3.4.7", "lerc": "^3.0.0", "pako": "^2.0.4", "parse-headers": "^2.0.2", "quick-lru": "^6.1.1", "web-worker": "^1.2.0", "xml-utils": "^1.0.2"}, "engines": {"node": ">=10.19"}}, "node_modules/get-caller-file": {"version": "2.0.5", "dev": true, "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-func-name": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/get-intrinsic": {"version": "1.2.0", "dev": true, "license": "MIT", "dependencies": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-pkg": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"axios": "^0.18.0"}, "engines": {"node": ">=4"}}, "node_modules/get-pkg-repo": {"version": "4.2.1", "dev": true, "license": "MIT", "dependencies": {"@hutson/parse-repository-url": "^3.0.0", "hosted-git-info": "^4.0.0", "through2": "^2.0.0", "yargs": "^16.2.0"}, "bin": {"get-pkg-repo": "src/cli.js"}, "engines": {"node": ">=6.9.0"}}, "node_modules/get-repository-url": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"get-pkg": "^1.0.0", "parse-github-url": "^1.0.2"}, "bin": {"get-repository-url": "bin/cli.js"}, "engines": {"node": ">=4"}}, "node_modules/get-stream": {"version": "5.2.0", "dev": true, "license": "MIT", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/get-symbol-description": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-uri": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"basic-ftp": "^5.0.2", "data-uri-to-buffer": "^5.0.1", "debug": "^4.3.4", "fs-extra": "^8.1.0"}, "engines": {"node": ">= 14"}}, "node_modules/get-uri/node_modules/data-uri-to-buffer": {"version": "5.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 14"}}, "node_modules/git-raw-commits": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"dargs": "^7.0.0", "meow": "^8.1.2", "split2": "^3.2.2"}, "bin": {"git-raw-commits": "cli.js"}, "engines": {"node": ">=14"}}, "node_modules/git-remote-origin-url": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"gitconfiglocal": "^1.0.0", "pify": "^2.3.0"}, "engines": {"node": ">=4"}}, "node_modules/git-semver-tags": {"version": "5.0.1", "dev": true, "license": "MIT", "dependencies": {"meow": "^8.1.2", "semver": "^7.0.0"}, "bin": {"git-semver-tags": "cli.js"}, "engines": {"node": ">=14"}}, "node_modules/git-semver-tags/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/git-semver-tags/node_modules/semver": {"version": "7.5.4", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/git-semver-tags/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/git-up": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"is-ssh": "^1.4.0", "parse-url": "^8.1.0"}}, "node_modules/git-url-parse": {"version": "13.1.0", "dev": true, "license": "MIT", "dependencies": {"git-up": "^7.0.0"}}, "node_modules/gitconfiglocal": {"version": "1.0.0", "dev": true, "license": "BSD", "dependencies": {"ini": "^1.3.2"}}, "node_modules/gitconfiglocal/node_modules/ini": {"version": "1.3.8", "dev": true, "license": "ISC"}, "node_modules/glob": {"version": "7.2.3", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "6.0.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/global-dirs": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"ini": "2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/globals": {"version": "11.12.0", "resolved": "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz", "integrity": "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==", "dev": true, "peer": true, "engines": {"node": ">=4"}}, "node_modules/globalthis": {"version": "1.0.3", "dev": true, "license": "MIT", "dependencies": {"define-properties": "^1.1.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/globby": {"version": "11.1.0", "dev": true, "license": "MIT", "dependencies": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.2.9", "ignore": "^5.2.0", "merge2": "^1.4.1", "slash": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/gopd": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"get-intrinsic": "^1.1.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/got": {"version": "12.6.0", "dev": true, "license": "MIT", "dependencies": {"@sindresorhus/is": "^5.2.0", "@szmarczak/http-timer": "^5.0.1", "cacheable-lookup": "^7.0.0", "cacheable-request": "^10.2.8", "decompress-response": "^6.0.0", "form-data-encoder": "^2.1.2", "get-stream": "^6.0.1", "http2-wrapper": "^2.1.10", "lowercase-keys": "^3.0.0", "p-cancelable": "^3.0.0", "responselike": "^3.0.0"}, "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sindresorhus/got?sponsor=1"}}, "node_modules/got/node_modules/get-stream": {"version": "6.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/graceful-fs": {"version": "4.2.11", "dev": true, "license": "ISC"}, "node_modules/grapheme-splitter": {"version": "1.0.4", "dev": true, "license": "MIT"}, "node_modules/graphemer": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/handlebars": {"version": "4.7.7", "dev": true, "license": "MIT", "dependencies": {"minimist": "^1.2.5", "neo-async": "^2.6.0", "source-map": "^0.6.1", "wordwrap": "^1.0.0"}, "bin": {"handlebars": "bin/handlebars"}, "engines": {"node": ">=0.4.7"}, "optionalDependencies": {"uglify-js": "^3.1.4"}}, "node_modules/hard-rejection": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/has": {"version": "1.0.3", "dev": true, "license": "MIT", "dependencies": {"function-bind": "^1.1.1"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/has-bigints": {"version": "1.0.2", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-flag": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==", "dev": true, "engines": {"node": ">=4"}}, "node_modules/has-property-descriptors": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"get-intrinsic": "^1.1.1"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-proto": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.0.3", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"has-symbols": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-yarn": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/he": {"version": "1.2.0", "dev": true, "license": "MIT", "bin": {"he": "bin/he"}}, "node_modules/hosted-git-info": {"version": "4.1.0", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "engines": {"node": ">=10"}}, "node_modules/hosted-git-info/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/hosted-git-info/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/html-encoding-sniffer": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"whatwg-encoding": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/html-escaper": {"version": "2.0.2", "dev": true, "license": "MIT"}, "node_modules/html2canvas": {"version": "1.4.1", "license": "MIT", "optional": true, "dependencies": {"css-line-break": "^2.1.0", "text-segmentation": "^1.0.3"}, "engines": {"node": ">=8.0.0"}}, "node_modules/http-cache-semantics": {"version": "4.1.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/http-proxy-agent": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"agent-base": "^7.1.0", "debug": "^4.3.4"}, "engines": {"node": ">= 14"}}, "node_modules/http-proxy-agent/node_modules/agent-base": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.3.4"}, "engines": {"node": ">= 14"}}, "node_modules/http2-wrapper": {"version": "2.2.0", "dev": true, "license": "MIT", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.2.0"}, "engines": {"node": ">=10.19.0"}}, "node_modules/http2-wrapper/node_modules/quick-lru": {"version": "5.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/https-proxy-agent": {"version": "5.0.1", "dev": true, "license": "MIT", "dependencies": {"agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/human-signals": {"version": "1.1.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=8.12.0"}}, "node_modules/husky": {"version": "8.0.3", "dev": true, "license": "MIT", "bin": {"husky": "lib/bin.js"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/typicode"}}, "node_modules/iconv-lite": {"version": "0.4.24", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ieee754": {"version": "1.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ignore": {"version": "5.2.4", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/import-fresh": {"version": "3.3.0", "dev": true, "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/import-lazy": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/imurmurhash": {"version": "0.1.4", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/indent-string": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/inflight": {"version": "1.0.6", "dev": true, "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "dev": true, "license": "ISC"}, "node_modules/ini": {"version": "2.0.0", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/inquirer": {"version": "9.2.8", "dev": true, "license": "MIT", "dependencies": {"ansi-escapes": "^4.3.2", "chalk": "^5.3.0", "cli-cursor": "^3.1.0", "cli-width": "^4.0.0", "external-editor": "^3.0.3", "figures": "^5.0.0", "lodash": "^4.17.21", "mute-stream": "1.0.0", "ora": "^5.4.1", "run-async": "^3.0.0", "rxjs": "^7.8.1", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "through": "^2.3.6", "wrap-ansi": "^6.0.1"}, "engines": {"node": ">=14.18.0"}}, "node_modules/inquirer/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/inquirer/node_modules/bl": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}}, "node_modules/inquirer/node_modules/buffer": {"version": "5.7.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "node_modules/inquirer/node_modules/chalk": {"version": "5.3.0", "dev": true, "license": "MIT", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/inquirer/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/inquirer/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/inquirer/node_modules/emoji-regex": {"version": "8.0.0", "dev": true, "license": "MIT"}, "node_modules/inquirer/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/inquirer/node_modules/is-interactive": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/inquirer/node_modules/is-unicode-supported": {"version": "0.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/inquirer/node_modules/log-symbols": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.1.0", "is-unicode-supported": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/inquirer/node_modules/log-symbols/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/inquirer/node_modules/ora": {"version": "5.4.1", "dev": true, "license": "MIT", "dependencies": {"bl": "^4.1.0", "chalk": "^4.1.0", "cli-cursor": "^3.1.0", "cli-spinners": "^2.5.0", "is-interactive": "^1.0.0", "is-unicode-supported": "^0.1.0", "log-symbols": "^4.1.0", "strip-ansi": "^6.0.0", "wcwidth": "^1.0.1"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/inquirer/node_modules/ora/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/inquirer/node_modules/string-width": {"version": "4.2.3", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/inquirer/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/inquirer/node_modules/wrap-ansi": {"version": "6.2.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=8"}}, "node_modules/internal-slot": {"version": "1.0.5", "dev": true, "license": "MIT", "dependencies": {"get-intrinsic": "^1.2.0", "has": "^1.0.3", "side-channel": "^1.0.4"}, "engines": {"node": ">= 0.4"}}, "node_modules/interpret": {"version": "1.4.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/ip": {"version": "1.1.8", "dev": true, "license": "MIT"}, "node_modules/is-arguments": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-array-buffer": {"version": "3.0.2", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.2.0", "is-typed-array": "^1.1.10"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-arrayish": {"version": "0.2.1", "dev": true, "license": "MIT"}, "node_modules/is-bigint": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"has-bigints": "^1.0.1"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-boolean-object": {"version": "1.1.2", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-buffer": {"version": "2.0.5", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/is-callable": {"version": "1.2.7", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-ci": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"ci-info": "^3.2.0"}, "bin": {"is-ci": "bin.js"}}, "node_modules/is-core-module": {"version": "2.12.0", "dev": true, "license": "MIT", "dependencies": {"has": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-date-object": {"version": "1.0.5", "dev": true, "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-docker": {"version": "3.0.0", "dev": true, "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-extglob": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-glob": {"version": "4.0.3", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-inside-container": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"is-docker": "^3.0.0"}, "bin": {"is-inside-container": "cli.js"}, "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-installed-globally": {"version": "0.4.0", "dev": true, "license": "MIT", "dependencies": {"global-dirs": "^3.0.0", "is-path-inside": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-interactive": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-map": {"version": "2.0.2", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-negative-zero": {"version": "2.0.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-npm": {"version": "6.0.0", "dev": true, "license": "MIT", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-number": {"version": "7.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-number-object": {"version": "1.0.7", "dev": true, "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-obj": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-path-inside": {"version": "3.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-plain-obj": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-plain-object": {"version": "5.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-potential-custom-element-name": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/is-regex": {"version": "1.1.4", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-set": {"version": "2.0.2", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-shared-array-buffer": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-ssh": {"version": "1.4.0", "dev": true, "license": "MIT", "dependencies": {"protocols": "^2.0.1"}}, "node_modules/is-stream": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-string": {"version": "1.0.7", "dev": true, "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-symbol": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"has-symbols": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-text-path": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"text-extensions": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-typed-array": {"version": "1.1.10", "dev": true, "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.5", "call-bind": "^1.0.2", "for-each": "^0.3.3", "gopd": "^1.0.1", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-typedarray": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/is-unicode-supported": {"version": "1.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-weakref": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-wsl": {"version": "2.2.0", "dev": true, "license": "MIT", "dependencies": {"is-docker": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-wsl/node_modules/is-docker": {"version": "2.2.1", "dev": true, "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-yarn-global": {"version": "0.4.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}}, "node_modules/isarray": {"version": "2.0.5", "dev": true, "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "dev": true, "license": "ISC"}, "node_modules/issue-parser": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"lodash.capitalize": "^4.2.1", "lodash.escaperegexp": "^4.1.2", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.uniqby": "^4.7.0"}, "engines": {"node": ">=10.13"}}, "node_modules/istanbul-lib-coverage": {"version": "3.2.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-report": {"version": "3.0.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"istanbul-lib-coverage": "^3.0.0", "make-dir": "^3.0.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-report/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-report/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-source-maps": {"version": "4.0.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"debug": "^4.1.1", "istanbul-lib-coverage": "^3.0.0", "source-map": "^0.6.1"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-reports": {"version": "3.1.5", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/iterate-iterator": {"version": "1.0.2", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/iterate-value": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"es-get-iterator": "^1.0.2", "iterate-iterator": "^1.0.1"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/jju": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/js-beautify": {"version": "1.14.9", "dev": true, "license": "MIT", "dependencies": {"config-chain": "^1.1.13", "editorconfig": "^1.0.3", "glob": "^8.1.0", "nopt": "^6.0.0"}, "bin": {"css-beautify": "js/bin/css-beautify.js", "html-beautify": "js/bin/html-beautify.js", "js-beautify": "js/bin/js-beautify.js"}, "engines": {"node": ">=12"}}, "node_modules/js-beautify/node_modules/brace-expansion": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/js-beautify/node_modules/glob": {"version": "8.1.0", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^5.0.1", "once": "^1.3.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/js-beautify/node_modules/minimatch": {"version": "5.1.6", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==", "dev": true}, "node_modules/js-yaml": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsdom": {"version": "22.1.0", "dev": true, "license": "MIT", "dependencies": {"abab": "^2.0.6", "cssstyle": "^3.0.0", "data-urls": "^4.0.0", "decimal.js": "^10.4.3", "domexception": "^4.0.0", "form-data": "^4.0.0", "html-encoding-sniffer": "^3.0.0", "http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.1", "is-potential-custom-element-name": "^1.0.1", "nwsapi": "^2.2.4", "parse5": "^7.1.2", "rrweb-cssom": "^0.6.0", "saxes": "^6.0.0", "symbol-tree": "^3.2.4", "tough-cookie": "^4.1.2", "w3c-xmlserializer": "^4.0.0", "webidl-conversions": "^7.0.0", "whatwg-encoding": "^2.0.0", "whatwg-mimetype": "^3.0.0", "whatwg-url": "^12.0.1", "ws": "^8.13.0", "xml-name-validator": "^4.0.0"}, "engines": {"node": ">=16"}, "peerDependencies": {"canvas": "^2.5.0"}, "peerDependenciesMeta": {"canvas": {"optional": true}}}, "node_modules/jsdom/node_modules/@tootallnate/once": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/jsdom/node_modules/http-proxy-agent": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"@tootallnate/once": "2", "agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/jsdom/node_modules/tr46": {"version": "4.1.1", "dev": true, "license": "MIT", "dependencies": {"punycode": "^2.3.0"}, "engines": {"node": ">=14"}}, "node_modules/jsdom/node_modules/webidl-conversions": {"version": "7.0.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}}, "node_modules/jsdom/node_modules/whatwg-url": {"version": "12.0.1", "dev": true, "license": "MIT", "dependencies": {"tr46": "^4.1.1", "webidl-conversions": "^7.0.0"}, "engines": {"node": ">=14"}}, "node_modules/jsesc": {"version": "2.5.2", "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-2.5.2.tgz", "integrity": "sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==", "dev": true, "peer": true, "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=4"}}, "node_modules/json-buffer": {"version": "3.0.1", "dev": true, "license": "MIT"}, "node_modules/json-parse-better-errors": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/json-parse-even-better-errors": {"version": "2.3.1", "dev": true, "license": "MIT"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "dev": true, "license": "MIT"}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/json-stringify-pretty-compact": {"version": "2.0.0", "license": "MIT", "peer": true}, "node_modules/json-stringify-safe": {"version": "5.0.1", "dev": true, "license": "ISC"}, "node_modules/json5": {"version": "2.2.3", "resolved": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "integrity": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==", "dev": true, "peer": true, "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/jsonc-parser": {"version": "3.2.0", "dev": true, "license": "MIT"}, "node_modules/jsonfile": {"version": "4.0.0", "dev": true, "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/jsonparse": {"version": "1.3.1", "dev": true, "engines": ["node >= 0.2.0"], "license": "MIT"}, "node_modules/JSONStream": {"version": "1.3.5", "dev": true, "license": "(MIT OR Apache-2.0)", "dependencies": {"jsonparse": "^1.2.0", "through": ">=2.2.7 <3"}, "bin": {"JSONStream": "bin.js"}, "engines": {"node": "*"}}, "node_modules/jspdf": {"version": "2.5.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.14.0", "atob": "^2.1.2", "btoa": "^1.2.1", "fflate": "^0.4.8"}, "optionalDependencies": {"canvg": "^3.0.6", "core-js": "^3.6.0", "dompurify": "^2.2.0", "html2canvas": "^1.0.0-rc.5"}}, "node_modules/keyv": {"version": "4.5.2", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/kind-of": {"version": "6.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/kolorist": {"version": "1.8.0", "dev": true, "license": "MIT"}, "node_modules/latest-version": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"package-json": "^8.1.0"}, "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lerc": {"version": "3.0.0", "license": "Apache-2.0", "peer": true}, "node_modules/levn": {"version": "0.4.1", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/lines-and-columns": {"version": "1.2.4", "dev": true, "license": "MIT"}, "node_modules/load-json-file": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.1.2", "parse-json": "^4.0.0", "pify": "^3.0.0", "strip-bom": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/load-json-file/node_modules/parse-json": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"error-ex": "^1.3.1", "json-parse-better-errors": "^1.0.1"}, "engines": {"node": ">=4"}}, "node_modules/load-json-file/node_modules/pify": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/local-pkg": {"version": "0.4.3", "dev": true, "license": "MIT", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/locate-path": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lodash": {"version": "4.17.21", "dev": true, "license": "MIT"}, "node_modules/lodash.capitalize": {"version": "4.2.1", "dev": true, "license": "MIT"}, "node_modules/lodash.escaperegexp": {"version": "4.1.2", "dev": true, "license": "MIT"}, "node_modules/lodash.get": {"version": "4.4.2", "dev": true, "license": "MIT"}, "node_modules/lodash.isequal": {"version": "4.5.0", "dev": true, "license": "MIT"}, "node_modules/lodash.ismatch": {"version": "4.4.0", "dev": true, "license": "MIT"}, "node_modules/lodash.isplainobject": {"version": "4.0.6", "dev": true, "license": "MIT"}, "node_modules/lodash.isstring": {"version": "4.0.1", "dev": true, "license": "MIT"}, "node_modules/lodash.merge": {"version": "4.6.2", "dev": true, "license": "MIT"}, "node_modules/lodash.uniqby": {"version": "4.7.0", "dev": true, "license": "MIT"}, "node_modules/log-symbols": {"version": "5.1.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^5.0.0", "is-unicode-supported": "^1.1.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/log-symbols/node_modules/chalk": {"version": "5.3.0", "dev": true, "license": "MIT", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/loupe": {"version": "2.3.6", "dev": true, "license": "MIT", "dependencies": {"get-func-name": "^2.0.0"}}, "node_modules/lowercase-keys": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lru-cache": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz", "integrity": "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==", "dev": true, "peer": true, "dependencies": {"yallist": "^3.0.2"}}, "node_modules/macos-release": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/magic-string": {"version": "0.30.1", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "engines": {"node": ">=12"}}, "node_modules/make-dir": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"semver": "^6.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/map-obj": {"version": "4.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/mapbox-to-css-font": {"version": "2.4.2", "license": "BSD-2-<PERSON><PERSON>", "peer": true}, "node_modules/mark.js": {"version": "8.11.1", "dev": true, "license": "MIT"}, "node_modules/meow": {"version": "8.1.2", "dev": true, "license": "MIT", "dependencies": {"@types/minimist": "^1.2.0", "camelcase-keys": "^6.2.2", "decamelize-keys": "^1.1.0", "hard-rejection": "^2.1.0", "minimist-options": "4.1.0", "normalize-package-data": "^3.0.0", "read-pkg-up": "^7.0.1", "redent": "^3.0.0", "trim-newlines": "^3.0.0", "type-fest": "^0.18.0", "yargs-parser": "^20.2.3"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/meow/node_modules/find-up": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/meow/node_modules/hosted-git-info": {"version": "2.8.9", "dev": true, "license": "ISC"}, "node_modules/meow/node_modules/locate-path": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/meow/node_modules/p-limit": {"version": "2.3.0", "dev": true, "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/meow/node_modules/p-locate": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/meow/node_modules/read-pkg": {"version": "5.2.0", "dev": true, "license": "MIT", "dependencies": {"@types/normalize-package-data": "^2.4.0", "normalize-package-data": "^2.5.0", "parse-json": "^5.0.0", "type-fest": "^0.6.0"}, "engines": {"node": ">=8"}}, "node_modules/meow/node_modules/read-pkg-up": {"version": "7.0.1", "dev": true, "license": "MIT", "dependencies": {"find-up": "^4.1.0", "read-pkg": "^5.2.0", "type-fest": "^0.8.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/meow/node_modules/read-pkg-up/node_modules/type-fest": {"version": "0.8.1", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=8"}}, "node_modules/meow/node_modules/read-pkg/node_modules/normalize-package-data": {"version": "2.5.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "node_modules/meow/node_modules/read-pkg/node_modules/type-fest": {"version": "0.6.0", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=8"}}, "node_modules/meow/node_modules/semver": {"version": "5.7.2", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/meow/node_modules/type-fest": {"version": "0.18.1", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/meow/node_modules/yargs-parser": {"version": "20.2.9", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/merge-stream": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/merge2": {"version": "1.4.1", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/mgrs": {"version": "1.0.0", "license": "MIT"}, "node_modules/micromatch": {"version": "4.0.5", "dev": true, "license": "MIT", "dependencies": {"braces": "^3.0.2", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mime-db": {"version": "1.52.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "dev": true, "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-fn": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/mimic-response": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/min-indent": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/minimatch": {"version": "3.1.2", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.8", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/minimist-options": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"arrify": "^1.0.1", "is-plain-obj": "^1.1.0", "kind-of": "^6.0.3"}, "engines": {"node": ">= 6"}}, "node_modules/minimist-options/node_modules/arrify": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/minisearch": {"version": "6.1.0", "dev": true, "license": "MIT"}, "node_modules/mlly": {"version": "1.4.0", "dev": true, "license": "MIT", "dependencies": {"acorn": "^8.9.0", "pathe": "^1.1.1", "pkg-types": "^1.0.3", "ufo": "^1.1.2"}}, "node_modules/modify-values": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/mri": {"version": "1.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/ms": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/muggle-string": {"version": "0.3.1", "dev": true, "license": "MIT"}, "node_modules/multimatch": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"@types/minimatch": "^3.0.3", "array-differ": "^3.0.0", "array-union": "^2.1.0", "arrify": "^2.0.1", "minimatch": "^3.0.4"}, "engines": {"node": ">=8"}}, "node_modules/mute-stream": {"version": "1.0.0", "dev": true, "license": "ISC", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/nanoid": {"version": "3.3.6", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/natural-compare": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/natural-compare-lite": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/neo-async": {"version": "2.6.2", "dev": true, "license": "MIT"}, "node_modules/netmask": {"version": "2.0.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/new-github-release-url": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^2.5.1"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/new-github-release-url/node_modules/type-fest": {"version": "2.19.0", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=12.20"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/node-domexception": {"version": "1.0.0", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "github", "url": "https://paypal.me/jimmywarting"}], "license": "MIT", "engines": {"node": ">=10.5.0"}}, "node_modules/node-fetch": {"version": "3.3.1", "dev": true, "license": "MIT", "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}}, "node_modules/node-releases": {"version": "2.0.13", "resolved": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.13.tgz", "integrity": "sha512-uYr7J37ae/ORWdZeQ1xxMJe3NtdmqMC/JZK+geofDrkLUApKRHPd18/TxtBOJ4A0/+uUIliorNrfYV6s1b02eQ==", "dev": true, "peer": true}, "node_modules/nopt": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"abbrev": "^1.0.0"}, "bin": {"nopt": "bin/nopt.js"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/normalize-package-data": {"version": "3.0.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"hosted-git-info": "^4.0.1", "is-core-module": "^2.5.0", "semver": "^7.3.4", "validate-npm-package-license": "^3.0.1"}, "engines": {"node": ">=10"}}, "node_modules/normalize-package-data/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/normalize-package-data/node_modules/semver": {"version": "7.5.4", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/normalize-package-data/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/normalize-url": {"version": "8.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/npm-run-path": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/nth-check": {"version": "2.1.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0"}, "funding": {"url": "https://github.com/fb55/nth-check?sponsor=1"}}, "node_modules/nwsapi": {"version": "2.2.4", "dev": true, "license": "MIT"}, "node_modules/object-inspect": {"version": "1.12.3", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-keys": {"version": "1.1.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/object.assign": {"version": "4.1.4", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "has-symbols": "^1.0.3", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/ol": {"version": "7.5.1", "license": "BSD-2-<PERSON><PERSON>", "peer": true, "dependencies": {"earcut": "^2.2.3", "geotiff": "^2.0.7", "ol-mapbox-style": "^10.1.0", "pbf": "3.2.1", "rbush": "^3.0.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/openlayers"}}, "node_modules/ol-contextmenu": {"version": "5.2.1", "license": "MIT", "peer": true, "dependencies": {"tiny-emitter": "^2.1.0"}, "engines": {"node": "^16 || ^18", "npm": ">=8"}, "peerDependencies": {"ol": "^7.1.0"}}, "node_modules/ol-ext": {"version": "4.0.11", "license": "BSD-3-<PERSON><PERSON>", "peer": true, "peerDependencies": {"ol": ">= 5.3.0"}}, "node_modules/ol-mapbox-style": {"version": "10.7.0", "license": "BSD-2-<PERSON><PERSON>", "peer": true, "dependencies": {"@mapbox/mapbox-gl-style-spec": "^13.23.1", "mapbox-to-css-font": "^2.4.1", "ol": "^7.3.0"}}, "node_modules/once": {"version": "1.4.0", "dev": true, "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/onetime": {"version": "5.1.2", "dev": true, "license": "MIT", "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/open": {"version": "9.1.0", "dev": true, "license": "MIT", "dependencies": {"default-browser": "^4.0.0", "define-lazy-prop": "^3.0.0", "is-inside-container": "^1.0.0", "is-wsl": "^2.2.0"}, "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/optionator": {"version": "0.9.3", "dev": true, "license": "MIT", "dependencies": {"@aashutoshrathi/word-wrap": "^1.2.3", "deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/ora": {"version": "6.3.1", "dev": true, "license": "MIT", "dependencies": {"chalk": "^5.0.0", "cli-cursor": "^4.0.0", "cli-spinners": "^2.6.1", "is-interactive": "^2.0.0", "is-unicode-supported": "^1.1.0", "log-symbols": "^5.1.0", "stdin-discarder": "^0.1.0", "strip-ansi": "^7.0.1", "wcwidth": "^1.0.1"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ora/node_modules/ansi-regex": {"version": "6.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/ora/node_modules/chalk": {"version": "5.3.0", "dev": true, "license": "MIT", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/ora/node_modules/cli-cursor": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"restore-cursor": "^4.0.0"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ora/node_modules/restore-cursor": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ora/node_modules/strip-ansi": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/os-name": {"version": "5.1.0", "dev": true, "license": "MIT", "dependencies": {"macos-release": "^3.1.0", "windows-release": "^5.0.1"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/os-tmpdir": {"version": "1.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/p-cancelable": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12.20"}}, "node_modules/p-limit": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-try": {"version": "2.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/pac-proxy-agent": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"@tootallnate/quickjs-emscripten": "^0.23.0", "agent-base": "^7.0.2", "debug": "^4.3.4", "get-uri": "^6.0.1", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.0", "pac-resolver": "^7.0.0", "socks-proxy-agent": "^8.0.1"}, "engines": {"node": ">= 14"}}, "node_modules/pac-proxy-agent/node_modules/agent-base": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.3.4"}, "engines": {"node": ">= 14"}}, "node_modules/pac-proxy-agent/node_modules/https-proxy-agent": {"version": "7.0.1", "dev": true, "license": "MIT", "dependencies": {"agent-base": "^7.0.2", "debug": "4"}, "engines": {"node": ">= 14"}}, "node_modules/pac-resolver": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"degenerator": "^5.0.0", "ip": "^1.1.8", "netmask": "^2.0.2"}, "engines": {"node": ">= 14"}}, "node_modules/package-json": {"version": "8.1.0", "dev": true, "license": "MIT", "dependencies": {"got": "^12.1.0", "registry-auth-token": "^5.0.1", "registry-url": "^6.0.0", "semver": "^7.3.7"}, "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/package-json/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/package-json/node_modules/semver": {"version": "7.4.0", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/package-json/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/pako": {"version": "2.1.0", "license": "(MIT AND Zlib)", "peer": true}, "node_modules/parent-module": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/parse-github-url": {"version": "1.0.2", "dev": true, "license": "MIT", "bin": {"parse-github-url": "cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/parse-headers": {"version": "2.0.5", "license": "MIT", "peer": true}, "node_modules/parse-json": {"version": "5.2.0", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/parse-path": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"protocols": "^2.0.0"}}, "node_modules/parse-url": {"version": "8.1.0", "dev": true, "license": "MIT", "dependencies": {"parse-path": "^7.0.0"}}, "node_modules/parse5": {"version": "7.1.2", "dev": true, "license": "MIT", "dependencies": {"entities": "^4.4.0"}, "funding": {"url": "https://github.com/inikulin/parse5?sponsor=1"}}, "node_modules/path-exists": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "dev": true, "license": "MIT"}, "node_modules/path-type": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/pathe": {"version": "1.1.1", "dev": true, "license": "MIT"}, "node_modules/pathval": {"version": "1.1.1", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/pbf": {"version": "3.2.1", "license": "BSD-3-<PERSON><PERSON>", "peer": true, "dependencies": {"ieee754": "^1.1.12", "resolve-protobuf-schema": "^2.1.0"}, "bin": {"pbf": "bin/pbf"}}, "node_modules/performance-now": {"version": "2.1.0", "license": "MIT", "optional": true}, "node_modules/picocolors": {"version": "1.0.0", "dev": true, "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pify": {"version": "2.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/pkg-types": {"version": "1.0.3", "dev": true, "license": "MIT", "dependencies": {"jsonc-parser": "^3.2.0", "mlly": "^1.2.0", "pathe": "^1.1.0"}}, "node_modules/postcss": {"version": "8.4.27", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.6", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/postcss-selector-parser": {"version": "6.0.11", "dev": true, "license": "MIT", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "node_modules/preact": {"version": "10.15.1", "dev": true, "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/preact"}}, "node_modules/prelude-ls": {"version": "1.2.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/prettier": {"version": "2.8.8", "dev": true, "license": "MIT", "bin": {"prettier": "bin-prettier.js"}, "engines": {"node": ">=10.13.0"}, "funding": {"url": "https://github.com/prettier/prettier?sponsor=1"}}, "node_modules/prettier-linter-helpers": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"fast-diff": "^1.1.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/pretty-format": {"version": "29.6.1", "dev": true, "license": "MIT", "dependencies": {"@jest/schemas": "^29.6.0", "ansi-styles": "^5.0.0", "react-is": "^18.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/pretty-format/node_modules/ansi-styles": {"version": "5.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/pretty-quick": {"version": "3.1.3", "dev": true, "license": "MIT", "dependencies": {"chalk": "^3.0.0", "execa": "^4.0.0", "find-up": "^4.1.0", "ignore": "^5.1.4", "mri": "^1.1.5", "multimatch": "^4.0.0"}, "bin": {"pretty-quick": "bin/pretty-quick.js"}, "engines": {"node": ">=10.13"}, "peerDependencies": {"prettier": ">=2.0.0"}}, "node_modules/pretty-quick/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/pretty-quick/node_modules/chalk": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=8"}}, "node_modules/pretty-quick/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/pretty-quick/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/pretty-quick/node_modules/find-up": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/pretty-quick/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/pretty-quick/node_modules/locate-path": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/pretty-quick/node_modules/p-limit": {"version": "2.3.0", "dev": true, "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/pretty-quick/node_modules/p-locate": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/pretty-quick/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/process-nextick-args": {"version": "2.0.1", "dev": true, "license": "MIT"}, "node_modules/proj4": {"version": "2.9.0", "license": "MIT", "dependencies": {"mgrs": "1.0.0", "wkt-parser": "^1.3.1"}}, "node_modules/promise.allsettled": {"version": "1.0.6", "dev": true, "license": "MIT", "dependencies": {"array.prototype.map": "^1.0.5", "call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4", "get-intrinsic": "^1.1.3", "iterate-value": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/proto-list": {"version": "1.2.4", "dev": true, "license": "ISC"}, "node_modules/protocol-buffers-schema": {"version": "3.6.0", "license": "MIT", "peer": true}, "node_modules/protocols": {"version": "2.0.1", "dev": true, "license": "MIT"}, "node_modules/proxy-agent": {"version": "6.3.0", "dev": true, "license": "MIT", "dependencies": {"agent-base": "^7.0.2", "debug": "^4.3.4", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.0", "lru-cache": "^7.14.1", "pac-proxy-agent": "^7.0.0", "proxy-from-env": "^1.1.0", "socks-proxy-agent": "^8.0.1"}, "engines": {"node": ">= 14"}}, "node_modules/proxy-agent/node_modules/agent-base": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.3.4"}, "engines": {"node": ">= 14"}}, "node_modules/proxy-agent/node_modules/https-proxy-agent": {"version": "7.0.1", "dev": true, "license": "MIT", "dependencies": {"agent-base": "^7.0.2", "debug": "4"}, "engines": {"node": ">= 14"}}, "node_modules/proxy-agent/node_modules/lru-cache": {"version": "7.18.3", "dev": true, "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/proxy-from-env": {"version": "1.1.0", "dev": true, "license": "MIT"}, "node_modules/psl": {"version": "1.9.0", "dev": true, "license": "MIT"}, "node_modules/pump": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/punycode": {"version": "2.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/pupa": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"escape-goat": "^4.0.0"}, "engines": {"node": ">=12.20"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/querystringify": {"version": "2.2.0", "dev": true, "license": "MIT"}, "node_modules/queue-microtask": {"version": "1.2.3", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/quick-lru": {"version": "6.1.1", "license": "MIT", "peer": true, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/quickselect": {"version": "2.0.0", "license": "ISC", "peer": true}, "node_modules/raf": {"version": "3.4.1", "license": "MIT", "optional": true, "dependencies": {"performance-now": "^2.1.0"}}, "node_modules/rbush": {"version": "3.0.1", "license": "MIT", "peer": true, "dependencies": {"quickselect": "^2.0.0"}}, "node_modules/rc": {"version": "1.2.8", "dev": true, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "dependencies": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "bin": {"rc": "cli.js"}}, "node_modules/rc/node_modules/ini": {"version": "1.3.8", "dev": true, "license": "ISC"}, "node_modules/rc/node_modules/strip-json-comments": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/react-is": {"version": "18.2.0", "dev": true, "license": "MIT"}, "node_modules/read-pkg": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"load-json-file": "^4.0.0", "normalize-package-data": "^2.3.2", "path-type": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/read-pkg-up": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"find-up": "^2.0.0", "read-pkg": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/read-pkg-up/node_modules/find-up": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/read-pkg-up/node_modules/locate-path": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^2.0.0", "path-exists": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/read-pkg-up/node_modules/p-limit": {"version": "1.3.0", "dev": true, "license": "MIT", "dependencies": {"p-try": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/read-pkg-up/node_modules/p-locate": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^1.1.0"}, "engines": {"node": ">=4"}}, "node_modules/read-pkg-up/node_modules/p-try": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/read-pkg-up/node_modules/path-exists": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/read-pkg/node_modules/hosted-git-info": {"version": "2.8.9", "dev": true, "license": "ISC"}, "node_modules/read-pkg/node_modules/normalize-package-data": {"version": "2.5.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "node_modules/read-pkg/node_modules/path-type": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"pify": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/read-pkg/node_modules/pify": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/read-pkg/node_modules/semver": {"version": "5.7.2", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/readable-stream": {"version": "3.6.2", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/rechoir": {"version": "0.6.2", "dev": true, "dependencies": {"resolve": "^1.1.6"}, "engines": {"node": ">= 0.10"}}, "node_modules/redent": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"indent-string": "^4.0.0", "strip-indent": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/regenerator-runtime": {"version": "0.13.11", "license": "MIT"}, "node_modules/regexp.prototype.flags": {"version": "1.4.3", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.3", "functions-have-names": "^1.2.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/registry-auth-token": {"version": "5.0.2", "dev": true, "license": "MIT", "dependencies": {"@pnpm/npm-conf": "^2.1.0"}, "engines": {"node": ">=14"}}, "node_modules/registry-url": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"rc": "1.2.8"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/release-it": {"version": "16.1.3", "dev": true, "license": "MIT", "dependencies": {"@iarna/toml": "2.2.5", "@octokit/rest": "19.0.13", "async-retry": "1.3.3", "chalk": "5.3.0", "cosmiconfig": "8.2.0", "execa": "7.1.1", "git-url-parse": "13.1.0", "globby": "13.2.2", "got": "13.0.0", "inquirer": "9.2.8", "is-ci": "3.0.1", "issue-parser": "6.0.0", "lodash": "4.17.21", "mime-types": "2.1.35", "new-github-release-url": "2.0.0", "node-fetch": "3.3.1", "open": "9.1.0", "ora": "6.3.1", "os-name": "5.1.0", "promise.allsettled": "1.0.6", "proxy-agent": "6.3.0", "semver": "7.5.4", "shelljs": "0.8.5", "update-notifier": "6.0.2", "url-join": "5.0.0", "wildcard-match": "5.1.2", "yargs-parser": "21.1.1"}, "bin": {"release-it": "bin/release-it.js"}, "engines": {"node": ">=16"}}, "node_modules/release-it/node_modules/chalk": {"version": "5.3.0", "dev": true, "license": "MIT", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/release-it/node_modules/execa": {"version": "7.1.1", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.1", "human-signals": "^4.3.0", "is-stream": "^3.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^5.1.0", "onetime": "^6.0.0", "signal-exit": "^3.0.7", "strip-final-newline": "^3.0.0"}, "engines": {"node": "^14.18.0 || ^16.14.0 || >=18.0.0"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/release-it/node_modules/get-stream": {"version": "6.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/release-it/node_modules/globby": {"version": "13.2.2", "dev": true, "license": "MIT", "dependencies": {"dir-glob": "^3.0.1", "fast-glob": "^3.3.0", "ignore": "^5.2.4", "merge2": "^1.4.1", "slash": "^4.0.0"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/release-it/node_modules/got": {"version": "13.0.0", "dev": true, "license": "MIT", "dependencies": {"@sindresorhus/is": "^5.2.0", "@szmarczak/http-timer": "^5.0.1", "cacheable-lookup": "^7.0.0", "cacheable-request": "^10.2.8", "decompress-response": "^6.0.0", "form-data-encoder": "^2.1.2", "get-stream": "^6.0.1", "http2-wrapper": "^2.1.10", "lowercase-keys": "^3.0.0", "p-cancelable": "^3.0.0", "responselike": "^3.0.0"}, "engines": {"node": ">=16"}, "funding": {"url": "https://github.com/sindresorhus/got?sponsor=1"}}, "node_modules/release-it/node_modules/human-signals": {"version": "4.3.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=14.18.0"}}, "node_modules/release-it/node_modules/is-stream": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/release-it/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/release-it/node_modules/mimic-fn": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/release-it/node_modules/npm-run-path": {"version": "5.1.0", "dev": true, "license": "MIT", "dependencies": {"path-key": "^4.0.0"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/release-it/node_modules/onetime": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"mimic-fn": "^4.0.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/release-it/node_modules/path-key": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/release-it/node_modules/semver": {"version": "7.5.4", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/release-it/node_modules/slash": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/release-it/node_modules/strip-final-newline": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/release-it/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/require-directory": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/requires-port": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/resolve": {"version": "1.22.2", "dev": true, "license": "MIT", "dependencies": {"is-core-module": "^2.11.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-alpn": {"version": "1.2.1", "dev": true, "license": "MIT"}, "node_modules/resolve-from": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/resolve-protobuf-schema": {"version": "2.1.0", "license": "MIT", "peer": true, "dependencies": {"protocol-buffers-schema": "^3.3.1"}}, "node_modules/responselike": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"lowercase-keys": "^3.0.0"}, "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/restore-cursor": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}, "engines": {"node": ">=8"}}, "node_modules/retry": {"version": "0.13.1", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/reusify": {"version": "1.0.4", "dev": true, "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rgbcolor": {"version": "1.0.1", "license": "MIT OR SEE LICENSE IN FEEL-FREE.md", "optional": true, "engines": {"node": ">= 0.8.15"}}, "node_modules/rimraf": {"version": "3.0.2", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/rollup": {"version": "3.28.0", "resolved": "https://registry.npmjs.org/rollup/-/rollup-3.28.0.tgz", "integrity": "sha512-d7zhvo1OUY2SXSM6pfNjgD5+d0Nz87CUp4mt8l/GgVP3oBsPwzNvSzyu1me6BSG9JIgWNTVcafIXBIyM8yQ3yw==", "dev": true, "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/rrweb-cssom": {"version": "0.6.0", "dev": true, "license": "MIT"}, "node_modules/run-applescript": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"execa": "^5.0.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/run-applescript/node_modules/execa": {"version": "5.1.1", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/run-applescript/node_modules/get-stream": {"version": "6.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/run-applescript/node_modules/human-signals": {"version": "2.1.0", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=10.17.0"}}, "node_modules/run-async": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/run-parallel": {"version": "1.2.0", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/rw": {"version": "1.3.3", "license": "BSD-3-<PERSON><PERSON>", "peer": true}, "node_modules/rxjs": {"version": "7.8.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.1.0"}}, "node_modules/rxjs/node_modules/tslib": {"version": "2.6.1", "dev": true, "license": "0BSD"}, "node_modules/safe-buffer": {"version": "5.2.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safe-regex-test": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.3", "is-regex": "^1.1.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safer-buffer": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/saxes": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"xmlchars": "^2.2.0"}, "engines": {"node": ">=v12.22.7"}}, "node_modules/search-insights": {"version": "2.7.0", "resolved": "https://registry.npmjs.org/search-insights/-/search-insights-2.7.0.tgz", "integrity": "sha512-GLbVaGgzYEKMvuJbHRhLi1qoBFnjXZGZ6l4LxOYPCp4lI2jDRB3jPU9/XNhMwv6kvnA9slTreq6pvK+b3o3aqg==", "dev": true, "peer": true, "engines": {"node": ">=8.16.0"}}, "node_modules/semver": {"version": "6.3.1", "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "dev": true, "bin": {"semver": "bin/semver.js"}}, "node_modules/semver-diff": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"semver": "^7.3.5"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/semver-diff/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/semver-diff/node_modules/semver": {"version": "7.4.0", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/semver-diff/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/shebang-command": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/shelljs": {"version": "0.8.5", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"glob": "^7.0.0", "interpret": "^1.0.0", "rechoir": "^0.6.2"}, "bin": {"shjs": "bin/shjs"}, "engines": {"node": ">=4"}}, "node_modules/shiki": {"version": "0.14.3", "dev": true, "license": "MIT", "dependencies": {"ansi-sequence-parser": "^1.1.0", "jsonc-parser": "^3.2.0", "vscode-oniguruma": "^1.7.0", "vscode-textmate": "^8.0.0"}}, "node_modules/side-channel": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.0", "get-intrinsic": "^1.0.2", "object-inspect": "^1.9.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/siginfo": {"version": "2.0.0", "dev": true, "license": "ISC"}, "node_modules/signal-exit": {"version": "3.0.7", "dev": true, "license": "ISC"}, "node_modules/slash": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/smart-buffer": {"version": "4.2.0", "dev": true, "license": "MIT", "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}}, "node_modules/socks": {"version": "2.7.1", "dev": true, "license": "MIT", "dependencies": {"ip": "^2.0.0", "smart-buffer": "^4.2.0"}, "engines": {"node": ">= 10.13.0", "npm": ">= 3.0.0"}}, "node_modules/socks-proxy-agent": {"version": "8.0.1", "dev": true, "license": "MIT", "dependencies": {"agent-base": "^7.0.1", "debug": "^4.3.4", "socks": "^2.7.1"}, "engines": {"node": ">= 14"}}, "node_modules/socks-proxy-agent/node_modules/agent-base": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.3.4"}, "engines": {"node": ">= 14"}}, "node_modules/socks/node_modules/ip": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/sort-asc": {"version": "0.1.0", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/sort-desc": {"version": "0.1.1", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/sort-object": {"version": "0.3.2", "peer": true, "dependencies": {"sort-asc": "^0.1.0", "sort-desc": "^0.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/source-map": {"version": "0.6.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-js": {"version": "1.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/spdx-correct": {"version": "3.2.0", "dev": true, "license": "Apache-2.0", "dependencies": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-exceptions": {"version": "2.3.0", "dev": true, "license": "CC-BY-3.0"}, "node_modules/spdx-expression-parse": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-license-ids": {"version": "3.0.13", "dev": true, "license": "CC0-1.0"}, "node_modules/split": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"through": "2"}, "engines": {"node": "*"}}, "node_modules/split2": {"version": "3.2.2", "dev": true, "license": "ISC", "dependencies": {"readable-stream": "^3.0.0"}}, "node_modules/sprintf-js": {"version": "1.0.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/stackback": {"version": "0.0.2", "dev": true, "license": "MIT"}, "node_modules/stackblur-canvas": {"version": "2.5.0", "license": "MIT", "optional": true, "engines": {"node": ">=0.1.14"}}, "node_modules/std-env": {"version": "3.3.3", "dev": true, "license": "MIT"}, "node_modules/stdin-discarder": {"version": "0.1.0", "dev": true, "license": "MIT", "dependencies": {"bl": "^5.0.0"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/stop-iteration-iterator": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"internal-slot": "^1.0.4"}, "engines": {"node": ">= 0.4"}}, "node_modules/string_decoder": {"version": "1.3.0", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/string-argv": {"version": "0.3.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.6.19"}}, "node_modules/string-width": {"version": "5.1.2", "dev": true, "license": "MIT", "dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/string-width/node_modules/ansi-regex": {"version": "6.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/string-width/node_modules/strip-ansi": {"version": "7.0.1", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/string.prototype.trim": {"version": "1.2.7", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimend": {"version": "1.0.6", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimstart": {"version": "1.0.6", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/strip-ansi": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-bom": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/strip-final-newline": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/strip-indent": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"min-indent": "^1.0.0"}, "engines": {"node": ">=8"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/strip-literal": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"acorn": "^8.8.2"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/supports-color": {"version": "5.5.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz", "integrity": "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==", "dev": true, "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/svg-pathdata": {"version": "6.0.3", "license": "MIT", "optional": true, "engines": {"node": ">=12.0.0"}}, "node_modules/symbol-tree": {"version": "3.2.4", "dev": true, "license": "MIT"}, "node_modules/tabbable": {"version": "6.2.0", "resolved": "https://registry.npmjs.org/tabbable/-/tabbable-6.2.0.tgz", "integrity": "sha512-Cat63mxsVJlzYvN51JmVXIgNoUokrIaT2zLclCXjRd8boZ0004U4KCs/sToJ75C6sdlByWxpYnb5Boif1VSFew==", "dev": true}, "node_modules/test-exclude": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"@istanbuljs/schema": "^0.1.2", "glob": "^7.1.4", "minimatch": "^3.0.4"}, "engines": {"node": ">=8"}}, "node_modules/text-extensions": {"version": "1.9.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10"}}, "node_modules/text-segmentation": {"version": "1.0.3", "license": "MIT", "optional": true, "dependencies": {"utrie": "^1.0.2"}}, "node_modules/text-table": {"version": "0.2.0", "dev": true, "license": "MIT"}, "node_modules/through": {"version": "2.3.8", "dev": true, "license": "MIT"}, "node_modules/through2": {"version": "2.0.5", "dev": true, "license": "MIT", "dependencies": {"readable-stream": "~2.3.6", "xtend": "~4.0.1"}}, "node_modules/through2/node_modules/isarray": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/through2/node_modules/readable-stream": {"version": "2.3.8", "dev": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/through2/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "license": "MIT"}, "node_modules/through2/node_modules/string_decoder": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/tiny-emitter": {"version": "2.1.0", "license": "MIT", "peer": true}, "node_modules/tinybench": {"version": "2.5.0", "dev": true, "license": "MIT"}, "node_modules/tinypool": {"version": "0.6.0", "dev": true, "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/tinyspy": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/titleize": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/tmp": {"version": "0.0.33", "dev": true, "license": "MIT", "dependencies": {"os-tmpdir": "~1.0.2"}, "engines": {"node": ">=0.6.0"}}, "node_modules/to-fast-properties": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-2.0.0.tgz", "integrity": "sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==", "dev": true, "peer": true, "engines": {"node": ">=4"}}, "node_modules/to-regex-range": {"version": "5.0.1", "dev": true, "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/tough-cookie": {"version": "4.1.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"psl": "^1.1.33", "punycode": "^2.1.1", "universalify": "^0.2.0", "url-parse": "^1.5.3"}, "engines": {"node": ">=6"}}, "node_modules/tough-cookie/node_modules/universalify": {"version": "0.2.0", "dev": true, "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "node_modules/tr46": {"version": "0.0.3", "dev": true, "license": "MIT"}, "node_modules/trim-newlines": {"version": "3.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/tslib": {"version": "1.14.1", "dev": true, "license": "0BSD"}, "node_modules/tsutils": {"version": "3.21.0", "dev": true, "license": "MIT", "dependencies": {"tslib": "^1.8.1"}, "engines": {"node": ">= 6"}, "peerDependencies": {"typescript": ">=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta"}}, "node_modules/type-check": {"version": "0.4.0", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-detect": {"version": "4.0.8", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/type-fest": {"version": "0.20.2", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/typed-array-length": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "for-each": "^0.3.3", "is-typed-array": "^1.1.9"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typedarray": {"version": "0.0.6", "dev": true, "license": "MIT"}, "node_modules/typedarray-to-buffer": {"version": "3.1.5", "dev": true, "license": "MIT", "dependencies": {"is-typedarray": "^1.0.0"}}, "node_modules/typescript": {"version": "5.1.6", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/ufo": {"version": "1.1.2", "dev": true, "license": "MIT"}, "node_modules/uglify-js": {"version": "3.17.4", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "optional": true, "bin": {"uglifyjs": "bin/uglifyjs"}, "engines": {"node": ">=0.8.0"}}, "node_modules/unbox-primitive": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-bigints": "^1.0.2", "has-symbols": "^1.0.3", "which-boxed-primitive": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/unique-string": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"crypto-random-string": "^4.0.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/universal-user-agent": {"version": "6.0.0", "dev": true, "license": "ISC"}, "node_modules/universalify": {"version": "0.1.2", "dev": true, "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "node_modules/untildify": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/update-browserslist-db": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.11.tgz", "integrity": "sha512-dCwEFf0/oT85M1fHBg4F0jtLwJrutGoHSQXCh7u4o2t1drG+c0a9Flnqww6XUKSfQMPpJBRjU8d4RXB09qtvaA==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "peer": true, "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/update-notifier": {"version": "6.0.2", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boxen": "^7.0.0", "chalk": "^5.0.1", "configstore": "^6.0.0", "has-yarn": "^3.0.0", "import-lazy": "^4.0.0", "is-ci": "^3.0.1", "is-installed-globally": "^0.4.0", "is-npm": "^6.0.0", "is-yarn-global": "^0.4.0", "latest-version": "^7.0.0", "pupa": "^3.1.0", "semver": "^7.3.7", "semver-diff": "^4.0.0", "xdg-basedir": "^5.1.0"}, "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/yeoman/update-notifier?sponsor=1"}}, "node_modules/update-notifier/node_modules/chalk": {"version": "5.2.0", "dev": true, "license": "MIT", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/update-notifier/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/update-notifier/node_modules/semver": {"version": "7.4.0", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/update-notifier/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/uri-js": {"version": "4.4.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/url-join": {"version": "5.0.0", "dev": true, "license": "MIT", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}}, "node_modules/url-parse": {"version": "1.5.10", "dev": true, "license": "MIT", "dependencies": {"querystringify": "^2.1.1", "requires-port": "^1.0.0"}}, "node_modules/util-deprecate": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/utrie": {"version": "1.0.2", "license": "MIT", "optional": true, "dependencies": {"base64-arraybuffer": "^1.0.2"}}, "node_modules/v8-to-istanbul": {"version": "9.1.0", "dev": true, "license": "ISC", "dependencies": {"@jridgewell/trace-mapping": "^0.3.12", "@types/istanbul-lib-coverage": "^2.0.1", "convert-source-map": "^1.6.0"}, "engines": {"node": ">=10.12.0"}}, "node_modules/validate-npm-package-license": {"version": "3.0.4", "dev": true, "license": "Apache-2.0", "dependencies": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "node_modules/validator": {"version": "13.9.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/vite": {"version": "4.4.9", "resolved": "https://registry.npmjs.org/vite/-/vite-4.4.9.tgz", "integrity": "sha512-2mbUn2LlUmNASWwSCNSJ/EG2HuSRTnVNaydp6vMCm5VIqJsjMfbIWtbH2kDuwUVW5mMUKKZvGPX/rqeqVvv1XA==", "dev": true, "dependencies": {"esbuild": "^0.18.10", "postcss": "^8.4.27", "rollup": "^3.27.1"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "peerDependencies": {"@types/node": ">= 14", "less": "*", "lightningcss": "^1.21.0", "sass": "*", "stylus": "*", "sugarss": "*", "terser": "^5.4.0"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "less": {"optional": true}, "lightningcss": {"optional": true}, "sass": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}}}, "node_modules/vite-node": {"version": "0.33.0", "dev": true, "license": "MIT", "dependencies": {"cac": "^6.7.14", "debug": "^4.3.4", "mlly": "^1.4.0", "pathe": "^1.1.1", "picocolors": "^1.0.0", "vite": "^3.0.0 || ^4.0.0"}, "bin": {"vite-node": "vite-node.mjs"}, "engines": {"node": ">=v14.18.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/vite-plugin-dts": {"version": "3.3.1", "dev": true, "license": "MIT", "dependencies": {"@microsoft/api-extractor": "^7.36.0", "@rollup/pluginutils": "^5.0.2", "@vue/language-core": "^1.8.1", "debug": "^4.3.4", "kolorist": "^1.8.0", "vue-tsc": "^1.8.1"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependencies": {"typescript": "*", "vite": "*"}, "peerDependenciesMeta": {"vite": {"optional": true}}}, "node_modules/vitepress": {"version": "1.0.0-rc.4", "resolved": "https://registry.npmjs.org/vitepress/-/vitepress-1.0.0-rc.4.tgz", "integrity": "sha512-JCQ89Bm6ECUTnyzyas3JENo00UDJeK8q1SUQyJYou+4Yz5BKEc/F3O21cu++DnUT2zXc0kvQ2Aj4BZCc/nioXQ==", "dev": true, "dependencies": {"@docsearch/css": "^3.5.1", "@docsearch/js": "^3.5.1", "@vitejs/plugin-vue": "^4.2.3", "@vue/devtools-api": "^6.5.0", "@vueuse/core": "^10.3.0", "@vueuse/integrations": "^10.3.0", "body-scroll-lock": "4.0.0-beta.0", "focus-trap": "^7.5.2", "mark.js": "8.11.1", "minisearch": "^6.1.0", "shiki": "^0.14.3", "vite": "^4.4.9", "vue": "^3.3.4"}, "bin": {"vitepress": "bin/vitepress.js"}}, "node_modules/vitest": {"version": "0.33.0", "dev": true, "license": "MIT", "dependencies": {"@types/chai": "^4.3.5", "@types/chai-subset": "^1.3.3", "@types/node": "*", "@vitest/expect": "0.33.0", "@vitest/runner": "0.33.0", "@vitest/snapshot": "0.33.0", "@vitest/spy": "0.33.0", "@vitest/utils": "0.33.0", "acorn": "^8.9.0", "acorn-walk": "^8.2.0", "cac": "^6.7.14", "chai": "^4.3.7", "debug": "^4.3.4", "local-pkg": "^0.4.3", "magic-string": "^0.30.1", "pathe": "^1.1.1", "picocolors": "^1.0.0", "std-env": "^3.3.3", "strip-literal": "^1.0.1", "tinybench": "^2.5.0", "tinypool": "^0.6.0", "vite": "^3.0.0 || ^4.0.0", "vite-node": "0.33.0", "why-is-node-running": "^2.2.2"}, "bin": {"vitest": "vitest.mjs"}, "engines": {"node": ">=v14.18.0"}, "funding": {"url": "https://opencollective.com/vitest"}, "peerDependencies": {"@edge-runtime/vm": "*", "@vitest/browser": "*", "@vitest/ui": "*", "happy-dom": "*", "jsdom": "*", "playwright": "*", "safaridriver": "*", "webdriverio": "*"}, "peerDependenciesMeta": {"@edge-runtime/vm": {"optional": true}, "@vitest/browser": {"optional": true}, "@vitest/ui": {"optional": true}, "happy-dom": {"optional": true}, "jsdom": {"optional": true}, "playwright": {"optional": true}, "safaridriver": {"optional": true}, "webdriverio": {"optional": true}}}, "node_modules/vscode-oniguruma": {"version": "1.7.0", "dev": true, "license": "MIT"}, "node_modules/vscode-textmate": {"version": "8.0.0", "dev": true, "license": "MIT"}, "node_modules/vue": {"version": "3.3.4", "dev": true, "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.3.4", "@vue/compiler-sfc": "3.3.4", "@vue/runtime-dom": "3.3.4", "@vue/server-renderer": "3.3.4", "@vue/shared": "3.3.4"}}, "node_modules/vue-component-type-helpers": {"version": "1.8.4", "dev": true, "license": "MIT"}, "node_modules/vue-eslint-parser": {"version": "9.3.1", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.3.4", "eslint-scope": "^7.1.1", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.6"}, "engines": {"node": "^14.17.0 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/mysticatea"}, "peerDependencies": {"eslint": ">=6.0.0"}}, "node_modules/vue-eslint-parser/node_modules/eslint-scope": {"version": "7.2.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/vue-eslint-parser/node_modules/eslint-visitor-keys": {"version": "3.4.0", "dev": true, "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/vue-eslint-parser/node_modules/estraverse": {"version": "5.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/vue-eslint-parser/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/vue-eslint-parser/node_modules/semver": {"version": "7.4.0", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/vue-eslint-parser/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/vue-template-compiler": {"version": "2.7.14", "dev": true, "license": "MIT", "dependencies": {"de-indent": "^1.0.2", "he": "^1.2.0"}}, "node_modules/vue-tsc": {"version": "1.8.5", "dev": true, "license": "MIT", "dependencies": {"@vue/language-core": "1.8.5", "@vue/typescript": "1.8.5", "semver": "^7.3.8"}, "bin": {"vue-tsc": "bin/vue-tsc.js"}, "peerDependencies": {"typescript": "*"}}, "node_modules/vue-tsc/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/vue-tsc/node_modules/semver": {"version": "7.5.4", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/vue-tsc/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/w3c-xmlserializer": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"xml-name-validator": "^4.0.0"}, "engines": {"node": ">=14"}}, "node_modules/wcwidth": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"defaults": "^1.0.3"}}, "node_modules/web-streams-polyfill": {"version": "3.2.1", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/web-worker": {"version": "1.2.0", "license": "Apache-2.0", "peer": true}, "node_modules/webidl-conversions": {"version": "3.0.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/whatwg-encoding": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"iconv-lite": "0.6.3"}, "engines": {"node": ">=12"}}, "node_modules/whatwg-encoding/node_modules/iconv-lite": {"version": "0.6.3", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/whatwg-mimetype": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}}, "node_modules/whatwg-url": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/which": {"version": "2.0.2", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/which-boxed-primitive": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"is-bigint": "^1.0.1", "is-boolean-object": "^1.1.0", "is-number-object": "^1.0.4", "is-string": "^1.0.5", "is-symbol": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-typed-array": {"version": "1.1.9", "dev": true, "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.5", "call-bind": "^1.0.2", "for-each": "^0.3.3", "gopd": "^1.0.1", "has-tostringtag": "^1.0.0", "is-typed-array": "^1.1.10"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/why-is-node-running": {"version": "2.2.2", "dev": true, "license": "MIT", "dependencies": {"siginfo": "^2.0.0", "stackback": "0.0.2"}, "bin": {"why-is-node-running": "cli.js"}, "engines": {"node": ">=8"}}, "node_modules/widest-line": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"string-width": "^5.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/wildcard-match": {"version": "5.1.2", "dev": true, "license": "ISC"}, "node_modules/windows-release": {"version": "5.1.0", "dev": true, "license": "MIT", "dependencies": {"execa": "^5.1.1"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/windows-release/node_modules/execa": {"version": "5.1.1", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/windows-release/node_modules/get-stream": {"version": "6.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/windows-release/node_modules/human-signals": {"version": "2.1.0", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=10.17.0"}}, "node_modules/wkt-parser": {"version": "1.3.2", "license": "MIT"}, "node_modules/wordwrap": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/wrap-ansi": {"version": "8.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi/node_modules/ansi-regex": {"version": "6.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/wrap-ansi/node_modules/ansi-styles": {"version": "6.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/wrap-ansi/node_modules/strip-ansi": {"version": "7.0.1", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "dev": true, "license": "ISC"}, "node_modules/write-file-atomic": {"version": "3.0.3", "dev": true, "license": "ISC", "dependencies": {"imurmurhash": "^0.1.4", "is-typedarray": "^1.0.0", "signal-exit": "^3.0.2", "typedarray-to-buffer": "^3.1.5"}}, "node_modules/ws": {"version": "8.13.0", "dev": true, "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/xdg-basedir": {"version": "5.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/xml-name-validator": {"version": "4.0.0", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12"}}, "node_modules/xml-utils": {"version": "1.7.0", "license": "CC0-1.0", "peer": true}, "node_modules/xmlchars": {"version": "2.2.0", "dev": true, "license": "MIT"}, "node_modules/xtend": {"version": "4.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.4"}}, "node_modules/y18n": {"version": "5.0.8", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yallist": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz", "integrity": "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==", "dev": true, "peer": true}, "node_modules/yargs": {"version": "16.2.0", "dev": true, "license": "MIT", "dependencies": {"cliui": "^7.0.2", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.0", "y18n": "^5.0.5", "yargs-parser": "^20.2.2"}, "engines": {"node": ">=10"}}, "node_modules/yargs-parser": {"version": "21.1.1", "dev": true, "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/yargs/node_modules/emoji-regex": {"version": "8.0.0", "dev": true, "license": "MIT"}, "node_modules/yargs/node_modules/string-width": {"version": "4.2.3", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/yargs/node_modules/yargs-parser": {"version": "20.2.9", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yocto-queue": {"version": "0.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/z-schema": {"version": "5.0.5", "dev": true, "license": "MIT", "dependencies": {"lodash.get": "^4.4.2", "lodash.isequal": "^4.5.0", "validator": "^13.7.0"}, "bin": {"z-schema": "bin/z-schema"}, "engines": {"node": ">=8.0.0"}, "optionalDependencies": {"commander": "^9.4.1"}}, "node_modules/z-schema/node_modules/commander": {"version": "9.5.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": "^12.20.0 || >=14"}}}, "dependencies": {"@aashutoshrathi/word-wrap": {"version": "1.2.6", "dev": true}, "@algolia/autocomplete-core": {"version": "1.9.3", "dev": true, "requires": {"@algolia/autocomplete-plugin-algolia-insights": "1.9.3", "@algolia/autocomplete-shared": "1.9.3"}}, "@algolia/autocomplete-plugin-algolia-insights": {"version": "1.9.3", "dev": true, "requires": {"@algolia/autocomplete-shared": "1.9.3"}}, "@algolia/autocomplete-preset-algolia": {"version": "1.9.3", "dev": true, "requires": {"@algolia/autocomplete-shared": "1.9.3"}}, "@algolia/autocomplete-shared": {"version": "1.9.3", "dev": true, "requires": {}}, "@algolia/cache-browser-local-storage": {"version": "4.18.0", "dev": true, "requires": {"@algolia/cache-common": "4.18.0"}}, "@algolia/cache-common": {"version": "4.18.0", "dev": true}, "@algolia/cache-in-memory": {"version": "4.18.0", "dev": true, "requires": {"@algolia/cache-common": "4.18.0"}}, "@algolia/client-account": {"version": "4.18.0", "dev": true, "requires": {"@algolia/client-common": "4.18.0", "@algolia/client-search": "4.18.0", "@algolia/transporter": "4.18.0"}}, "@algolia/client-analytics": {"version": "4.18.0", "dev": true, "requires": {"@algolia/client-common": "4.18.0", "@algolia/client-search": "4.18.0", "@algolia/requester-common": "4.18.0", "@algolia/transporter": "4.18.0"}}, "@algolia/client-common": {"version": "4.18.0", "dev": true, "requires": {"@algolia/requester-common": "4.18.0", "@algolia/transporter": "4.18.0"}}, "@algolia/client-personalization": {"version": "4.18.0", "dev": true, "requires": {"@algolia/client-common": "4.18.0", "@algolia/requester-common": "4.18.0", "@algolia/transporter": "4.18.0"}}, "@algolia/client-search": {"version": "4.18.0", "dev": true, "requires": {"@algolia/client-common": "4.18.0", "@algolia/requester-common": "4.18.0", "@algolia/transporter": "4.18.0"}}, "@algolia/logger-common": {"version": "4.18.0", "dev": true}, "@algolia/logger-console": {"version": "4.18.0", "dev": true, "requires": {"@algolia/logger-common": "4.18.0"}}, "@algolia/requester-browser-xhr": {"version": "4.18.0", "dev": true, "requires": {"@algolia/requester-common": "4.18.0"}}, "@algolia/requester-common": {"version": "4.18.0", "dev": true}, "@algolia/requester-node-http": {"version": "4.18.0", "dev": true, "requires": {"@algolia/requester-common": "4.18.0"}}, "@algolia/transporter": {"version": "4.18.0", "dev": true, "requires": {"@algolia/cache-common": "4.18.0", "@algolia/logger-common": "4.18.0", "@algolia/requester-common": "4.18.0"}}, "@ampproject/remapping": {"version": "2.2.1", "dev": true, "requires": {"@jridgewell/gen-mapping": "^0.3.0", "@jridgewell/trace-mapping": "^0.3.9"}}, "@babel/code-frame": {"version": "7.22.10", "resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.22.10.tgz", "integrity": "sha512-/KKIMG4UEL35WmI9OlvMhurwtytjvXoFcGNrOvyG9zIzA8YmPjVtIZUf7b05+TPO7G7/GEmLHDaoCgACHl9hhA==", "dev": true, "requires": {"@babel/highlight": "^7.22.10", "chalk": "^2.4.2"}}, "@babel/compat-data": {"version": "7.22.9", "resolved": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.22.9.tgz", "integrity": "sha512-5UamI7xkUcJ3i9qVDS+KFDEK8/7oJ55/sJMB1Ge7IEapr7KfdfV/HErR+koZwOfd+SgtFKOKRhRakdg++DcJpQ==", "dev": true, "peer": true}, "@babel/core": {"version": "7.22.10", "resolved": "https://registry.npmjs.org/@babel/core/-/core-7.22.10.tgz", "integrity": "sha512-fTmqbbUBAwCcre6zPzNngvsI0aNrPZe77AeqvDxWM9Nm+04RrJ3CAmGHA9f7lJQY6ZMhRztNemy4uslDxTX4Qw==", "dev": true, "peer": true, "requires": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.22.10", "@babel/generator": "^7.22.10", "@babel/helper-compilation-targets": "^7.22.10", "@babel/helper-module-transforms": "^7.22.9", "@babel/helpers": "^7.22.10", "@babel/parser": "^7.22.10", "@babel/template": "^7.22.5", "@babel/traverse": "^7.22.10", "@babel/types": "^7.22.10", "convert-source-map": "^1.7.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.2", "semver": "^6.3.1"}}, "@babel/eslint-parser": {"version": "7.22.9", "dev": true, "requires": {"@nicolo-ribaudo/eslint-scope-5-internals": "5.1.1-v1", "eslint-visitor-keys": "^2.1.0", "semver": "^6.3.1"}}, "@babel/generator": {"version": "7.22.10", "resolved": "https://registry.npmjs.org/@babel/generator/-/generator-7.22.10.tgz", "integrity": "sha512-79KIf7YiWjjdZ81JnLujDRApWtl7BxTqWD88+FFdQEIOG8LJ0etDOM7CXuIgGJa55sGOwZVwuEsaLEm0PJ5/+A==", "dev": true, "peer": true, "requires": {"@babel/types": "^7.22.10", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17", "jsesc": "^2.5.1"}}, "@babel/helper-compilation-targets": {"version": "7.22.10", "resolved": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.22.10.tgz", "integrity": "sha512-JMSwHD4J7SLod0idLq5PKgI+6g/hLD/iuWBq08ZX49xE14VpVEojJ5rHWptpirV2j020MvypRLAXAO50igCJ5Q==", "dev": true, "peer": true, "requires": {"@babel/compat-data": "^7.22.9", "@babel/helper-validator-option": "^7.22.5", "browserslist": "^4.21.9", "lru-cache": "^5.1.1", "semver": "^6.3.1"}}, "@babel/helper-environment-visitor": {"version": "7.22.5", "resolved": "https://registry.npmjs.org/@babel/helper-environment-visitor/-/helper-environment-visitor-7.22.5.tgz", "integrity": "sha512-XGmhECfVA/5sAt+H+xpSg0mfrHq6FzNr9Oxh7PSEBBRUb/mL7Kz3NICXb194rCqAEdxkhPT1a88teizAFyvk8Q==", "dev": true, "peer": true}, "@babel/helper-function-name": {"version": "7.22.5", "resolved": "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.22.5.tgz", "integrity": "sha512-wtHSq6jMRE3uF2otvfuD3DIvVhOsSNshQl0Qrd7qC9oQJzHvOL4qQXlQn2916+CXGywIjpGuIkoyZRRxHPiNQQ==", "dev": true, "peer": true, "requires": {"@babel/template": "^7.22.5", "@babel/types": "^7.22.5"}}, "@babel/helper-hoist-variables": {"version": "7.22.5", "resolved": "https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.22.5.tgz", "integrity": "sha512-wGjk9QZVzvknA6yKIUURb8zY3grXCcOZt+/7Wcy8O2uctxhplmUPkOdlgoNhmdVee2c92JXbf1xpMtVNbfoxRw==", "dev": true, "peer": true, "requires": {"@babel/types": "^7.22.5"}}, "@babel/helper-module-imports": {"version": "7.22.5", "resolved": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.22.5.tgz", "integrity": "sha512-8Dl6+HD/cKifutF5qGd/8ZJi84QeAKh+CEe1sBzz8UayBBGg1dAIJrdHOcOM5b2MpzWL2yuotJTtGjETq0qjXg==", "dev": true, "peer": true, "requires": {"@babel/types": "^7.22.5"}}, "@babel/helper-module-transforms": {"version": "7.22.9", "resolved": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.22.9.tgz", "integrity": "sha512-t+WA2Xn5K+rTeGtC8jCsdAH52bjggG5TKRuRrAGNM/mjIbO4GxvlLMFOEz9wXY5I2XQ60PMFsAG2WIcG82dQMQ==", "dev": true, "peer": true, "requires": {"@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-module-imports": "^7.22.5", "@babel/helper-simple-access": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/helper-validator-identifier": "^7.22.5"}}, "@babel/helper-simple-access": {"version": "7.22.5", "resolved": "https://registry.npmjs.org/@babel/helper-simple-access/-/helper-simple-access-7.22.5.tgz", "integrity": "sha512-n0H99E/K+Bika3++WNL17POvo4rKWZ7lZEp1Q+fStVbUi8nxPQEBOlTmCOxW/0JsS56SKKQ+ojAe2pHKJHN35w==", "dev": true, "peer": true, "requires": {"@babel/types": "^7.22.5"}}, "@babel/helper-split-export-declaration": {"version": "7.22.6", "resolved": "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.22.6.tgz", "integrity": "sha512-AsUnxuLhRYsisFiaJwvp1QF+I3KjD5FOxut14q/GzovUe6orHLesW2C7d754kRm53h5gqrz6sFl6sxc4BVtE/g==", "dev": true, "peer": true, "requires": {"@babel/types": "^7.22.5"}}, "@babel/helper-string-parser": {"version": "7.22.5", "resolved": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.22.5.tgz", "integrity": "sha512-mM4COjgZox8U+JcXQwPijIZLElkgEpO5rsERVDJTc2qfCDfERyob6k5WegS14SX18IIjv+XD+GrqNumY5JRCDw==", "dev": true, "peer": true}, "@babel/helper-validator-identifier": {"version": "7.22.5", "resolved": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.5.tgz", "integrity": "sha512-aJXu+6lErq8ltp+JhkJUfk1MTGyuA4v7f3pA+BJ5HLfNC6nAQ0Cpi9uOquUj8Hehg0aUiHzWQbOVJGao6ztBAQ==", "dev": true}, "@babel/helper-validator-option": {"version": "7.22.5", "resolved": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.22.5.tgz", "integrity": "sha512-R3oB6xlIVKUnxNUxbmgq7pKjxpru24zlimpE8WK47fACIlM0II/Hm1RS8IaOI7NgCr6LNS+jl5l75m20npAziw==", "dev": true, "peer": true}, "@babel/helpers": {"version": "7.22.10", "resolved": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.22.10.tgz", "integrity": "sha512-a41J4NW8HyZa1I1vAndrraTlPZ/eZoga2ZgS7fEr0tZJGVU4xqdE80CEm0CcNjha5EZ8fTBYLKHF0kqDUuAwQw==", "dev": true, "peer": true, "requires": {"@babel/template": "^7.22.5", "@babel/traverse": "^7.22.10", "@babel/types": "^7.22.10"}}, "@babel/highlight": {"version": "7.22.10", "resolved": "https://registry.npmjs.org/@babel/highlight/-/highlight-7.22.10.tgz", "integrity": "sha512-78aUtVcT7MUscr0K5mIEnkwxPE0MaxkR5RxRwuHaQ+JuU5AmTPhY+do2mdzVTnIJJpyBglql2pehuBIWHug+WQ==", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.22.5", "chalk": "^2.4.2", "js-tokens": "^4.0.0"}}, "@babel/parser": {"version": "7.22.10", "resolved": "https://registry.npmjs.org/@babel/parser/-/parser-7.22.10.tgz", "integrity": "sha512-lNbdGsQb9ekfsnjFGhEiF4hfFqGgfOP3H3d27re3n+CGhNuTSUEQdfWk556sTLNTloczcdM5TYF2LhzmDQKyvQ==", "dev": true}, "@babel/runtime": {"version": "7.21.0", "requires": {"regenerator-runtime": "^0.13.11"}}, "@babel/template": {"version": "7.22.5", "resolved": "https://registry.npmjs.org/@babel/template/-/template-7.22.5.tgz", "integrity": "sha512-X7yV7eiwAxdj9k94NEylvbVHLiVG1nvzCV2EAowhxLTwODV1jl9UzZ48leOC0sH7OnuHrIkllaBgneUykIcZaw==", "dev": true, "peer": true, "requires": {"@babel/code-frame": "^7.22.5", "@babel/parser": "^7.22.5", "@babel/types": "^7.22.5"}}, "@babel/traverse": {"version": "7.22.10", "resolved": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.22.10.tgz", "integrity": "sha512-Q/urqV4pRByiNNpb/f5OSv28ZlGJiFiiTh+GAHktbIrkPhPbl90+uW6SmpoLyZqutrg9AEaEf3Q/ZBRHBXgxig==", "dev": true, "peer": true, "requires": {"@babel/code-frame": "^7.22.10", "@babel/generator": "^7.22.10", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-function-name": "^7.22.5", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/parser": "^7.22.10", "@babel/types": "^7.22.10", "debug": "^4.1.0", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.22.10", "resolved": "https://registry.npmjs.org/@babel/types/-/types-7.22.10.tgz", "integrity": "sha512-obaoigiLrlDZ7TUQln/8m4mSqIW2QFeOrCQc9r+xsaHGNoplVNYlRVpsfE8Vj35GEm2ZH4ZhrNYogs/3fj85kg==", "dev": true, "peer": true, "requires": {"@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.5", "to-fast-properties": "^2.0.0"}}, "@bcoe/v8-coverage": {"version": "0.2.3", "dev": true}, "@docsearch/css": {"version": "3.5.1", "dev": true}, "@docsearch/js": {"version": "3.5.1", "dev": true, "requires": {"@docsearch/react": "3.5.1", "preact": "^10.0.0"}}, "@docsearch/react": {"version": "3.5.1", "dev": true, "requires": {"@algolia/autocomplete-core": "1.9.3", "@algolia/autocomplete-preset-algolia": "1.9.3", "@docsearch/css": "3.5.1", "algoliasearch": "^4.0.0"}}, "@esbuild/win32-x64": {"version": "0.18.11", "dev": true, "optional": true}, "@eslint-community/eslint-utils": {"version": "4.4.0", "dev": true, "requires": {"eslint-visitor-keys": "^3.3.0"}, "dependencies": {"eslint-visitor-keys": {"version": "3.4.0", "dev": true}}}, "@eslint-community/regexpp": {"version": "4.5.0", "dev": true}, "@eslint/eslintrc": {"version": "2.1.0", "dev": true, "requires": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "globals": "^13.19.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "dependencies": {"globals": {"version": "13.20.0", "dev": true, "requires": {"type-fest": "^0.20.2"}}}}, "@eslint/js": {"version": "8.44.0", "dev": true}, "@humanwhocodes/config-array": {"version": "0.11.10", "dev": true, "requires": {"@humanwhocodes/object-schema": "^1.2.1", "debug": "^4.1.1", "minimatch": "^3.0.5"}}, "@humanwhocodes/module-importer": {"version": "1.0.1", "dev": true}, "@humanwhocodes/object-schema": {"version": "1.2.1", "dev": true}, "@hutson/parse-repository-url": {"version": "3.0.2", "dev": true}, "@iarna/toml": {"version": "2.2.5", "dev": true}, "@istanbuljs/schema": {"version": "0.1.3", "dev": true}, "@jest/schemas": {"version": "29.6.0", "dev": true, "requires": {"@sinclair/typebox": "^0.27.8"}}, "@jridgewell/gen-mapping": {"version": "0.3.3", "dev": true, "requires": {"@jridgewell/set-array": "^1.0.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.9"}}, "@jridgewell/resolve-uri": {"version": "3.1.0", "dev": true}, "@jridgewell/set-array": {"version": "1.1.2", "dev": true}, "@jridgewell/sourcemap-codec": {"version": "1.4.15", "dev": true}, "@jridgewell/trace-mapping": {"version": "0.3.18", "dev": true, "requires": {"@jridgewell/resolve-uri": "3.1.0", "@jridgewell/sourcemap-codec": "1.4.14"}, "dependencies": {"@jridgewell/sourcemap-codec": {"version": "1.4.14", "dev": true}}}, "@mapbox/jsonlint-lines-primitives": {"version": "2.0.2", "peer": true}, "@mapbox/mapbox-gl-style-spec": {"version": "13.28.0", "peer": true, "requires": {"@mapbox/jsonlint-lines-primitives": "~2.0.2", "@mapbox/point-geometry": "^0.1.0", "@mapbox/unitbezier": "^0.0.0", "csscolorparser": "~1.0.2", "json-stringify-pretty-compact": "^2.0.0", "minimist": "^1.2.6", "rw": "^1.3.3", "sort-object": "^0.3.2"}}, "@mapbox/point-geometry": {"version": "0.1.0", "peer": true}, "@mapbox/unitbezier": {"version": "0.0.0", "peer": true}, "@microsoft/api-extractor": {"version": "7.36.2", "dev": true, "requires": {"@microsoft/api-extractor-model": "7.27.4", "@microsoft/tsdoc": "0.14.2", "@microsoft/tsdoc-config": "~0.16.1", "@rushstack/node-core-library": "3.59.5", "@rushstack/rig-package": "0.4.0", "@rushstack/ts-command-line": "4.15.1", "colors": "~1.2.1", "lodash": "~4.17.15", "resolve": "~1.22.1", "semver": "~7.3.0", "source-map": "~0.6.1", "typescript": "~5.0.4"}, "dependencies": {"lru-cache": {"version": "6.0.0", "dev": true, "requires": {"yallist": "^4.0.0"}}, "semver": {"version": "7.3.8", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "typescript": {"version": "5.0.4", "dev": true}, "yallist": {"version": "4.0.0", "dev": true}}}, "@microsoft/api-extractor-model": {"version": "7.27.4", "dev": true, "requires": {"@microsoft/tsdoc": "0.14.2", "@microsoft/tsdoc-config": "~0.16.1", "@rushstack/node-core-library": "3.59.5"}}, "@microsoft/tsdoc": {"version": "0.14.2", "dev": true}, "@microsoft/tsdoc-config": {"version": "0.16.2", "dev": true, "requires": {"@microsoft/tsdoc": "0.14.2", "ajv": "~6.12.6", "jju": "~1.4.0", "resolve": "~1.19.0"}, "dependencies": {"resolve": {"version": "1.19.0", "dev": true, "requires": {"is-core-module": "^2.1.0", "path-parse": "^1.0.6"}}}}, "@nicolo-ribaudo/eslint-scope-5-internals": {"version": "5.1.1-v1", "dev": true, "requires": {"eslint-scope": "5.1.1"}}, "@nodelib/fs.scandir": {"version": "2.1.5", "dev": true, "requires": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}}, "@nodelib/fs.stat": {"version": "2.0.5", "dev": true}, "@nodelib/fs.walk": {"version": "1.2.8", "dev": true, "requires": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}}, "@octokit/auth-token": {"version": "3.0.4", "dev": true}, "@octokit/core": {"version": "4.2.4", "dev": true, "requires": {"@octokit/auth-token": "^3.0.0", "@octokit/graphql": "^5.0.0", "@octokit/request": "^6.0.0", "@octokit/request-error": "^3.0.0", "@octokit/types": "^9.0.0", "before-after-hook": "^2.2.0", "universal-user-agent": "^6.0.0"}}, "@octokit/endpoint": {"version": "7.0.6", "dev": true, "requires": {"@octokit/types": "^9.0.0", "is-plain-object": "^5.0.0", "universal-user-agent": "^6.0.0"}}, "@octokit/graphql": {"version": "5.0.6", "dev": true, "requires": {"@octokit/request": "^6.0.0", "@octokit/types": "^9.0.0", "universal-user-agent": "^6.0.0"}}, "@octokit/openapi-types": {"version": "18.0.0", "dev": true}, "@octokit/plugin-paginate-rest": {"version": "6.1.2", "dev": true, "requires": {"@octokit/tsconfig": "^1.0.2", "@octokit/types": "^9.2.3"}}, "@octokit/plugin-request-log": {"version": "1.0.4", "dev": true, "requires": {}}, "@octokit/plugin-rest-endpoint-methods": {"version": "7.2.3", "dev": true, "requires": {"@octokit/types": "^10.0.0"}, "dependencies": {"@octokit/types": {"version": "10.0.0", "dev": true, "requires": {"@octokit/openapi-types": "^18.0.0"}}}}, "@octokit/request": {"version": "6.2.8", "dev": true, "requires": {"@octokit/endpoint": "^7.0.0", "@octokit/request-error": "^3.0.0", "@octokit/types": "^9.0.0", "is-plain-object": "^5.0.0", "node-fetch": "^2.6.7", "universal-user-agent": "^6.0.0"}, "dependencies": {"node-fetch": {"version": "2.6.12", "dev": true, "requires": {"whatwg-url": "^5.0.0"}}}}, "@octokit/request-error": {"version": "3.0.3", "dev": true, "requires": {"@octokit/types": "^9.0.0", "deprecation": "^2.0.0", "once": "^1.4.0"}}, "@octokit/rest": {"version": "19.0.13", "dev": true, "requires": {"@octokit/core": "^4.2.1", "@octokit/plugin-paginate-rest": "^6.1.2", "@octokit/plugin-request-log": "^1.0.4", "@octokit/plugin-rest-endpoint-methods": "^7.1.2"}}, "@octokit/tsconfig": {"version": "1.0.2", "dev": true}, "@octokit/types": {"version": "9.3.2", "dev": true, "requires": {"@octokit/openapi-types": "^18.0.0"}}, "@one-ini/wasm": {"version": "0.1.1", "dev": true}, "@petamoriken/float16": {"version": "3.8.3", "peer": true}, "@pnpm/config.env-replace": {"version": "1.1.0", "dev": true}, "@pnpm/network.ca-file": {"version": "1.0.2", "dev": true, "requires": {"graceful-fs": "4.2.10"}, "dependencies": {"graceful-fs": {"version": "4.2.10", "dev": true}}}, "@pnpm/npm-conf": {"version": "2.1.1", "dev": true, "requires": {"@pnpm/config.env-replace": "^1.1.0", "@pnpm/network.ca-file": "^1.0.1", "config-chain": "^1.1.11"}}, "@release-it/conventional-changelog": {"version": "7.0.0", "dev": true, "requires": {"concat-stream": "^2.0.0", "conventional-changelog": "^4.0.0", "conventional-recommended-bump": "^7.0.1", "semver": "7.5.1"}, "dependencies": {"lru-cache": {"version": "6.0.0", "dev": true, "requires": {"yallist": "^4.0.0"}}, "semver": {"version": "7.5.1", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "yallist": {"version": "4.0.0", "dev": true}}}, "@rollup/pluginutils": {"version": "5.0.2", "dev": true, "requires": {"@types/estree": "^1.0.0", "estree-walker": "^2.0.2", "picomatch": "^2.3.1"}}, "@rushstack/node-core-library": {"version": "3.59.5", "dev": true, "requires": {"colors": "~1.2.1", "fs-extra": "~7.0.1", "import-lazy": "~4.0.0", "jju": "~1.4.0", "resolve": "~1.22.1", "semver": "~7.3.0", "z-schema": "~5.0.2"}, "dependencies": {"fs-extra": {"version": "7.0.1", "dev": true, "requires": {"graceful-fs": "^4.1.2", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}}, "lru-cache": {"version": "6.0.0", "dev": true, "requires": {"yallist": "^4.0.0"}}, "semver": {"version": "7.3.8", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "yallist": {"version": "4.0.0", "dev": true}}}, "@rushstack/rig-package": {"version": "0.4.0", "dev": true, "requires": {"resolve": "~1.22.1", "strip-json-comments": "~3.1.1"}}, "@rushstack/ts-command-line": {"version": "4.15.1", "dev": true, "requires": {"@types/argparse": "1.0.38", "argparse": "~1.0.9", "colors": "~1.2.1", "string-argv": "~0.3.1"}, "dependencies": {"argparse": {"version": "1.0.10", "dev": true, "requires": {"sprintf-js": "~1.0.2"}}}}, "@sinclair/typebox": {"version": "0.27.8", "dev": true}, "@sindresorhus/is": {"version": "5.3.0", "dev": true}, "@szmarczak/http-timer": {"version": "5.0.1", "dev": true, "requires": {"defer-to-connect": "^2.0.1"}}, "@tootallnate/quickjs-emscripten": {"version": "0.23.0", "dev": true}, "@types/argparse": {"version": "1.0.38", "dev": true}, "@types/chai": {"version": "4.3.5", "dev": true}, "@types/chai-subset": {"version": "1.3.3", "dev": true, "requires": {"@types/chai": "*"}}, "@types/estree": {"version": "1.0.1", "dev": true}, "@types/file-saver": {"version": "2.0.5", "dev": true}, "@types/http-cache-semantics": {"version": "4.0.1", "dev": true}, "@types/istanbul-lib-coverage": {"version": "2.0.4", "dev": true}, "@types/json-schema": {"version": "7.0.12", "dev": true}, "@types/minimatch": {"version": "3.0.5", "dev": true}, "@types/minimist": {"version": "1.2.2", "dev": true}, "@types/node": {"version": "18.17.1", "dev": true}, "@types/normalize-package-data": {"version": "2.4.1", "dev": true}, "@types/ol-ext": {"version": "npm:@siedlerchr/types-ol-ext@3.2.0", "dev": true, "requires": {"jspdf": "^2.5.1"}}, "@types/proj4": {"version": "2.5.2", "dev": true}, "@types/raf": {"version": "3.4.0", "optional": true}, "@types/semver": {"version": "7.5.0", "dev": true}, "@types/web-bluetooth": {"version": "0.0.17", "resolved": "https://registry.npmjs.org/@types/web-bluetooth/-/web-bluetooth-0.0.17.tgz", "integrity": "sha512-4p9vcSmxAayx72yn70joFoL44c9MO/0+iVEBIQXe3v2h2SiAsEIo/G5v6ObFWvNKRFjbrVadNf9LqEEZeQPzdA==", "dev": true}, "@typescript-eslint/eslint-plugin": {"version": "5.59.9", "dev": true, "requires": {"@eslint-community/regexpp": "^4.4.0", "@typescript-eslint/scope-manager": "5.59.9", "@typescript-eslint/type-utils": "5.59.9", "@typescript-eslint/utils": "5.59.9", "debug": "^4.3.4", "grapheme-splitter": "^1.0.4", "ignore": "^5.2.0", "natural-compare-lite": "^1.4.0", "semver": "^7.3.7", "tsutils": "^3.21.0"}, "dependencies": {"lru-cache": {"version": "6.0.0", "dev": true, "requires": {"yallist": "^4.0.0"}}, "semver": {"version": "7.5.1", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "yallist": {"version": "4.0.0", "dev": true}}}, "@typescript-eslint/parser": {"version": "5.59.9", "dev": true, "requires": {"@typescript-eslint/scope-manager": "5.59.9", "@typescript-eslint/types": "5.59.9", "@typescript-eslint/typescript-estree": "5.59.9", "debug": "^4.3.4"}}, "@typescript-eslint/scope-manager": {"version": "5.59.9", "dev": true, "requires": {"@typescript-eslint/types": "5.59.9", "@typescript-eslint/visitor-keys": "5.59.9"}}, "@typescript-eslint/type-utils": {"version": "5.59.9", "dev": true, "requires": {"@typescript-eslint/typescript-estree": "5.59.9", "@typescript-eslint/utils": "5.59.9", "debug": "^4.3.4", "tsutils": "^3.21.0"}}, "@typescript-eslint/types": {"version": "5.59.9", "dev": true}, "@typescript-eslint/typescript-estree": {"version": "5.59.9", "dev": true, "requires": {"@typescript-eslint/types": "5.59.9", "@typescript-eslint/visitor-keys": "5.59.9", "debug": "^4.3.4", "globby": "^11.1.0", "is-glob": "^4.0.3", "semver": "^7.3.7", "tsutils": "^3.21.0"}, "dependencies": {"lru-cache": {"version": "6.0.0", "dev": true, "requires": {"yallist": "^4.0.0"}}, "semver": {"version": "7.5.1", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "yallist": {"version": "4.0.0", "dev": true}}}, "@typescript-eslint/utils": {"version": "5.59.9", "dev": true, "requires": {"@eslint-community/eslint-utils": "^4.2.0", "@types/json-schema": "^7.0.9", "@types/semver": "^7.3.12", "@typescript-eslint/scope-manager": "5.59.9", "@typescript-eslint/types": "5.59.9", "@typescript-eslint/typescript-estree": "5.59.9", "eslint-scope": "^5.1.1", "semver": "^7.3.7"}, "dependencies": {"lru-cache": {"version": "6.0.0", "dev": true, "requires": {"yallist": "^4.0.0"}}, "semver": {"version": "7.5.1", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "yallist": {"version": "4.0.0", "dev": true}}}, "@typescript-eslint/visitor-keys": {"version": "5.59.9", "dev": true, "requires": {"@typescript-eslint/types": "5.59.9", "eslint-visitor-keys": "^3.3.0"}, "dependencies": {"eslint-visitor-keys": {"version": "3.4.1", "dev": true}}}, "@vitejs/plugin-vue": {"version": "4.2.3", "dev": true, "requires": {}}, "@vitest/coverage-v8": {"version": "0.33.0", "dev": true, "requires": {"@ampproject/remapping": "^2.2.1", "@bcoe/v8-coverage": "^0.2.3", "istanbul-lib-coverage": "^3.2.0", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.1", "istanbul-reports": "^3.1.5", "magic-string": "^0.30.1", "picocolors": "^1.0.0", "std-env": "^3.3.3", "test-exclude": "^6.0.0", "v8-to-istanbul": "^9.1.0"}}, "@vitest/expect": {"version": "0.33.0", "dev": true, "requires": {"@vitest/spy": "0.33.0", "@vitest/utils": "0.33.0", "chai": "^4.3.7"}}, "@vitest/runner": {"version": "0.33.0", "dev": true, "requires": {"@vitest/utils": "0.33.0", "p-limit": "^4.0.0", "pathe": "^1.1.1"}, "dependencies": {"p-limit": {"version": "4.0.0", "dev": true, "requires": {"yocto-queue": "^1.0.0"}}, "yocto-queue": {"version": "1.0.0", "dev": true}}}, "@vitest/snapshot": {"version": "0.33.0", "dev": true, "requires": {"magic-string": "^0.30.1", "pathe": "^1.1.1", "pretty-format": "^29.5.0"}}, "@vitest/spy": {"version": "0.33.0", "dev": true, "requires": {"tinyspy": "^2.1.1"}}, "@vitest/utils": {"version": "0.33.0", "dev": true, "requires": {"diff-sequences": "^29.4.3", "loupe": "^2.3.6", "pretty-format": "^29.5.0"}}, "@volar/language-core": {"version": "1.9.0", "dev": true, "requires": {"@volar/source-map": "1.9.0"}}, "@volar/source-map": {"version": "1.9.0", "dev": true, "requires": {"muggle-string": "^0.3.1"}}, "@volar/typescript": {"version": "1.9.0", "dev": true, "requires": {"@volar/language-core": "1.9.0"}}, "@vue/compiler-core": {"version": "3.3.4", "dev": true, "requires": {"@babel/parser": "^7.21.3", "@vue/shared": "3.3.4", "estree-walker": "^2.0.2", "source-map-js": "^1.0.2"}}, "@vue/compiler-dom": {"version": "3.3.4", "dev": true, "requires": {"@vue/compiler-core": "3.3.4", "@vue/shared": "3.3.4"}}, "@vue/compiler-sfc": {"version": "3.3.4", "dev": true, "requires": {"@babel/parser": "^7.20.15", "@vue/compiler-core": "3.3.4", "@vue/compiler-dom": "3.3.4", "@vue/compiler-ssr": "3.3.4", "@vue/reactivity-transform": "3.3.4", "@vue/shared": "3.3.4", "estree-walker": "^2.0.2", "magic-string": "^0.30.0", "postcss": "^8.1.10", "source-map-js": "^1.0.2"}}, "@vue/compiler-ssr": {"version": "3.3.4", "dev": true, "requires": {"@vue/compiler-dom": "3.3.4", "@vue/shared": "3.3.4"}}, "@vue/devtools-api": {"version": "6.5.0", "dev": true}, "@vue/eslint-config-prettier": {"version": "7.1.0", "dev": true, "requires": {"eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0"}}, "@vue/eslint-config-typescript": {"version": "11.0.3", "dev": true, "requires": {"@typescript-eslint/eslint-plugin": "^5.59.1", "@typescript-eslint/parser": "^5.59.1", "vue-eslint-parser": "^9.1.1"}}, "@vue/language-core": {"version": "1.8.5", "dev": true, "requires": {"@volar/language-core": "~1.9.0", "@volar/source-map": "~1.9.0", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "dependencies": {"brace-expansion": {"version": "2.0.1", "dev": true, "requires": {"balanced-match": "^1.0.0"}}, "minimatch": {"version": "9.0.3", "dev": true, "requires": {"brace-expansion": "^2.0.1"}}}}, "@vue/reactivity": {"version": "3.3.4", "dev": true, "requires": {"@vue/shared": "3.3.4"}}, "@vue/reactivity-transform": {"version": "3.3.4", "dev": true, "requires": {"@babel/parser": "^7.20.15", "@vue/compiler-core": "3.3.4", "@vue/shared": "3.3.4", "estree-walker": "^2.0.2", "magic-string": "^0.30.0"}}, "@vue/runtime-core": {"version": "3.3.4", "dev": true, "requires": {"@vue/reactivity": "3.3.4", "@vue/shared": "3.3.4"}}, "@vue/runtime-dom": {"version": "3.3.4", "dev": true, "requires": {"@vue/runtime-core": "3.3.4", "@vue/shared": "3.3.4", "csstype": "^3.1.1"}}, "@vue/server-renderer": {"version": "3.3.4", "dev": true, "requires": {"@vue/compiler-ssr": "3.3.4", "@vue/shared": "3.3.4"}}, "@vue/shared": {"version": "3.3.4", "dev": true}, "@vue/test-utils": {"version": "2.4.1", "dev": true, "requires": {"js-beautify": "1.14.9", "vue-component-type-helpers": "1.8.4"}}, "@vue/tsconfig": {"version": "0.4.0", "dev": true}, "@vue/typescript": {"version": "1.8.5", "dev": true, "requires": {"@volar/typescript": "~1.9.0", "@vue/language-core": "1.8.5"}}, "@vueuse/core": {"version": "10.3.0", "resolved": "https://registry.npmjs.org/@vueuse/core/-/core-10.3.0.tgz", "integrity": "sha512-B<PERSON>5yxcFKb5btFjTSAFjTu5jmwoW66fyV9uJIP4wUXXU8aR5Hl44gndaaXp7dC5HSObmgbnR2RN+Un1p68Mf5Q==", "dev": true, "requires": {"@types/web-bluetooth": "^0.0.17", "@vueuse/metadata": "10.3.0", "@vueuse/shared": "10.3.0", "vue-demi": ">=0.14.5"}, "dependencies": {"vue-demi": {"version": "0.14.5", "resolved": "https://registry.npmjs.org/vue-demi/-/vue-demi-0.14.5.tgz", "integrity": "sha512-o9NUVpl/YlsGJ7t+xuqJKx8EBGf1quRhCiT6D/J0pfwmk9zUwYkC7yrF4SZCe6fETvSM3UNL2edcbYrSyc4QHA==", "dev": true, "requires": {}}}}, "@vueuse/integrations": {"version": "10.3.0", "resolved": "https://registry.npmjs.org/@vueuse/integrations/-/integrations-10.3.0.tgz", "integrity": "sha512-Jgiv7oFyIgC6BxmDtiyG/fxyGysIds00YaY7sefwbhCZ2/tjEx1W/1WcsISSJPNI30in28+HC2J4uuU8184ekg==", "dev": true, "requires": {"@vueuse/core": "10.3.0", "@vueuse/shared": "10.3.0", "vue-demi": ">=0.14.5"}, "dependencies": {"vue-demi": {"version": "0.14.5", "resolved": "https://registry.npmjs.org/vue-demi/-/vue-demi-0.14.5.tgz", "integrity": "sha512-o9NUVpl/YlsGJ7t+xuqJKx8EBGf1quRhCiT6D/J0pfwmk9zUwYkC7yrF4SZCe6fETvSM3UNL2edcbYrSyc4QHA==", "dev": true, "requires": {}}}}, "@vueuse/metadata": {"version": "10.3.0", "resolved": "https://registry.npmjs.org/@vueuse/metadata/-/metadata-10.3.0.tgz", "integrity": "sha512-Ema3YhNOa4swDsV0V7CEY5JXvK19JI/o1szFO1iWxdFg3vhdFtCtSTP26PCvbUpnUtNHBY2wx5y3WDXND5Pvnw==", "dev": true}, "@vueuse/shared": {"version": "10.3.0", "resolved": "https://registry.npmjs.org/@vueuse/shared/-/shared-10.3.0.tgz", "integrity": "sha512-kGqCTEuFPMK4+fNWy6dUOiYmxGcUbtznMwBZLC1PubidF4VZY05B+Oht7Jh7/6x4VOWGpvu3R37WHi81cKpiqg==", "dev": true, "requires": {"vue-demi": ">=0.14.5"}, "dependencies": {"vue-demi": {"version": "0.14.5", "resolved": "https://registry.npmjs.org/vue-demi/-/vue-demi-0.14.5.tgz", "integrity": "sha512-o9NUVpl/YlsGJ7t+xuqJKx8EBGf1quRhCiT6D/J0pfwmk9zUwYkC7yrF4SZCe6fETvSM3UNL2edcbYrSyc4QHA==", "dev": true, "requires": {}}}}, "abab": {"version": "2.0.6", "dev": true}, "abbrev": {"version": "1.1.1", "dev": true}, "acorn": {"version": "8.10.0", "dev": true}, "acorn-jsx": {"version": "5.3.2", "dev": true, "requires": {}}, "acorn-walk": {"version": "8.2.0", "dev": true}, "add-stream": {"version": "1.0.0", "dev": true}, "agent-base": {"version": "6.0.2", "dev": true, "requires": {"debug": "4"}}, "ajv": {"version": "6.12.6", "dev": true, "requires": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "algoliasearch": {"version": "4.18.0", "dev": true, "requires": {"@algolia/cache-browser-local-storage": "4.18.0", "@algolia/cache-common": "4.18.0", "@algolia/cache-in-memory": "4.18.0", "@algolia/client-account": "4.18.0", "@algolia/client-analytics": "4.18.0", "@algolia/client-common": "4.18.0", "@algolia/client-personalization": "4.18.0", "@algolia/client-search": "4.18.0", "@algolia/logger-common": "4.18.0", "@algolia/logger-console": "4.18.0", "@algolia/requester-browser-xhr": "4.18.0", "@algolia/requester-common": "4.18.0", "@algolia/requester-node-http": "4.18.0", "@algolia/transporter": "4.18.0"}}, "ansi-align": {"version": "3.0.1", "dev": true, "requires": {"string-width": "^4.1.0"}, "dependencies": {"emoji-regex": {"version": "8.0.0", "dev": true}, "string-width": {"version": "4.2.3", "dev": true, "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}}}, "ansi-escapes": {"version": "4.3.2", "dev": true, "requires": {"type-fest": "^0.21.3"}, "dependencies": {"type-fest": {"version": "0.21.3", "dev": true}}}, "ansi-regex": {"version": "5.0.1", "dev": true}, "ansi-sequence-parser": {"version": "1.1.0", "dev": true}, "ansi-styles": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==", "dev": true, "requires": {"color-convert": "^1.9.0"}}, "argparse": {"version": "2.0.1", "dev": true}, "array-buffer-byte-length": {"version": "1.0.0", "dev": true, "requires": {"call-bind": "^1.0.2", "is-array-buffer": "^3.0.1"}}, "array-differ": {"version": "3.0.0", "dev": true}, "array-ify": {"version": "1.0.0", "dev": true}, "array-union": {"version": "2.1.0", "dev": true}, "array.prototype.map": {"version": "1.0.5", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4", "es-array-method-boxes-properly": "^1.0.0", "is-string": "^1.0.7"}}, "arrify": {"version": "2.0.1", "dev": true}, "assertion-error": {"version": "1.1.0", "dev": true}, "ast-types": {"version": "0.13.4", "dev": true, "requires": {"tslib": "^2.0.1"}, "dependencies": {"tslib": {"version": "2.6.1", "dev": true}}}, "async-retry": {"version": "1.3.3", "dev": true, "requires": {"retry": "0.13.1"}}, "asynckit": {"version": "0.4.0", "dev": true}, "atob": {"version": "2.1.2"}, "available-typed-arrays": {"version": "1.0.5", "dev": true}, "axios": {"version": "0.18.1", "dev": true, "requires": {"follow-redirects": "1.5.10", "is-buffer": "^2.0.2"}}, "balanced-match": {"version": "1.0.2", "dev": true}, "base64-arraybuffer": {"version": "1.0.2", "optional": true}, "base64-js": {"version": "1.5.1", "dev": true}, "basic-ftp": {"version": "5.0.3", "dev": true}, "before-after-hook": {"version": "2.2.3", "dev": true}, "big-integer": {"version": "1.6.51", "dev": true}, "bl": {"version": "5.1.0", "dev": true, "requires": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}}, "body-scroll-lock": {"version": "4.0.0-beta.0", "dev": true}, "boolbase": {"version": "1.0.0", "dev": true}, "boxen": {"version": "7.0.2", "dev": true, "requires": {"ansi-align": "^3.0.1", "camelcase": "^7.0.0", "chalk": "^5.0.1", "cli-boxes": "^3.0.0", "string-width": "^5.1.2", "type-fest": "^2.13.0", "widest-line": "^4.0.1", "wrap-ansi": "^8.0.1"}, "dependencies": {"chalk": {"version": "5.2.0", "dev": true}, "type-fest": {"version": "2.19.0", "dev": true}}}, "bplist-parser": {"version": "0.2.0", "dev": true, "requires": {"big-integer": "^1.6.44"}}, "brace-expansion": {"version": "1.1.11", "dev": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "braces": {"version": "3.0.2", "dev": true, "requires": {"fill-range": "^7.0.1"}}, "browserslist": {"version": "4.21.10", "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.10.tgz", "integrity": "sha512-bipEBdZfVH5/pwrvqc+Ub0kUPVfGUhlKxbvfD+z1BDnPEO/X98ruXGA1WP5ASpAFKan7Qr6j736IacbZQuAlKQ==", "dev": true, "peer": true, "requires": {"caniuse-lite": "^1.0.30001517", "electron-to-chromium": "^1.4.477", "node-releases": "^2.0.13", "update-browserslist-db": "^1.0.11"}}, "btoa": {"version": "1.2.1"}, "buffer": {"version": "6.0.3", "dev": true, "requires": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "buffer-from": {"version": "1.1.2", "dev": true}, "bundle-name": {"version": "3.0.0", "dev": true, "requires": {"run-applescript": "^5.0.0"}}, "cac": {"version": "6.7.14", "dev": true}, "cacheable-lookup": {"version": "7.0.0", "dev": true}, "cacheable-request": {"version": "10.2.9", "dev": true, "requires": {"@types/http-cache-semantics": "^4.0.1", "get-stream": "^6.0.1", "http-cache-semantics": "^4.1.1", "keyv": "^4.5.2", "mimic-response": "^4.0.0", "normalize-url": "^8.0.0", "responselike": "^3.0.0"}, "dependencies": {"get-stream": {"version": "6.0.1", "dev": true}}}, "call-bind": {"version": "1.0.2", "dev": true, "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "callsites": {"version": "3.1.0", "dev": true}, "camelcase": {"version": "7.0.1", "dev": true}, "camelcase-keys": {"version": "6.2.2", "dev": true, "requires": {"camelcase": "^5.3.1", "map-obj": "^4.0.0", "quick-lru": "^4.0.1"}, "dependencies": {"camelcase": {"version": "5.3.1", "dev": true}, "quick-lru": {"version": "4.0.1", "dev": true}}}, "caniuse-lite": {"version": "1.0.30001521", "resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001521.tgz", "integrity": "sha512-fnx1grfpEOvDGH+V17eccmNjucGUnCbP6KL+l5KqBIerp26WK/+RQ7CIDE37KGJjaPyqWXXlFUyKiWmvdNNKmQ==", "dev": true, "peer": true}, "canvg": {"version": "3.0.10", "optional": true, "requires": {"@babel/runtime": "^7.12.5", "@types/raf": "^3.4.0", "core-js": "^3.8.3", "raf": "^3.4.1", "regenerator-runtime": "^0.13.7", "rgbcolor": "^1.0.1", "stackblur-canvas": "^2.0.0", "svg-pathdata": "^6.0.3"}}, "chai": {"version": "4.3.7", "dev": true, "requires": {"assertion-error": "^1.1.0", "check-error": "^1.0.2", "deep-eql": "^4.1.2", "get-func-name": "^2.0.0", "loupe": "^2.3.1", "pathval": "^1.1.1", "type-detect": "^4.0.5"}}, "chalk": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "integrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "chardet": {"version": "0.7.0", "dev": true}, "check-error": {"version": "1.0.2", "dev": true}, "ci-info": {"version": "3.8.0", "dev": true}, "cli-boxes": {"version": "3.0.0", "dev": true}, "cli-cursor": {"version": "3.1.0", "dev": true, "requires": {"restore-cursor": "^3.1.0"}}, "cli-spinners": {"version": "2.9.0", "dev": true}, "cli-width": {"version": "4.0.0", "dev": true}, "cliui": {"version": "7.0.4", "dev": true, "requires": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^7.0.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "color-convert": {"version": "2.0.1", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "dev": true}, "emoji-regex": {"version": "8.0.0", "dev": true}, "string-width": {"version": "4.2.3", "dev": true, "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "wrap-ansi": {"version": "7.0.0", "dev": true, "requires": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}}}}, "clone": {"version": "1.0.4", "dev": true}, "color-convert": {"version": "1.9.3", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz", "integrity": "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==", "dev": true, "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz", "integrity": "sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==", "dev": true}, "colors": {"version": "1.2.5", "dev": true}, "combined-stream": {"version": "1.0.8", "dev": true, "requires": {"delayed-stream": "~1.0.0"}}, "commander": {"version": "10.0.1", "dev": true}, "compare-func": {"version": "2.0.0", "dev": true, "requires": {"array-ify": "^1.0.0", "dot-prop": "^5.1.0"}, "dependencies": {"dot-prop": {"version": "5.3.0", "dev": true, "requires": {"is-obj": "^2.0.0"}}}}, "concat-map": {"version": "0.0.1", "dev": true}, "concat-stream": {"version": "2.0.0", "dev": true, "requires": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.0.2", "typedarray": "^0.0.6"}}, "config-chain": {"version": "1.1.13", "dev": true, "requires": {"ini": "^1.3.4", "proto-list": "~1.2.1"}, "dependencies": {"ini": {"version": "1.3.8", "dev": true}}}, "configstore": {"version": "6.0.0", "dev": true, "requires": {"dot-prop": "^6.0.1", "graceful-fs": "^4.2.6", "unique-string": "^3.0.0", "write-file-atomic": "^3.0.3", "xdg-basedir": "^5.0.1"}}, "conventional-changelog": {"version": "4.0.0", "dev": true, "requires": {"conventional-changelog-angular": "^6.0.0", "conventional-changelog-atom": "^3.0.0", "conventional-changelog-codemirror": "^3.0.0", "conventional-changelog-conventionalcommits": "^6.0.0", "conventional-changelog-core": "^5.0.0", "conventional-changelog-ember": "^3.0.0", "conventional-changelog-eslint": "^4.0.0", "conventional-changelog-express": "^3.0.0", "conventional-changelog-jquery": "^4.0.0", "conventional-changelog-jshint": "^3.0.0", "conventional-changelog-preset-loader": "^3.0.0"}}, "conventional-changelog-angular": {"version": "6.0.0", "dev": true, "requires": {"compare-func": "^2.0.0"}}, "conventional-changelog-atom": {"version": "3.0.0", "dev": true}, "conventional-changelog-codemirror": {"version": "3.0.0", "dev": true}, "conventional-changelog-conventionalcommits": {"version": "6.1.0", "dev": true, "requires": {"compare-func": "^2.0.0"}}, "conventional-changelog-core": {"version": "5.0.2", "dev": true, "requires": {"add-stream": "^1.0.0", "conventional-changelog-writer": "^6.0.0", "conventional-commits-parser": "^4.0.0", "dateformat": "^3.0.3", "get-pkg-repo": "^4.2.1", "git-raw-commits": "^3.0.0", "git-remote-origin-url": "^2.0.0", "git-semver-tags": "^5.0.0", "normalize-package-data": "^3.0.3", "read-pkg": "^3.0.0", "read-pkg-up": "^3.0.0"}}, "conventional-changelog-ember": {"version": "3.0.0", "dev": true}, "conventional-changelog-eslint": {"version": "4.0.0", "dev": true}, "conventional-changelog-express": {"version": "3.0.0", "dev": true}, "conventional-changelog-jquery": {"version": "4.0.0", "dev": true}, "conventional-changelog-jshint": {"version": "3.0.0", "dev": true, "requires": {"compare-func": "^2.0.0"}}, "conventional-changelog-preset-loader": {"version": "3.0.0", "dev": true}, "conventional-changelog-writer": {"version": "6.0.1", "dev": true, "requires": {"conventional-commits-filter": "^3.0.0", "dateformat": "^3.0.3", "handlebars": "^4.7.7", "json-stringify-safe": "^5.0.1", "meow": "^8.1.2", "semver": "^7.0.0", "split": "^1.0.1"}, "dependencies": {"lru-cache": {"version": "6.0.0", "dev": true, "requires": {"yallist": "^4.0.0"}}, "semver": {"version": "7.5.4", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "yallist": {"version": "4.0.0", "dev": true}}}, "conventional-commits-filter": {"version": "3.0.0", "dev": true, "requires": {"lodash.ismatch": "^4.4.0", "modify-values": "^1.0.1"}}, "conventional-commits-parser": {"version": "4.0.0", "dev": true, "requires": {"is-text-path": "^1.0.1", "JSONStream": "^1.3.5", "meow": "^8.1.2", "split2": "^3.2.2"}}, "conventional-recommended-bump": {"version": "7.0.1", "dev": true, "requires": {"concat-stream": "^2.0.0", "conventional-changelog-preset-loader": "^3.0.0", "conventional-commits-filter": "^3.0.0", "conventional-commits-parser": "^4.0.0", "git-raw-commits": "^3.0.0", "git-semver-tags": "^5.0.0", "meow": "^8.1.2"}}, "convert-source-map": {"version": "1.9.0", "dev": true}, "core-js": {"version": "3.30.1", "optional": true}, "core-util-is": {"version": "1.0.3", "dev": true}, "cosmiconfig": {"version": "8.2.0", "dev": true, "requires": {"import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "parse-json": "^5.0.0", "path-type": "^4.0.0"}}, "cross-spawn": {"version": "7.0.3", "dev": true, "requires": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}}, "crypto-random-string": {"version": "4.0.0", "dev": true, "requires": {"type-fest": "^1.0.1"}, "dependencies": {"type-fest": {"version": "1.4.0", "dev": true}}}, "css-line-break": {"version": "2.1.0", "optional": true, "requires": {"utrie": "^1.0.2"}}, "csscolorparser": {"version": "1.0.3", "peer": true}, "cssesc": {"version": "3.0.0", "dev": true}, "cssstyle": {"version": "3.0.0", "dev": true, "requires": {"rrweb-cssom": "^0.6.0"}}, "csstype": {"version": "3.1.2", "dev": true}, "dargs": {"version": "7.0.0", "dev": true}, "data-uri-to-buffer": {"version": "4.0.1", "dev": true}, "data-urls": {"version": "4.0.0", "dev": true, "requires": {"abab": "^2.0.6", "whatwg-mimetype": "^3.0.0", "whatwg-url": "^12.0.0"}, "dependencies": {"tr46": {"version": "4.1.1", "dev": true, "requires": {"punycode": "^2.3.0"}}, "webidl-conversions": {"version": "7.0.0", "dev": true}, "whatwg-url": {"version": "12.0.1", "dev": true, "requires": {"tr46": "^4.1.1", "webidl-conversions": "^7.0.0"}}}}, "dateformat": {"version": "3.0.3", "dev": true}, "de-indent": {"version": "1.0.2", "dev": true}, "debug": {"version": "4.3.4", "dev": true, "requires": {"ms": "2.1.2"}}, "decamelize": {"version": "1.2.0", "dev": true}, "decamelize-keys": {"version": "1.1.1", "dev": true, "requires": {"decamelize": "^1.1.0", "map-obj": "^1.0.0"}, "dependencies": {"map-obj": {"version": "1.0.1", "dev": true}}}, "decimal.js": {"version": "10.4.3", "dev": true}, "decompress-response": {"version": "6.0.0", "dev": true, "requires": {"mimic-response": "^3.1.0"}, "dependencies": {"mimic-response": {"version": "3.1.0", "dev": true}}}, "deep-eql": {"version": "4.1.3", "dev": true, "requires": {"type-detect": "^4.0.0"}}, "deep-extend": {"version": "0.6.0", "dev": true}, "deep-is": {"version": "0.1.4", "dev": true}, "default-browser": {"version": "4.0.0", "dev": true, "requires": {"bundle-name": "^3.0.0", "default-browser-id": "^3.0.0", "execa": "^7.1.1", "titleize": "^3.0.0"}, "dependencies": {"execa": {"version": "7.1.1", "dev": true, "requires": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.1", "human-signals": "^4.3.0", "is-stream": "^3.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^5.1.0", "onetime": "^6.0.0", "signal-exit": "^3.0.7", "strip-final-newline": "^3.0.0"}}, "get-stream": {"version": "6.0.1", "dev": true}, "human-signals": {"version": "4.3.1", "dev": true}, "is-stream": {"version": "3.0.0", "dev": true}, "mimic-fn": {"version": "4.0.0", "dev": true}, "npm-run-path": {"version": "5.1.0", "dev": true, "requires": {"path-key": "^4.0.0"}}, "onetime": {"version": "6.0.0", "dev": true, "requires": {"mimic-fn": "^4.0.0"}}, "path-key": {"version": "4.0.0", "dev": true}, "strip-final-newline": {"version": "3.0.0", "dev": true}}}, "default-browser-id": {"version": "3.0.0", "dev": true, "requires": {"bplist-parser": "^0.2.0", "untildify": "^4.0.0"}}, "defaults": {"version": "1.0.4", "dev": true, "requires": {"clone": "^1.0.2"}}, "defer-to-connect": {"version": "2.0.1", "dev": true}, "define-lazy-prop": {"version": "3.0.0", "dev": true}, "define-properties": {"version": "1.2.0", "dev": true, "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}, "degenerator": {"version": "5.0.1", "dev": true, "requires": {"ast-types": "^0.13.4", "escodegen": "^2.1.0", "esprima": "^4.0.1"}}, "delayed-stream": {"version": "1.0.0", "dev": true}, "deprecation": {"version": "2.3.1", "dev": true}, "diff-sequences": {"version": "29.4.3", "dev": true}, "dir-glob": {"version": "3.0.1", "dev": true, "requires": {"path-type": "^4.0.0"}}, "doctrine": {"version": "3.0.0", "dev": true, "requires": {"esutils": "^2.0.2"}}, "domexception": {"version": "4.0.0", "dev": true, "requires": {"webidl-conversions": "^7.0.0"}, "dependencies": {"webidl-conversions": {"version": "7.0.0", "dev": true}}}, "dompurify": {"version": "2.4.5", "optional": true}, "dot-prop": {"version": "6.0.1", "dev": true, "requires": {"is-obj": "^2.0.0"}}, "earcut": {"version": "2.2.4", "peer": true}, "eastasianwidth": {"version": "0.2.0", "dev": true}, "editorconfig": {"version": "1.0.4", "dev": true, "requires": {"@one-ini/wasm": "0.1.1", "commander": "^10.0.0", "minimatch": "9.0.1", "semver": "^7.5.3"}, "dependencies": {"brace-expansion": {"version": "2.0.1", "dev": true, "requires": {"balanced-match": "^1.0.0"}}, "lru-cache": {"version": "6.0.0", "dev": true, "requires": {"yallist": "^4.0.0"}}, "minimatch": {"version": "9.0.1", "dev": true, "requires": {"brace-expansion": "^2.0.1"}}, "semver": {"version": "7.5.4", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "yallist": {"version": "4.0.0", "dev": true}}}, "electron-to-chromium": {"version": "1.4.495", "resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.4.495.tgz", "integrity": "sha512-mwknuemBZnoOCths4GtpU/SDuVMp3uQHKa2UNJT9/aVD6WVRjGpXOxRGX7lm6ILIenTdGXPSTCTDaWos5tEU8Q==", "dev": true, "peer": true}, "emoji-regex": {"version": "9.2.2", "dev": true}, "end-of-stream": {"version": "1.4.4", "dev": true, "requires": {"once": "^1.4.0"}}, "entities": {"version": "4.5.0", "dev": true}, "error-ex": {"version": "1.3.2", "dev": true, "requires": {"is-arrayish": "^0.2.1"}}, "es-abstract": {"version": "1.21.2", "dev": true, "requires": {"array-buffer-byte-length": "^1.0.0", "available-typed-arrays": "^1.0.5", "call-bind": "^1.0.2", "es-set-tostringtag": "^2.0.1", "es-to-primitive": "^1.2.1", "function.prototype.name": "^1.1.5", "get-intrinsic": "^1.2.0", "get-symbol-description": "^1.0.0", "globalthis": "^1.0.3", "gopd": "^1.0.1", "has": "^1.0.3", "has-property-descriptors": "^1.0.0", "has-proto": "^1.0.1", "has-symbols": "^1.0.3", "internal-slot": "^1.0.5", "is-array-buffer": "^3.0.2", "is-callable": "^1.2.7", "is-negative-zero": "^2.0.2", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.2", "is-string": "^1.0.7", "is-typed-array": "^1.1.10", "is-weakref": "^1.0.2", "object-inspect": "^1.12.3", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.4.3", "safe-regex-test": "^1.0.0", "string.prototype.trim": "^1.2.7", "string.prototype.trimend": "^1.0.6", "string.prototype.trimstart": "^1.0.6", "typed-array-length": "^1.0.4", "unbox-primitive": "^1.0.2", "which-typed-array": "^1.1.9"}}, "es-array-method-boxes-properly": {"version": "1.0.0", "dev": true}, "es-get-iterator": {"version": "1.1.3", "dev": true, "requires": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.3", "has-symbols": "^1.0.3", "is-arguments": "^1.1.1", "is-map": "^2.0.2", "is-set": "^2.0.2", "is-string": "^1.0.7", "isarray": "^2.0.5", "stop-iteration-iterator": "^1.0.0"}}, "es-set-tostringtag": {"version": "2.0.1", "dev": true, "requires": {"get-intrinsic": "^1.1.3", "has": "^1.0.3", "has-tostringtag": "^1.0.0"}}, "es-to-primitive": {"version": "1.2.1", "dev": true, "requires": {"is-callable": "^1.1.4", "is-date-object": "^1.0.1", "is-symbol": "^1.0.2"}}, "esbuild": {"version": "0.18.11", "dev": true, "requires": {"@esbuild/android-arm": "0.18.11", "@esbuild/android-arm64": "0.18.11", "@esbuild/android-x64": "0.18.11", "@esbuild/darwin-arm64": "0.18.11", "@esbuild/darwin-x64": "0.18.11", "@esbuild/freebsd-arm64": "0.18.11", "@esbuild/freebsd-x64": "0.18.11", "@esbuild/linux-arm": "0.18.11", "@esbuild/linux-arm64": "0.18.11", "@esbuild/linux-ia32": "0.18.11", "@esbuild/linux-loong64": "0.18.11", "@esbuild/linux-mips64el": "0.18.11", "@esbuild/linux-ppc64": "0.18.11", "@esbuild/linux-riscv64": "0.18.11", "@esbuild/linux-s390x": "0.18.11", "@esbuild/linux-x64": "0.18.11", "@esbuild/netbsd-x64": "0.18.11", "@esbuild/openbsd-x64": "0.18.11", "@esbuild/sunos-x64": "0.18.11", "@esbuild/win32-arm64": "0.18.11", "@esbuild/win32-ia32": "0.18.11", "@esbuild/win32-x64": "0.18.11"}}, "escalade": {"version": "3.1.1", "dev": true}, "escape-goat": {"version": "4.0.0", "dev": true}, "escape-string-regexp": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==", "dev": true}, "escodegen": {"version": "2.1.0", "dev": true, "requires": {"esprima": "^4.0.1", "estraverse": "^5.2.0", "esutils": "^2.0.2", "source-map": "~0.6.1"}, "dependencies": {"estraverse": {"version": "5.3.0", "dev": true}}}, "eslint": {"version": "8.45.0", "dev": true, "requires": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.4.0", "@eslint/eslintrc": "^2.1.0", "@eslint/js": "8.44.0", "@humanwhocodes/config-array": "^0.11.10", "@humanwhocodes/module-importer": "^1.0.1", "@nodelib/fs.walk": "^1.2.8", "ajv": "^6.10.0", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.3.2", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.2.0", "eslint-visitor-keys": "^3.4.1", "espree": "^9.6.0", "esquery": "^1.4.2", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "globals": "^13.19.0", "graphemer": "^1.4.0", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "is-path-inside": "^3.0.3", "js-yaml": "^4.1.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3", "strip-ansi": "^6.0.1", "text-table": "^0.2.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.2", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "dev": true}, "escape-string-regexp": {"version": "4.0.0", "dev": true}, "eslint-scope": {"version": "7.2.0", "dev": true, "requires": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}}, "eslint-visitor-keys": {"version": "3.4.1", "dev": true}, "estraverse": {"version": "5.3.0", "dev": true}, "globals": {"version": "13.20.0", "dev": true, "requires": {"type-fest": "^0.20.2"}}, "has-flag": {"version": "4.0.0", "dev": true}, "supports-color": {"version": "7.2.0", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "eslint-config-prettier": {"version": "8.8.0", "dev": true, "requires": {}}, "eslint-plugin-prettier": {"version": "4.2.1", "dev": true, "requires": {"prettier-linter-helpers": "^1.0.0"}}, "eslint-plugin-vue": {"version": "9.15.1", "dev": true, "requires": {"@eslint-community/eslint-utils": "^4.3.0", "natural-compare": "^1.4.0", "nth-check": "^2.0.1", "postcss-selector-parser": "^6.0.9", "semver": "^7.3.5", "vue-eslint-parser": "^9.3.0", "xml-name-validator": "^4.0.0"}, "dependencies": {"lru-cache": {"version": "6.0.0", "dev": true, "requires": {"yallist": "^4.0.0"}}, "semver": {"version": "7.4.0", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "yallist": {"version": "4.0.0", "dev": true}}}, "eslint-scope": {"version": "5.1.1", "dev": true, "requires": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}}, "eslint-visitor-keys": {"version": "2.1.0", "dev": true}, "espree": {"version": "9.6.0", "dev": true, "requires": {"acorn": "^8.9.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}, "dependencies": {"eslint-visitor-keys": {"version": "3.4.1", "dev": true}}}, "esprima": {"version": "4.0.1", "dev": true}, "esquery": {"version": "1.5.0", "dev": true, "requires": {"estraverse": "^5.1.0"}, "dependencies": {"estraverse": {"version": "5.3.0", "dev": true}}}, "esrecurse": {"version": "4.3.0", "dev": true, "requires": {"estraverse": "^5.2.0"}, "dependencies": {"estraverse": {"version": "5.3.0", "dev": true}}}, "estraverse": {"version": "4.3.0", "dev": true}, "estree-walker": {"version": "2.0.2", "dev": true}, "esutils": {"version": "2.0.3", "dev": true}, "execa": {"version": "4.1.0", "dev": true, "requires": {"cross-spawn": "^7.0.0", "get-stream": "^5.0.0", "human-signals": "^1.1.1", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "onetime": "^5.1.0", "signal-exit": "^3.0.2", "strip-final-newline": "^2.0.0"}}, "external-editor": {"version": "3.1.0", "dev": true, "requires": {"chardet": "^0.7.0", "iconv-lite": "^0.4.24", "tmp": "^0.0.33"}}, "fast-deep-equal": {"version": "3.1.3", "dev": true}, "fast-diff": {"version": "1.2.0", "dev": true}, "fast-glob": {"version": "3.3.0", "dev": true, "requires": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}, "dependencies": {"glob-parent": {"version": "5.1.2", "dev": true, "requires": {"is-glob": "^4.0.1"}}}}, "fast-json-stable-stringify": {"version": "2.1.0", "dev": true}, "fast-levenshtein": {"version": "2.0.6", "dev": true}, "fastq": {"version": "1.15.0", "dev": true, "requires": {"reusify": "^1.0.4"}}, "fetch-blob": {"version": "3.2.0", "dev": true, "requires": {"node-domexception": "^1.0.0", "web-streams-polyfill": "^3.0.3"}}, "fflate": {"version": "0.4.8"}, "figures": {"version": "5.0.0", "dev": true, "requires": {"escape-string-regexp": "^5.0.0", "is-unicode-supported": "^1.2.0"}, "dependencies": {"escape-string-regexp": {"version": "5.0.0", "dev": true}}}, "file-entry-cache": {"version": "6.0.1", "dev": true, "requires": {"flat-cache": "^3.0.4"}}, "file-saver": {"version": "2.0.5"}, "fill-range": {"version": "7.0.1", "dev": true, "requires": {"to-regex-range": "^5.0.1"}}, "find-up": {"version": "5.0.0", "dev": true, "requires": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}}, "flat-cache": {"version": "3.0.4", "dev": true, "requires": {"flatted": "^3.1.0", "rimraf": "^3.0.2"}}, "flatted": {"version": "3.2.7", "dev": true}, "focus-trap": {"version": "7.5.2", "resolved": "https://registry.npmjs.org/focus-trap/-/focus-trap-7.5.2.tgz", "integrity": "sha512-p6vGNNWLDGwJCiEjkSK6oERj/hEyI9ITsSwIUICBoKLlWiTWXJRfQibCwcoi50rTZdbi87qDtUlMCmQwsGSgPw==", "dev": true, "requires": {"tabbable": "^6.2.0"}}, "follow-redirects": {"version": "1.5.10", "dev": true, "requires": {"debug": "=3.1.0"}, "dependencies": {"debug": {"version": "3.1.0", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "dev": true}}}, "for-each": {"version": "0.3.3", "dev": true, "requires": {"is-callable": "^1.1.3"}}, "form-data": {"version": "4.0.0", "dev": true, "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}}, "form-data-encoder": {"version": "2.1.4", "dev": true}, "formdata-polyfill": {"version": "4.0.10", "dev": true, "requires": {"fetch-blob": "^3.1.2"}}, "fs-extra": {"version": "8.1.0", "dev": true, "requires": {"graceful-fs": "^4.2.0", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}}, "fs.realpath": {"version": "1.0.0", "dev": true}, "function-bind": {"version": "1.1.1", "dev": true}, "function.prototype.name": {"version": "1.1.5", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.3", "es-abstract": "^1.19.0", "functions-have-names": "^1.2.2"}}, "functions-have-names": {"version": "1.2.3", "dev": true}, "gensync": {"version": "1.0.0-beta.2", "resolved": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz", "integrity": "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==", "dev": true, "peer": true}, "geotiff": {"version": "2.0.7", "peer": true, "requires": {"@petamoriken/float16": "^3.4.7", "lerc": "^3.0.0", "pako": "^2.0.4", "parse-headers": "^2.0.2", "quick-lru": "^6.1.1", "web-worker": "^1.2.0", "xml-utils": "^1.0.2"}}, "get-caller-file": {"version": "2.0.5", "dev": true}, "get-func-name": {"version": "2.0.0", "dev": true}, "get-intrinsic": {"version": "1.2.0", "dev": true, "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}}, "get-pkg": {"version": "1.1.0", "dev": true, "requires": {"axios": "^0.18.0"}}, "get-pkg-repo": {"version": "4.2.1", "dev": true, "requires": {"@hutson/parse-repository-url": "^3.0.0", "hosted-git-info": "^4.0.0", "through2": "^2.0.0", "yargs": "^16.2.0"}}, "get-repository-url": {"version": "2.0.0", "dev": true, "requires": {"get-pkg": "^1.0.0", "parse-github-url": "^1.0.2"}}, "get-stream": {"version": "5.2.0", "dev": true, "requires": {"pump": "^3.0.0"}}, "get-symbol-description": {"version": "1.0.0", "dev": true, "requires": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.1"}}, "get-uri": {"version": "6.0.1", "dev": true, "requires": {"basic-ftp": "^5.0.2", "data-uri-to-buffer": "^5.0.1", "debug": "^4.3.4", "fs-extra": "^8.1.0"}, "dependencies": {"data-uri-to-buffer": {"version": "5.0.1", "dev": true}}}, "git-raw-commits": {"version": "3.0.0", "dev": true, "requires": {"dargs": "^7.0.0", "meow": "^8.1.2", "split2": "^3.2.2"}}, "git-remote-origin-url": {"version": "2.0.0", "dev": true, "requires": {"gitconfiglocal": "^1.0.0", "pify": "^2.3.0"}}, "git-semver-tags": {"version": "5.0.1", "dev": true, "requires": {"meow": "^8.1.2", "semver": "^7.0.0"}, "dependencies": {"lru-cache": {"version": "6.0.0", "dev": true, "requires": {"yallist": "^4.0.0"}}, "semver": {"version": "7.5.4", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "yallist": {"version": "4.0.0", "dev": true}}}, "git-up": {"version": "7.0.0", "dev": true, "requires": {"is-ssh": "^1.4.0", "parse-url": "^8.1.0"}}, "git-url-parse": {"version": "13.1.0", "dev": true, "requires": {"git-up": "^7.0.0"}}, "gitconfiglocal": {"version": "1.0.0", "dev": true, "requires": {"ini": "^1.3.2"}, "dependencies": {"ini": {"version": "1.3.8", "dev": true}}}, "glob": {"version": "7.2.3", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "glob-parent": {"version": "6.0.2", "dev": true, "requires": {"is-glob": "^4.0.3"}}, "global-dirs": {"version": "3.0.1", "dev": true, "requires": {"ini": "2.0.0"}}, "globals": {"version": "11.12.0", "resolved": "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz", "integrity": "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==", "dev": true, "peer": true}, "globalthis": {"version": "1.0.3", "dev": true, "requires": {"define-properties": "^1.1.3"}}, "globby": {"version": "11.1.0", "dev": true, "requires": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.2.9", "ignore": "^5.2.0", "merge2": "^1.4.1", "slash": "^3.0.0"}}, "gopd": {"version": "1.0.1", "dev": true, "requires": {"get-intrinsic": "^1.1.3"}}, "got": {"version": "12.6.0", "dev": true, "requires": {"@sindresorhus/is": "^5.2.0", "@szmarczak/http-timer": "^5.0.1", "cacheable-lookup": "^7.0.0", "cacheable-request": "^10.2.8", "decompress-response": "^6.0.0", "form-data-encoder": "^2.1.2", "get-stream": "^6.0.1", "http2-wrapper": "^2.1.10", "lowercase-keys": "^3.0.0", "p-cancelable": "^3.0.0", "responselike": "^3.0.0"}, "dependencies": {"get-stream": {"version": "6.0.1", "dev": true}}}, "graceful-fs": {"version": "4.2.11", "dev": true}, "grapheme-splitter": {"version": "1.0.4", "dev": true}, "graphemer": {"version": "1.4.0", "dev": true}, "handlebars": {"version": "4.7.7", "dev": true, "requires": {"minimist": "^1.2.5", "neo-async": "^2.6.0", "source-map": "^0.6.1", "uglify-js": "^3.1.4", "wordwrap": "^1.0.0"}}, "hard-rejection": {"version": "2.1.0", "dev": true}, "has": {"version": "1.0.3", "dev": true, "requires": {"function-bind": "^1.1.1"}}, "has-bigints": {"version": "1.0.2", "dev": true}, "has-flag": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==", "dev": true}, "has-property-descriptors": {"version": "1.0.0", "dev": true, "requires": {"get-intrinsic": "^1.1.1"}}, "has-proto": {"version": "1.0.1", "dev": true}, "has-symbols": {"version": "1.0.3", "dev": true}, "has-tostringtag": {"version": "1.0.0", "dev": true, "requires": {"has-symbols": "^1.0.2"}}, "has-yarn": {"version": "3.0.0", "dev": true}, "he": {"version": "1.2.0", "dev": true}, "hosted-git-info": {"version": "4.1.0", "dev": true, "requires": {"lru-cache": "^6.0.0"}, "dependencies": {"lru-cache": {"version": "6.0.0", "dev": true, "requires": {"yallist": "^4.0.0"}}, "yallist": {"version": "4.0.0", "dev": true}}}, "html-encoding-sniffer": {"version": "3.0.0", "dev": true, "requires": {"whatwg-encoding": "^2.0.0"}}, "html-escaper": {"version": "2.0.2", "dev": true}, "html2canvas": {"version": "1.4.1", "optional": true, "requires": {"css-line-break": "^2.1.0", "text-segmentation": "^1.0.3"}}, "http-cache-semantics": {"version": "4.1.1", "dev": true}, "http-proxy-agent": {"version": "7.0.0", "dev": true, "requires": {"agent-base": "^7.1.0", "debug": "^4.3.4"}, "dependencies": {"agent-base": {"version": "7.1.0", "dev": true, "requires": {"debug": "^4.3.4"}}}}, "http2-wrapper": {"version": "2.2.0", "dev": true, "requires": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.2.0"}, "dependencies": {"quick-lru": {"version": "5.1.1", "dev": true}}}, "https-proxy-agent": {"version": "5.0.1", "dev": true, "requires": {"agent-base": "6", "debug": "4"}}, "human-signals": {"version": "1.1.1", "dev": true}, "husky": {"version": "8.0.3", "dev": true}, "iconv-lite": {"version": "0.4.24", "dev": true, "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "ieee754": {"version": "1.2.1"}, "ignore": {"version": "5.2.4", "dev": true}, "import-fresh": {"version": "3.3.0", "dev": true, "requires": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}}, "import-lazy": {"version": "4.0.0", "dev": true}, "imurmurhash": {"version": "0.1.4", "dev": true}, "indent-string": {"version": "4.0.0", "dev": true}, "inflight": {"version": "1.0.6", "dev": true, "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.4", "dev": true}, "ini": {"version": "2.0.0", "dev": true}, "inquirer": {"version": "9.2.8", "dev": true, "requires": {"ansi-escapes": "^4.3.2", "chalk": "^5.3.0", "cli-cursor": "^3.1.0", "cli-width": "^4.0.0", "external-editor": "^3.0.3", "figures": "^5.0.0", "lodash": "^4.17.21", "mute-stream": "1.0.0", "ora": "^5.4.1", "run-async": "^3.0.0", "rxjs": "^7.8.1", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "through": "^2.3.6", "wrap-ansi": "^6.0.1"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "bl": {"version": "4.1.0", "dev": true, "requires": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}}, "buffer": {"version": "5.7.1", "dev": true, "requires": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "chalk": {"version": "5.3.0", "dev": true}, "color-convert": {"version": "2.0.1", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "dev": true}, "emoji-regex": {"version": "8.0.0", "dev": true}, "has-flag": {"version": "4.0.0", "dev": true}, "is-interactive": {"version": "1.0.0", "dev": true}, "is-unicode-supported": {"version": "0.1.0", "dev": true}, "log-symbols": {"version": "4.1.0", "dev": true, "requires": {"chalk": "^4.1.0", "is-unicode-supported": "^0.1.0"}, "dependencies": {"chalk": {"version": "4.1.2", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}}}, "ora": {"version": "5.4.1", "dev": true, "requires": {"bl": "^4.1.0", "chalk": "^4.1.0", "cli-cursor": "^3.1.0", "cli-spinners": "^2.5.0", "is-interactive": "^1.0.0", "is-unicode-supported": "^0.1.0", "log-symbols": "^4.1.0", "strip-ansi": "^6.0.0", "wcwidth": "^1.0.1"}, "dependencies": {"chalk": {"version": "4.1.2", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}}}, "string-width": {"version": "4.2.3", "dev": true, "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "supports-color": {"version": "7.2.0", "dev": true, "requires": {"has-flag": "^4.0.0"}}, "wrap-ansi": {"version": "6.2.0", "dev": true, "requires": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}}}}, "internal-slot": {"version": "1.0.5", "dev": true, "requires": {"get-intrinsic": "^1.2.0", "has": "^1.0.3", "side-channel": "^1.0.4"}}, "interpret": {"version": "1.4.0", "dev": true}, "ip": {"version": "1.1.8", "dev": true}, "is-arguments": {"version": "1.1.1", "dev": true, "requires": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}}, "is-array-buffer": {"version": "3.0.2", "dev": true, "requires": {"call-bind": "^1.0.2", "get-intrinsic": "^1.2.0", "is-typed-array": "^1.1.10"}}, "is-arrayish": {"version": "0.2.1", "dev": true}, "is-bigint": {"version": "1.0.4", "dev": true, "requires": {"has-bigints": "^1.0.1"}}, "is-boolean-object": {"version": "1.1.2", "dev": true, "requires": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}}, "is-buffer": {"version": "2.0.5", "dev": true}, "is-callable": {"version": "1.2.7", "dev": true}, "is-ci": {"version": "3.0.1", "dev": true, "requires": {"ci-info": "^3.2.0"}}, "is-core-module": {"version": "2.12.0", "dev": true, "requires": {"has": "^1.0.3"}}, "is-date-object": {"version": "1.0.5", "dev": true, "requires": {"has-tostringtag": "^1.0.0"}}, "is-docker": {"version": "3.0.0", "dev": true}, "is-extglob": {"version": "2.1.1", "dev": true}, "is-fullwidth-code-point": {"version": "3.0.0", "dev": true}, "is-glob": {"version": "4.0.3", "dev": true, "requires": {"is-extglob": "^2.1.1"}}, "is-inside-container": {"version": "1.0.0", "dev": true, "requires": {"is-docker": "^3.0.0"}}, "is-installed-globally": {"version": "0.4.0", "dev": true, "requires": {"global-dirs": "^3.0.0", "is-path-inside": "^3.0.2"}}, "is-interactive": {"version": "2.0.0", "dev": true}, "is-map": {"version": "2.0.2", "dev": true}, "is-negative-zero": {"version": "2.0.2", "dev": true}, "is-npm": {"version": "6.0.0", "dev": true}, "is-number": {"version": "7.0.0", "dev": true}, "is-number-object": {"version": "1.0.7", "dev": true, "requires": {"has-tostringtag": "^1.0.0"}}, "is-obj": {"version": "2.0.0", "dev": true}, "is-path-inside": {"version": "3.0.3", "dev": true}, "is-plain-obj": {"version": "1.1.0", "dev": true}, "is-plain-object": {"version": "5.0.0", "dev": true}, "is-potential-custom-element-name": {"version": "1.0.1", "dev": true}, "is-regex": {"version": "1.1.4", "dev": true, "requires": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}}, "is-set": {"version": "2.0.2", "dev": true}, "is-shared-array-buffer": {"version": "1.0.2", "dev": true, "requires": {"call-bind": "^1.0.2"}}, "is-ssh": {"version": "1.4.0", "dev": true, "requires": {"protocols": "^2.0.1"}}, "is-stream": {"version": "2.0.1", "dev": true}, "is-string": {"version": "1.0.7", "dev": true, "requires": {"has-tostringtag": "^1.0.0"}}, "is-symbol": {"version": "1.0.4", "dev": true, "requires": {"has-symbols": "^1.0.2"}}, "is-text-path": {"version": "1.0.1", "dev": true, "requires": {"text-extensions": "^1.0.0"}}, "is-typed-array": {"version": "1.1.10", "dev": true, "requires": {"available-typed-arrays": "^1.0.5", "call-bind": "^1.0.2", "for-each": "^0.3.3", "gopd": "^1.0.1", "has-tostringtag": "^1.0.0"}}, "is-typedarray": {"version": "1.0.0", "dev": true}, "is-unicode-supported": {"version": "1.3.0", "dev": true}, "is-weakref": {"version": "1.0.2", "dev": true, "requires": {"call-bind": "^1.0.2"}}, "is-wsl": {"version": "2.2.0", "dev": true, "requires": {"is-docker": "^2.0.0"}, "dependencies": {"is-docker": {"version": "2.2.1", "dev": true}}}, "is-yarn-global": {"version": "0.4.1", "dev": true}, "isarray": {"version": "2.0.5", "dev": true}, "isexe": {"version": "2.0.0", "dev": true}, "issue-parser": {"version": "6.0.0", "dev": true, "requires": {"lodash.capitalize": "^4.2.1", "lodash.escaperegexp": "^4.1.2", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.uniqby": "^4.7.0"}}, "istanbul-lib-coverage": {"version": "3.2.0", "dev": true}, "istanbul-lib-report": {"version": "3.0.0", "dev": true, "requires": {"istanbul-lib-coverage": "^3.0.0", "make-dir": "^3.0.0", "supports-color": "^7.1.0"}, "dependencies": {"has-flag": {"version": "4.0.0", "dev": true}, "supports-color": {"version": "7.2.0", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "istanbul-lib-source-maps": {"version": "4.0.1", "dev": true, "requires": {"debug": "^4.1.1", "istanbul-lib-coverage": "^3.0.0", "source-map": "^0.6.1"}}, "istanbul-reports": {"version": "3.1.5", "dev": true, "requires": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}}, "iterate-iterator": {"version": "1.0.2", "dev": true}, "iterate-value": {"version": "1.0.2", "dev": true, "requires": {"es-get-iterator": "^1.0.2", "iterate-iterator": "^1.0.1"}}, "jju": {"version": "1.4.0", "dev": true}, "js-beautify": {"version": "1.14.9", "dev": true, "requires": {"config-chain": "^1.1.13", "editorconfig": "^1.0.3", "glob": "^8.1.0", "nopt": "^6.0.0"}, "dependencies": {"brace-expansion": {"version": "2.0.1", "dev": true, "requires": {"balanced-match": "^1.0.0"}}, "glob": {"version": "8.1.0", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^5.0.1", "once": "^1.3.0"}}, "minimatch": {"version": "5.1.6", "dev": true, "requires": {"brace-expansion": "^2.0.1"}}}}, "js-tokens": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==", "dev": true}, "js-yaml": {"version": "4.1.0", "dev": true, "requires": {"argparse": "^2.0.1"}}, "jsdom": {"version": "22.1.0", "dev": true, "requires": {"abab": "^2.0.6", "cssstyle": "^3.0.0", "data-urls": "^4.0.0", "decimal.js": "^10.4.3", "domexception": "^4.0.0", "form-data": "^4.0.0", "html-encoding-sniffer": "^3.0.0", "http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.1", "is-potential-custom-element-name": "^1.0.1", "nwsapi": "^2.2.4", "parse5": "^7.1.2", "rrweb-cssom": "^0.6.0", "saxes": "^6.0.0", "symbol-tree": "^3.2.4", "tough-cookie": "^4.1.2", "w3c-xmlserializer": "^4.0.0", "webidl-conversions": "^7.0.0", "whatwg-encoding": "^2.0.0", "whatwg-mimetype": "^3.0.0", "whatwg-url": "^12.0.1", "ws": "^8.13.0", "xml-name-validator": "^4.0.0"}, "dependencies": {"@tootallnate/once": {"version": "2.0.0", "dev": true}, "http-proxy-agent": {"version": "5.0.0", "dev": true, "requires": {"@tootallnate/once": "2", "agent-base": "6", "debug": "4"}}, "tr46": {"version": "4.1.1", "dev": true, "requires": {"punycode": "^2.3.0"}}, "webidl-conversions": {"version": "7.0.0", "dev": true}, "whatwg-url": {"version": "12.0.1", "dev": true, "requires": {"tr46": "^4.1.1", "webidl-conversions": "^7.0.0"}}}}, "jsesc": {"version": "2.5.2", "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-2.5.2.tgz", "integrity": "sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==", "dev": true, "peer": true}, "json-buffer": {"version": "3.0.1", "dev": true}, "json-parse-better-errors": {"version": "1.0.2", "dev": true}, "json-parse-even-better-errors": {"version": "2.3.1", "dev": true}, "json-schema-traverse": {"version": "0.4.1", "dev": true}, "json-stable-stringify-without-jsonify": {"version": "1.0.1", "dev": true}, "json-stringify-pretty-compact": {"version": "2.0.0", "peer": true}, "json-stringify-safe": {"version": "5.0.1", "dev": true}, "json5": {"version": "2.2.3", "resolved": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "integrity": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==", "dev": true, "peer": true}, "jsonc-parser": {"version": "3.2.0", "dev": true}, "jsonfile": {"version": "4.0.0", "dev": true, "requires": {"graceful-fs": "^4.1.6"}}, "jsonparse": {"version": "1.3.1", "dev": true}, "JSONStream": {"version": "1.3.5", "dev": true, "requires": {"jsonparse": "^1.2.0", "through": ">=2.2.7 <3"}}, "jspdf": {"version": "2.5.1", "requires": {"@babel/runtime": "^7.14.0", "atob": "^2.1.2", "btoa": "^1.2.1", "canvg": "^3.0.6", "core-js": "^3.6.0", "dompurify": "^2.2.0", "fflate": "^0.4.8", "html2canvas": "^1.0.0-rc.5"}}, "keyv": {"version": "4.5.2", "dev": true, "requires": {"json-buffer": "3.0.1"}}, "kind-of": {"version": "6.0.3", "dev": true}, "kolorist": {"version": "1.8.0", "dev": true}, "latest-version": {"version": "7.0.0", "dev": true, "requires": {"package-json": "^8.1.0"}}, "lerc": {"version": "3.0.0", "peer": true}, "levn": {"version": "0.4.1", "dev": true, "requires": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}}, "lines-and-columns": {"version": "1.2.4", "dev": true}, "load-json-file": {"version": "4.0.0", "dev": true, "requires": {"graceful-fs": "^4.1.2", "parse-json": "^4.0.0", "pify": "^3.0.0", "strip-bom": "^3.0.0"}, "dependencies": {"parse-json": {"version": "4.0.0", "dev": true, "requires": {"error-ex": "^1.3.1", "json-parse-better-errors": "^1.0.1"}}, "pify": {"version": "3.0.0", "dev": true}}}, "local-pkg": {"version": "0.4.3", "dev": true}, "locate-path": {"version": "6.0.0", "dev": true, "requires": {"p-locate": "^5.0.0"}}, "lodash": {"version": "4.17.21", "dev": true}, "lodash.capitalize": {"version": "4.2.1", "dev": true}, "lodash.escaperegexp": {"version": "4.1.2", "dev": true}, "lodash.get": {"version": "4.4.2", "dev": true}, "lodash.isequal": {"version": "4.5.0", "dev": true}, "lodash.ismatch": {"version": "4.4.0", "dev": true}, "lodash.isplainobject": {"version": "4.0.6", "dev": true}, "lodash.isstring": {"version": "4.0.1", "dev": true}, "lodash.merge": {"version": "4.6.2", "dev": true}, "lodash.uniqby": {"version": "4.7.0", "dev": true}, "log-symbols": {"version": "5.1.0", "dev": true, "requires": {"chalk": "^5.0.0", "is-unicode-supported": "^1.1.0"}, "dependencies": {"chalk": {"version": "5.3.0", "dev": true}}}, "loupe": {"version": "2.3.6", "dev": true, "requires": {"get-func-name": "^2.0.0"}}, "lowercase-keys": {"version": "3.0.0", "dev": true}, "lru-cache": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz", "integrity": "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==", "dev": true, "peer": true, "requires": {"yallist": "^3.0.2"}}, "macos-release": {"version": "3.1.0", "dev": true}, "magic-string": {"version": "0.30.1", "dev": true, "requires": {"@jridgewell/sourcemap-codec": "^1.4.15"}}, "make-dir": {"version": "3.1.0", "dev": true, "requires": {"semver": "^6.0.0"}}, "map-obj": {"version": "4.3.0", "dev": true}, "mapbox-to-css-font": {"version": "2.4.2", "peer": true}, "mark.js": {"version": "8.11.1", "dev": true}, "meow": {"version": "8.1.2", "dev": true, "requires": {"@types/minimist": "^1.2.0", "camelcase-keys": "^6.2.2", "decamelize-keys": "^1.1.0", "hard-rejection": "^2.1.0", "minimist-options": "4.1.0", "normalize-package-data": "^3.0.0", "read-pkg-up": "^7.0.1", "redent": "^3.0.0", "trim-newlines": "^3.0.0", "type-fest": "^0.18.0", "yargs-parser": "^20.2.3"}, "dependencies": {"find-up": {"version": "4.1.0", "dev": true, "requires": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}}, "hosted-git-info": {"version": "2.8.9", "dev": true}, "locate-path": {"version": "5.0.0", "dev": true, "requires": {"p-locate": "^4.1.0"}}, "p-limit": {"version": "2.3.0", "dev": true, "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "4.1.0", "dev": true, "requires": {"p-limit": "^2.2.0"}}, "read-pkg": {"version": "5.2.0", "dev": true, "requires": {"@types/normalize-package-data": "^2.4.0", "normalize-package-data": "^2.5.0", "parse-json": "^5.0.0", "type-fest": "^0.6.0"}, "dependencies": {"normalize-package-data": {"version": "2.5.0", "dev": true, "requires": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "type-fest": {"version": "0.6.0", "dev": true}}}, "read-pkg-up": {"version": "7.0.1", "dev": true, "requires": {"find-up": "^4.1.0", "read-pkg": "^5.2.0", "type-fest": "^0.8.1"}, "dependencies": {"type-fest": {"version": "0.8.1", "dev": true}}}, "semver": {"version": "5.7.2", "dev": true}, "type-fest": {"version": "0.18.1", "dev": true}, "yargs-parser": {"version": "20.2.9", "dev": true}}}, "merge-stream": {"version": "2.0.0", "dev": true}, "merge2": {"version": "1.4.1", "dev": true}, "mgrs": {"version": "1.0.0"}, "micromatch": {"version": "4.0.5", "dev": true, "requires": {"braces": "^3.0.2", "picomatch": "^2.3.1"}}, "mime-db": {"version": "1.52.0", "dev": true}, "mime-types": {"version": "2.1.35", "dev": true, "requires": {"mime-db": "1.52.0"}}, "mimic-fn": {"version": "2.1.0", "dev": true}, "mimic-response": {"version": "4.0.0", "dev": true}, "min-indent": {"version": "1.0.1", "dev": true}, "minimatch": {"version": "3.1.2", "dev": true, "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "1.2.8"}, "minimist-options": {"version": "4.1.0", "dev": true, "requires": {"arrify": "^1.0.1", "is-plain-obj": "^1.1.0", "kind-of": "^6.0.3"}, "dependencies": {"arrify": {"version": "1.0.1", "dev": true}}}, "minisearch": {"version": "6.1.0", "dev": true}, "mlly": {"version": "1.4.0", "dev": true, "requires": {"acorn": "^8.9.0", "pathe": "^1.1.1", "pkg-types": "^1.0.3", "ufo": "^1.1.2"}}, "modify-values": {"version": "1.0.1", "dev": true}, "mri": {"version": "1.2.0", "dev": true}, "ms": {"version": "2.1.2", "dev": true}, "muggle-string": {"version": "0.3.1", "dev": true}, "multimatch": {"version": "4.0.0", "dev": true, "requires": {"@types/minimatch": "^3.0.3", "array-differ": "^3.0.0", "array-union": "^2.1.0", "arrify": "^2.0.1", "minimatch": "^3.0.4"}}, "mute-stream": {"version": "1.0.0", "dev": true}, "nanoid": {"version": "3.3.6", "dev": true}, "natural-compare": {"version": "1.4.0", "dev": true}, "natural-compare-lite": {"version": "1.4.0", "dev": true}, "neo-async": {"version": "2.6.2", "dev": true}, "netmask": {"version": "2.0.2", "dev": true}, "new-github-release-url": {"version": "2.0.0", "dev": true, "requires": {"type-fest": "^2.5.1"}, "dependencies": {"type-fest": {"version": "2.19.0", "dev": true}}}, "node-domexception": {"version": "1.0.0", "dev": true}, "node-fetch": {"version": "3.3.1", "dev": true, "requires": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}}, "node-releases": {"version": "2.0.13", "resolved": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.13.tgz", "integrity": "sha512-uYr7J37ae/ORWdZeQ1xxMJe3NtdmqMC/JZK+geofDrkLUApKRHPd18/TxtBOJ4A0/+uUIliorNrfYV6s1b02eQ==", "dev": true, "peer": true}, "nopt": {"version": "6.0.0", "dev": true, "requires": {"abbrev": "^1.0.0"}}, "normalize-package-data": {"version": "3.0.3", "dev": true, "requires": {"hosted-git-info": "^4.0.1", "is-core-module": "^2.5.0", "semver": "^7.3.4", "validate-npm-package-license": "^3.0.1"}, "dependencies": {"lru-cache": {"version": "6.0.0", "dev": true, "requires": {"yallist": "^4.0.0"}}, "semver": {"version": "7.5.4", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "yallist": {"version": "4.0.0", "dev": true}}}, "normalize-url": {"version": "8.0.0", "dev": true}, "npm-run-path": {"version": "4.0.1", "dev": true, "requires": {"path-key": "^3.0.0"}}, "nth-check": {"version": "2.1.1", "dev": true, "requires": {"boolbase": "^1.0.0"}}, "nwsapi": {"version": "2.2.4", "dev": true}, "object-inspect": {"version": "1.12.3", "dev": true}, "object-keys": {"version": "1.1.1", "dev": true}, "object.assign": {"version": "4.1.4", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "has-symbols": "^1.0.3", "object-keys": "^1.1.1"}}, "ol": {"version": "7.5.1", "peer": true, "requires": {"earcut": "^2.2.3", "geotiff": "^2.0.7", "ol-mapbox-style": "^10.1.0", "pbf": "3.2.1", "rbush": "^3.0.1"}}, "ol-contextmenu": {"version": "5.2.1", "peer": true, "requires": {"tiny-emitter": "^2.1.0"}}, "ol-ext": {"version": "4.0.11", "peer": true, "requires": {}}, "ol-mapbox-style": {"version": "10.7.0", "peer": true, "requires": {"@mapbox/mapbox-gl-style-spec": "^13.23.1", "mapbox-to-css-font": "^2.4.1", "ol": "^7.3.0"}}, "once": {"version": "1.4.0", "dev": true, "requires": {"wrappy": "1"}}, "onetime": {"version": "5.1.2", "dev": true, "requires": {"mimic-fn": "^2.1.0"}}, "open": {"version": "9.1.0", "dev": true, "requires": {"default-browser": "^4.0.0", "define-lazy-prop": "^3.0.0", "is-inside-container": "^1.0.0", "is-wsl": "^2.2.0"}}, "optionator": {"version": "0.9.3", "dev": true, "requires": {"@aashutoshrathi/word-wrap": "^1.2.3", "deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0"}}, "ora": {"version": "6.3.1", "dev": true, "requires": {"chalk": "^5.0.0", "cli-cursor": "^4.0.0", "cli-spinners": "^2.6.1", "is-interactive": "^2.0.0", "is-unicode-supported": "^1.1.0", "log-symbols": "^5.1.0", "stdin-discarder": "^0.1.0", "strip-ansi": "^7.0.1", "wcwidth": "^1.0.1"}, "dependencies": {"ansi-regex": {"version": "6.0.1", "dev": true}, "chalk": {"version": "5.3.0", "dev": true}, "cli-cursor": {"version": "4.0.0", "dev": true, "requires": {"restore-cursor": "^4.0.0"}}, "restore-cursor": {"version": "4.0.0", "dev": true, "requires": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}}, "strip-ansi": {"version": "7.1.0", "dev": true, "requires": {"ansi-regex": "^6.0.1"}}}}, "os-name": {"version": "5.1.0", "dev": true, "requires": {"macos-release": "^3.1.0", "windows-release": "^5.0.1"}}, "os-tmpdir": {"version": "1.0.2", "dev": true}, "p-cancelable": {"version": "3.0.0", "dev": true}, "p-limit": {"version": "3.1.0", "dev": true, "requires": {"yocto-queue": "^0.1.0"}}, "p-locate": {"version": "5.0.0", "dev": true, "requires": {"p-limit": "^3.0.2"}}, "p-try": {"version": "2.2.0", "dev": true}, "pac-proxy-agent": {"version": "7.0.0", "dev": true, "requires": {"@tootallnate/quickjs-emscripten": "^0.23.0", "agent-base": "^7.0.2", "debug": "^4.3.4", "get-uri": "^6.0.1", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.0", "pac-resolver": "^7.0.0", "socks-proxy-agent": "^8.0.1"}, "dependencies": {"agent-base": {"version": "7.1.0", "dev": true, "requires": {"debug": "^4.3.4"}}, "https-proxy-agent": {"version": "7.0.1", "dev": true, "requires": {"agent-base": "^7.0.2", "debug": "4"}}}}, "pac-resolver": {"version": "7.0.0", "dev": true, "requires": {"degenerator": "^5.0.0", "ip": "^1.1.8", "netmask": "^2.0.2"}}, "package-json": {"version": "8.1.0", "dev": true, "requires": {"got": "^12.1.0", "registry-auth-token": "^5.0.1", "registry-url": "^6.0.0", "semver": "^7.3.7"}, "dependencies": {"lru-cache": {"version": "6.0.0", "dev": true, "requires": {"yallist": "^4.0.0"}}, "semver": {"version": "7.4.0", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "yallist": {"version": "4.0.0", "dev": true}}}, "pako": {"version": "2.1.0", "peer": true}, "parent-module": {"version": "1.0.1", "dev": true, "requires": {"callsites": "^3.0.0"}}, "parse-github-url": {"version": "1.0.2", "dev": true}, "parse-headers": {"version": "2.0.5", "peer": true}, "parse-json": {"version": "5.2.0", "dev": true, "requires": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}}, "parse-path": {"version": "7.0.0", "dev": true, "requires": {"protocols": "^2.0.0"}}, "parse-url": {"version": "8.1.0", "dev": true, "requires": {"parse-path": "^7.0.0"}}, "parse5": {"version": "7.1.2", "dev": true, "requires": {"entities": "^4.4.0"}}, "path-exists": {"version": "4.0.0", "dev": true}, "path-is-absolute": {"version": "1.0.1", "dev": true}, "path-key": {"version": "3.1.1", "dev": true}, "path-parse": {"version": "1.0.7", "dev": true}, "path-type": {"version": "4.0.0", "dev": true}, "pathe": {"version": "1.1.1", "dev": true}, "pathval": {"version": "1.1.1", "dev": true}, "pbf": {"version": "3.2.1", "peer": true, "requires": {"ieee754": "^1.1.12", "resolve-protobuf-schema": "^2.1.0"}}, "performance-now": {"version": "2.1.0", "optional": true}, "picocolors": {"version": "1.0.0", "dev": true}, "picomatch": {"version": "2.3.1", "dev": true}, "pify": {"version": "2.3.0", "dev": true}, "pkg-types": {"version": "1.0.3", "dev": true, "requires": {"jsonc-parser": "^3.2.0", "mlly": "^1.2.0", "pathe": "^1.1.0"}}, "postcss": {"version": "8.4.27", "dev": true, "requires": {"nanoid": "^3.3.6", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}}, "postcss-selector-parser": {"version": "6.0.11", "dev": true, "requires": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}}, "preact": {"version": "10.15.1", "dev": true}, "prelude-ls": {"version": "1.2.1", "dev": true}, "prettier": {"version": "2.8.8", "dev": true}, "prettier-linter-helpers": {"version": "1.0.0", "dev": true, "requires": {"fast-diff": "^1.1.2"}}, "pretty-format": {"version": "29.6.1", "dev": true, "requires": {"@jest/schemas": "^29.6.0", "ansi-styles": "^5.0.0", "react-is": "^18.0.0"}, "dependencies": {"ansi-styles": {"version": "5.2.0", "dev": true}}}, "pretty-quick": {"version": "3.1.3", "dev": true, "requires": {"chalk": "^3.0.0", "execa": "^4.0.0", "find-up": "^4.1.0", "ignore": "^5.1.4", "mri": "^1.1.5", "multimatch": "^4.0.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "3.0.0", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "dev": true}, "find-up": {"version": "4.1.0", "dev": true, "requires": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}}, "has-flag": {"version": "4.0.0", "dev": true}, "locate-path": {"version": "5.0.0", "dev": true, "requires": {"p-locate": "^4.1.0"}}, "p-limit": {"version": "2.3.0", "dev": true, "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "4.1.0", "dev": true, "requires": {"p-limit": "^2.2.0"}}, "supports-color": {"version": "7.2.0", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "process-nextick-args": {"version": "2.0.1", "dev": true}, "proj4": {"version": "2.9.0", "requires": {"mgrs": "1.0.0", "wkt-parser": "^1.3.1"}}, "promise.allsettled": {"version": "1.0.6", "dev": true, "requires": {"array.prototype.map": "^1.0.5", "call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4", "get-intrinsic": "^1.1.3", "iterate-value": "^1.0.2"}}, "proto-list": {"version": "1.2.4", "dev": true}, "protocol-buffers-schema": {"version": "3.6.0", "peer": true}, "protocols": {"version": "2.0.1", "dev": true}, "proxy-agent": {"version": "6.3.0", "dev": true, "requires": {"agent-base": "^7.0.2", "debug": "^4.3.4", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.0", "lru-cache": "^7.14.1", "pac-proxy-agent": "^7.0.0", "proxy-from-env": "^1.1.0", "socks-proxy-agent": "^8.0.1"}, "dependencies": {"agent-base": {"version": "7.1.0", "dev": true, "requires": {"debug": "^4.3.4"}}, "https-proxy-agent": {"version": "7.0.1", "dev": true, "requires": {"agent-base": "^7.0.2", "debug": "4"}}, "lru-cache": {"version": "7.18.3", "dev": true}}}, "proxy-from-env": {"version": "1.1.0", "dev": true}, "psl": {"version": "1.9.0", "dev": true}, "pump": {"version": "3.0.0", "dev": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "punycode": {"version": "2.3.0", "dev": true}, "pupa": {"version": "3.1.0", "dev": true, "requires": {"escape-goat": "^4.0.0"}}, "querystringify": {"version": "2.2.0", "dev": true}, "queue-microtask": {"version": "1.2.3", "dev": true}, "quick-lru": {"version": "6.1.1", "peer": true}, "quickselect": {"version": "2.0.0", "peer": true}, "raf": {"version": "3.4.1", "optional": true, "requires": {"performance-now": "^2.1.0"}}, "rbush": {"version": "3.0.1", "peer": true, "requires": {"quickselect": "^2.0.0"}}, "rc": {"version": "1.2.8", "dev": true, "requires": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "dependencies": {"ini": {"version": "1.3.8", "dev": true}, "strip-json-comments": {"version": "2.0.1", "dev": true}}}, "react-is": {"version": "18.2.0", "dev": true}, "read-pkg": {"version": "3.0.0", "dev": true, "requires": {"load-json-file": "^4.0.0", "normalize-package-data": "^2.3.2", "path-type": "^3.0.0"}, "dependencies": {"hosted-git-info": {"version": "2.8.9", "dev": true}, "normalize-package-data": {"version": "2.5.0", "dev": true, "requires": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "path-type": {"version": "3.0.0", "dev": true, "requires": {"pify": "^3.0.0"}}, "pify": {"version": "3.0.0", "dev": true}, "semver": {"version": "5.7.2", "dev": true}}}, "read-pkg-up": {"version": "3.0.0", "dev": true, "requires": {"find-up": "^2.0.0", "read-pkg": "^3.0.0"}, "dependencies": {"find-up": {"version": "2.1.0", "dev": true, "requires": {"locate-path": "^2.0.0"}}, "locate-path": {"version": "2.0.0", "dev": true, "requires": {"p-locate": "^2.0.0", "path-exists": "^3.0.0"}}, "p-limit": {"version": "1.3.0", "dev": true, "requires": {"p-try": "^1.0.0"}}, "p-locate": {"version": "2.0.0", "dev": true, "requires": {"p-limit": "^1.1.0"}}, "p-try": {"version": "1.0.0", "dev": true}, "path-exists": {"version": "3.0.0", "dev": true}}}, "readable-stream": {"version": "3.6.2", "dev": true, "requires": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}}, "rechoir": {"version": "0.6.2", "dev": true, "requires": {"resolve": "^1.1.6"}}, "redent": {"version": "3.0.0", "dev": true, "requires": {"indent-string": "^4.0.0", "strip-indent": "^3.0.0"}}, "regenerator-runtime": {"version": "0.13.11"}, "regexp.prototype.flags": {"version": "1.4.3", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.3", "functions-have-names": "^1.2.2"}}, "registry-auth-token": {"version": "5.0.2", "dev": true, "requires": {"@pnpm/npm-conf": "^2.1.0"}}, "registry-url": {"version": "6.0.1", "dev": true, "requires": {"rc": "1.2.8"}}, "release-it": {"version": "16.1.3", "dev": true, "requires": {"@iarna/toml": "2.2.5", "@octokit/rest": "19.0.13", "async-retry": "1.3.3", "chalk": "5.3.0", "cosmiconfig": "8.2.0", "execa": "7.1.1", "git-url-parse": "13.1.0", "globby": "13.2.2", "got": "13.0.0", "inquirer": "9.2.8", "is-ci": "3.0.1", "issue-parser": "6.0.0", "lodash": "4.17.21", "mime-types": "2.1.35", "new-github-release-url": "2.0.0", "node-fetch": "3.3.1", "open": "9.1.0", "ora": "6.3.1", "os-name": "5.1.0", "promise.allsettled": "1.0.6", "proxy-agent": "6.3.0", "semver": "7.5.4", "shelljs": "0.8.5", "update-notifier": "6.0.2", "url-join": "5.0.0", "wildcard-match": "5.1.2", "yargs-parser": "21.1.1"}, "dependencies": {"chalk": {"version": "5.3.0", "dev": true}, "execa": {"version": "7.1.1", "dev": true, "requires": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.1", "human-signals": "^4.3.0", "is-stream": "^3.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^5.1.0", "onetime": "^6.0.0", "signal-exit": "^3.0.7", "strip-final-newline": "^3.0.0"}}, "get-stream": {"version": "6.0.1", "dev": true}, "globby": {"version": "13.2.2", "dev": true, "requires": {"dir-glob": "^3.0.1", "fast-glob": "^3.3.0", "ignore": "^5.2.4", "merge2": "^1.4.1", "slash": "^4.0.0"}}, "got": {"version": "13.0.0", "dev": true, "requires": {"@sindresorhus/is": "^5.2.0", "@szmarczak/http-timer": "^5.0.1", "cacheable-lookup": "^7.0.0", "cacheable-request": "^10.2.8", "decompress-response": "^6.0.0", "form-data-encoder": "^2.1.2", "get-stream": "^6.0.1", "http2-wrapper": "^2.1.10", "lowercase-keys": "^3.0.0", "p-cancelable": "^3.0.0", "responselike": "^3.0.0"}}, "human-signals": {"version": "4.3.1", "dev": true}, "is-stream": {"version": "3.0.0", "dev": true}, "lru-cache": {"version": "6.0.0", "dev": true, "requires": {"yallist": "^4.0.0"}}, "mimic-fn": {"version": "4.0.0", "dev": true}, "npm-run-path": {"version": "5.1.0", "dev": true, "requires": {"path-key": "^4.0.0"}}, "onetime": {"version": "6.0.0", "dev": true, "requires": {"mimic-fn": "^4.0.0"}}, "path-key": {"version": "4.0.0", "dev": true}, "semver": {"version": "7.5.4", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "slash": {"version": "4.0.0", "dev": true}, "strip-final-newline": {"version": "3.0.0", "dev": true}, "yallist": {"version": "4.0.0", "dev": true}}}, "require-directory": {"version": "2.1.1", "dev": true}, "requires-port": {"version": "1.0.0", "dev": true}, "resolve": {"version": "1.22.2", "dev": true, "requires": {"is-core-module": "^2.11.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}}, "resolve-alpn": {"version": "1.2.1", "dev": true}, "resolve-from": {"version": "4.0.0", "dev": true}, "resolve-protobuf-schema": {"version": "2.1.0", "peer": true, "requires": {"protocol-buffers-schema": "^3.3.1"}}, "responselike": {"version": "3.0.0", "dev": true, "requires": {"lowercase-keys": "^3.0.0"}}, "restore-cursor": {"version": "3.1.0", "dev": true, "requires": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}}, "retry": {"version": "0.13.1", "dev": true}, "reusify": {"version": "1.0.4", "dev": true}, "rgbcolor": {"version": "1.0.1", "optional": true}, "rimraf": {"version": "3.0.2", "dev": true, "requires": {"glob": "^7.1.3"}}, "rollup": {"version": "3.28.0", "resolved": "https://registry.npmjs.org/rollup/-/rollup-3.28.0.tgz", "integrity": "sha512-d7zhvo1OUY2SXSM6pfNjgD5+d0Nz87CUp4mt8l/GgVP3oBsPwzNvSzyu1me6BSG9JIgWNTVcafIXBIyM8yQ3yw==", "dev": true, "requires": {"fsevents": "~2.3.2"}}, "rrweb-cssom": {"version": "0.6.0", "dev": true}, "run-applescript": {"version": "5.0.0", "dev": true, "requires": {"execa": "^5.0.0"}, "dependencies": {"execa": {"version": "5.1.1", "dev": true, "requires": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}}, "get-stream": {"version": "6.0.1", "dev": true}, "human-signals": {"version": "2.1.0", "dev": true}}}, "run-async": {"version": "3.0.0", "dev": true}, "run-parallel": {"version": "1.2.0", "dev": true, "requires": {"queue-microtask": "^1.2.2"}}, "rw": {"version": "1.3.3", "peer": true}, "rxjs": {"version": "7.8.1", "dev": true, "requires": {"tslib": "^2.1.0"}, "dependencies": {"tslib": {"version": "2.6.1", "dev": true}}}, "safe-buffer": {"version": "5.2.1", "dev": true}, "safe-regex-test": {"version": "1.0.0", "dev": true, "requires": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.3", "is-regex": "^1.1.4"}}, "safer-buffer": {"version": "2.1.2", "dev": true}, "saxes": {"version": "6.0.0", "dev": true, "requires": {"xmlchars": "^2.2.0"}}, "search-insights": {"version": "2.7.0", "resolved": "https://registry.npmjs.org/search-insights/-/search-insights-2.7.0.tgz", "integrity": "sha512-GLbVaGgzYEKMvuJbHRhLi1qoBFnjXZGZ6l4LxOYPCp4lI2jDRB3jPU9/XNhMwv6kvnA9slTreq6pvK+b3o3aqg==", "dev": true, "peer": true}, "semver": {"version": "6.3.1", "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "dev": true}, "semver-diff": {"version": "4.0.0", "dev": true, "requires": {"semver": "^7.3.5"}, "dependencies": {"lru-cache": {"version": "6.0.0", "dev": true, "requires": {"yallist": "^4.0.0"}}, "semver": {"version": "7.4.0", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "yallist": {"version": "4.0.0", "dev": true}}}, "shebang-command": {"version": "2.0.0", "dev": true, "requires": {"shebang-regex": "^3.0.0"}}, "shebang-regex": {"version": "3.0.0", "dev": true}, "shelljs": {"version": "0.8.5", "dev": true, "requires": {"glob": "^7.0.0", "interpret": "^1.0.0", "rechoir": "^0.6.2"}}, "shiki": {"version": "0.14.3", "dev": true, "requires": {"ansi-sequence-parser": "^1.1.0", "jsonc-parser": "^3.2.0", "vscode-oniguruma": "^1.7.0", "vscode-textmate": "^8.0.0"}}, "side-channel": {"version": "1.0.4", "dev": true, "requires": {"call-bind": "^1.0.0", "get-intrinsic": "^1.0.2", "object-inspect": "^1.9.0"}}, "siginfo": {"version": "2.0.0", "dev": true}, "signal-exit": {"version": "3.0.7", "dev": true}, "slash": {"version": "3.0.0", "dev": true}, "smart-buffer": {"version": "4.2.0", "dev": true}, "socks": {"version": "2.7.1", "dev": true, "requires": {"ip": "^2.0.0", "smart-buffer": "^4.2.0"}, "dependencies": {"ip": {"version": "2.0.0", "dev": true}}}, "socks-proxy-agent": {"version": "8.0.1", "dev": true, "requires": {"agent-base": "^7.0.1", "debug": "^4.3.4", "socks": "^2.7.1"}, "dependencies": {"agent-base": {"version": "7.1.0", "dev": true, "requires": {"debug": "^4.3.4"}}}}, "sort-asc": {"version": "0.1.0", "peer": true}, "sort-desc": {"version": "0.1.1", "peer": true}, "sort-object": {"version": "0.3.2", "peer": true, "requires": {"sort-asc": "^0.1.0", "sort-desc": "^0.1.1"}}, "source-map": {"version": "0.6.1", "dev": true}, "source-map-js": {"version": "1.0.2", "dev": true}, "spdx-correct": {"version": "3.2.0", "dev": true, "requires": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "spdx-exceptions": {"version": "2.3.0", "dev": true}, "spdx-expression-parse": {"version": "3.0.1", "dev": true, "requires": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "spdx-license-ids": {"version": "3.0.13", "dev": true}, "split": {"version": "1.0.1", "dev": true, "requires": {"through": "2"}}, "split2": {"version": "3.2.2", "dev": true, "requires": {"readable-stream": "^3.0.0"}}, "sprintf-js": {"version": "1.0.3", "dev": true}, "stackback": {"version": "0.0.2", "dev": true}, "stackblur-canvas": {"version": "2.5.0", "optional": true}, "std-env": {"version": "3.3.3", "dev": true}, "stdin-discarder": {"version": "0.1.0", "dev": true, "requires": {"bl": "^5.0.0"}}, "stop-iteration-iterator": {"version": "1.0.0", "dev": true, "requires": {"internal-slot": "^1.0.4"}}, "string_decoder": {"version": "1.3.0", "dev": true, "requires": {"safe-buffer": "~5.2.0"}}, "string-argv": {"version": "0.3.2", "dev": true}, "string-width": {"version": "5.1.2", "dev": true, "requires": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "dependencies": {"ansi-regex": {"version": "6.0.1", "dev": true}, "strip-ansi": {"version": "7.0.1", "dev": true, "requires": {"ansi-regex": "^6.0.1"}}}}, "string.prototype.trim": {"version": "1.2.7", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}}, "string.prototype.trimend": {"version": "1.0.6", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}}, "string.prototype.trimstart": {"version": "1.0.6", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}}, "strip-ansi": {"version": "6.0.1", "dev": true, "requires": {"ansi-regex": "^5.0.1"}}, "strip-bom": {"version": "3.0.0", "dev": true}, "strip-final-newline": {"version": "2.0.0", "dev": true}, "strip-indent": {"version": "3.0.0", "dev": true, "requires": {"min-indent": "^1.0.0"}}, "strip-json-comments": {"version": "3.1.1", "dev": true}, "strip-literal": {"version": "1.0.1", "dev": true, "requires": {"acorn": "^8.8.2"}}, "supports-color": {"version": "5.5.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz", "integrity": "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==", "dev": true, "requires": {"has-flag": "^3.0.0"}}, "supports-preserve-symlinks-flag": {"version": "1.0.0", "dev": true}, "svg-pathdata": {"version": "6.0.3", "optional": true}, "symbol-tree": {"version": "3.2.4", "dev": true}, "tabbable": {"version": "6.2.0", "resolved": "https://registry.npmjs.org/tabbable/-/tabbable-6.2.0.tgz", "integrity": "sha512-Cat63mxsVJlzYvN51JmVXIgNoUokrIaT2zLclCXjRd8boZ0004U4KCs/sToJ75C6sdlByWxpYnb5Boif1VSFew==", "dev": true}, "test-exclude": {"version": "6.0.0", "dev": true, "requires": {"@istanbuljs/schema": "^0.1.2", "glob": "^7.1.4", "minimatch": "^3.0.4"}}, "text-extensions": {"version": "1.9.0", "dev": true}, "text-segmentation": {"version": "1.0.3", "optional": true, "requires": {"utrie": "^1.0.2"}}, "text-table": {"version": "0.2.0", "dev": true}, "through": {"version": "2.3.8", "dev": true}, "through2": {"version": "2.0.5", "dev": true, "requires": {"readable-stream": "~2.3.6", "xtend": "~4.0.1"}, "dependencies": {"isarray": {"version": "1.0.0", "dev": true}, "readable-stream": {"version": "2.3.8", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "safe-buffer": {"version": "5.1.2", "dev": true}, "string_decoder": {"version": "1.1.1", "dev": true, "requires": {"safe-buffer": "~5.1.0"}}}}, "tiny-emitter": {"version": "2.1.0", "peer": true}, "tinybench": {"version": "2.5.0", "dev": true}, "tinypool": {"version": "0.6.0", "dev": true}, "tinyspy": {"version": "2.1.1", "dev": true}, "titleize": {"version": "3.0.0", "dev": true}, "tmp": {"version": "0.0.33", "dev": true, "requires": {"os-tmpdir": "~1.0.2"}}, "to-fast-properties": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-2.0.0.tgz", "integrity": "sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==", "dev": true, "peer": true}, "to-regex-range": {"version": "5.0.1", "dev": true, "requires": {"is-number": "^7.0.0"}}, "tough-cookie": {"version": "4.1.2", "dev": true, "requires": {"psl": "^1.1.33", "punycode": "^2.1.1", "universalify": "^0.2.0", "url-parse": "^1.5.3"}, "dependencies": {"universalify": {"version": "0.2.0", "dev": true}}}, "tr46": {"version": "0.0.3", "dev": true}, "trim-newlines": {"version": "3.0.1", "dev": true}, "tslib": {"version": "1.14.1", "dev": true}, "tsutils": {"version": "3.21.0", "dev": true, "requires": {"tslib": "^1.8.1"}}, "type-check": {"version": "0.4.0", "dev": true, "requires": {"prelude-ls": "^1.2.1"}}, "type-detect": {"version": "4.0.8", "dev": true}, "type-fest": {"version": "0.20.2", "dev": true}, "typed-array-length": {"version": "1.0.4", "dev": true, "requires": {"call-bind": "^1.0.2", "for-each": "^0.3.3", "is-typed-array": "^1.1.9"}}, "typedarray": {"version": "0.0.6", "dev": true}, "typedarray-to-buffer": {"version": "3.1.5", "dev": true, "requires": {"is-typedarray": "^1.0.0"}}, "typescript": {"version": "5.1.6", "dev": true}, "ufo": {"version": "1.1.2", "dev": true}, "uglify-js": {"version": "3.17.4", "dev": true, "optional": true}, "unbox-primitive": {"version": "1.0.2", "dev": true, "requires": {"call-bind": "^1.0.2", "has-bigints": "^1.0.2", "has-symbols": "^1.0.3", "which-boxed-primitive": "^1.0.2"}}, "unique-string": {"version": "3.0.0", "dev": true, "requires": {"crypto-random-string": "^4.0.0"}}, "universal-user-agent": {"version": "6.0.0", "dev": true}, "universalify": {"version": "0.1.2", "dev": true}, "untildify": {"version": "4.0.0", "dev": true}, "update-browserslist-db": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.11.tgz", "integrity": "sha512-dCwEFf0/oT85M1fHBg4F0jtLwJrutGoHSQXCh7u4o2t1drG+c0a9Flnqww6XUKSfQMPpJBRjU8d4RXB09qtvaA==", "dev": true, "peer": true, "requires": {"escalade": "^3.1.1", "picocolors": "^1.0.0"}}, "update-notifier": {"version": "6.0.2", "dev": true, "requires": {"boxen": "^7.0.0", "chalk": "^5.0.1", "configstore": "^6.0.0", "has-yarn": "^3.0.0", "import-lazy": "^4.0.0", "is-ci": "^3.0.1", "is-installed-globally": "^0.4.0", "is-npm": "^6.0.0", "is-yarn-global": "^0.4.0", "latest-version": "^7.0.0", "pupa": "^3.1.0", "semver": "^7.3.7", "semver-diff": "^4.0.0", "xdg-basedir": "^5.1.0"}, "dependencies": {"chalk": {"version": "5.2.0", "dev": true}, "lru-cache": {"version": "6.0.0", "dev": true, "requires": {"yallist": "^4.0.0"}}, "semver": {"version": "7.4.0", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "yallist": {"version": "4.0.0", "dev": true}}}, "uri-js": {"version": "4.4.1", "dev": true, "requires": {"punycode": "^2.1.0"}}, "url-join": {"version": "5.0.0", "dev": true}, "url-parse": {"version": "1.5.10", "dev": true, "requires": {"querystringify": "^2.1.1", "requires-port": "^1.0.0"}}, "util-deprecate": {"version": "1.0.2", "dev": true}, "utrie": {"version": "1.0.2", "optional": true, "requires": {"base64-arraybuffer": "^1.0.2"}}, "v8-to-istanbul": {"version": "9.1.0", "dev": true, "requires": {"@jridgewell/trace-mapping": "^0.3.12", "@types/istanbul-lib-coverage": "^2.0.1", "convert-source-map": "^1.6.0"}}, "validate-npm-package-license": {"version": "3.0.4", "dev": true, "requires": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "validator": {"version": "13.9.0", "dev": true}, "vite": {"version": "4.4.9", "resolved": "https://registry.npmjs.org/vite/-/vite-4.4.9.tgz", "integrity": "sha512-2mbUn2LlUmNASWwSCNSJ/EG2HuSRTnVNaydp6vMCm5VIqJsjMfbIWtbH2kDuwUVW5mMUKKZvGPX/rqeqVvv1XA==", "dev": true, "requires": {"esbuild": "^0.18.10", "fsevents": "~2.3.2", "postcss": "^8.4.27", "rollup": "^3.27.1"}}, "vite-node": {"version": "0.33.0", "dev": true, "requires": {"cac": "^6.7.14", "debug": "^4.3.4", "mlly": "^1.4.0", "pathe": "^1.1.1", "picocolors": "^1.0.0", "vite": "^3.0.0 || ^4.0.0"}}, "vite-plugin-dts": {"version": "3.3.1", "dev": true, "requires": {"@microsoft/api-extractor": "^7.36.0", "@rollup/pluginutils": "^5.0.2", "@vue/language-core": "^1.8.1", "debug": "^4.3.4", "kolorist": "^1.8.0", "vue-tsc": "^1.8.1"}}, "vitepress": {"version": "1.0.0-rc.4", "resolved": "https://registry.npmjs.org/vitepress/-/vitepress-1.0.0-rc.4.tgz", "integrity": "sha512-JCQ89Bm6ECUTnyzyas3JENo00UDJeK8q1SUQyJYou+4Yz5BKEc/F3O21cu++DnUT2zXc0kvQ2Aj4BZCc/nioXQ==", "dev": true, "requires": {"@docsearch/css": "^3.5.1", "@docsearch/js": "^3.5.1", "@vitejs/plugin-vue": "^4.2.3", "@vue/devtools-api": "^6.5.0", "@vueuse/core": "^10.3.0", "@vueuse/integrations": "^10.3.0", "body-scroll-lock": "4.0.0-beta.0", "focus-trap": "^7.5.2", "mark.js": "8.11.1", "minisearch": "^6.1.0", "shiki": "^0.14.3", "vite": "^4.4.9", "vue": "^3.3.4"}}, "vitest": {"version": "0.33.0", "dev": true, "requires": {"@types/chai": "^4.3.5", "@types/chai-subset": "^1.3.3", "@types/node": "*", "@vitest/expect": "0.33.0", "@vitest/runner": "0.33.0", "@vitest/snapshot": "0.33.0", "@vitest/spy": "0.33.0", "@vitest/utils": "0.33.0", "acorn": "^8.9.0", "acorn-walk": "^8.2.0", "cac": "^6.7.14", "chai": "^4.3.7", "debug": "^4.3.4", "local-pkg": "^0.4.3", "magic-string": "^0.30.1", "pathe": "^1.1.1", "picocolors": "^1.0.0", "std-env": "^3.3.3", "strip-literal": "^1.0.1", "tinybench": "^2.5.0", "tinypool": "^0.6.0", "vite": "^3.0.0 || ^4.0.0", "vite-node": "0.33.0", "why-is-node-running": "^2.2.2"}}, "vscode-oniguruma": {"version": "1.7.0", "dev": true}, "vscode-textmate": {"version": "8.0.0", "dev": true}, "vue": {"version": "3.3.4", "dev": true, "requires": {"@vue/compiler-dom": "3.3.4", "@vue/compiler-sfc": "3.3.4", "@vue/runtime-dom": "3.3.4", "@vue/server-renderer": "3.3.4", "@vue/shared": "3.3.4"}}, "vue-component-type-helpers": {"version": "1.8.4", "dev": true}, "vue-eslint-parser": {"version": "9.3.1", "dev": true, "requires": {"debug": "^4.3.4", "eslint-scope": "^7.1.1", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.6"}, "dependencies": {"eslint-scope": {"version": "7.2.0", "dev": true, "requires": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}}, "eslint-visitor-keys": {"version": "3.4.0", "dev": true}, "estraverse": {"version": "5.3.0", "dev": true}, "lru-cache": {"version": "6.0.0", "dev": true, "requires": {"yallist": "^4.0.0"}}, "semver": {"version": "7.4.0", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "yallist": {"version": "4.0.0", "dev": true}}}, "vue-template-compiler": {"version": "2.7.14", "dev": true, "requires": {"de-indent": "^1.0.2", "he": "^1.2.0"}}, "vue-tsc": {"version": "1.8.5", "dev": true, "requires": {"@vue/language-core": "1.8.5", "@vue/typescript": "1.8.5", "semver": "^7.3.8"}, "dependencies": {"lru-cache": {"version": "6.0.0", "dev": true, "requires": {"yallist": "^4.0.0"}}, "semver": {"version": "7.5.4", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "yallist": {"version": "4.0.0", "dev": true}}}, "w3c-xmlserializer": {"version": "4.0.0", "dev": true, "requires": {"xml-name-validator": "^4.0.0"}}, "wcwidth": {"version": "1.0.1", "dev": true, "requires": {"defaults": "^1.0.3"}}, "web-streams-polyfill": {"version": "3.2.1", "dev": true}, "web-worker": {"version": "1.2.0", "peer": true}, "webidl-conversions": {"version": "3.0.1", "dev": true}, "whatwg-encoding": {"version": "2.0.0", "dev": true, "requires": {"iconv-lite": "0.6.3"}, "dependencies": {"iconv-lite": {"version": "0.6.3", "dev": true, "requires": {"safer-buffer": ">= 2.1.2 < 3.0.0"}}}}, "whatwg-mimetype": {"version": "3.0.0", "dev": true}, "whatwg-url": {"version": "5.0.0", "dev": true, "requires": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "which": {"version": "2.0.2", "dev": true, "requires": {"isexe": "^2.0.0"}}, "which-boxed-primitive": {"version": "1.0.2", "dev": true, "requires": {"is-bigint": "^1.0.1", "is-boolean-object": "^1.1.0", "is-number-object": "^1.0.4", "is-string": "^1.0.5", "is-symbol": "^1.0.3"}}, "which-typed-array": {"version": "1.1.9", "dev": true, "requires": {"available-typed-arrays": "^1.0.5", "call-bind": "^1.0.2", "for-each": "^0.3.3", "gopd": "^1.0.1", "has-tostringtag": "^1.0.0", "is-typed-array": "^1.1.10"}}, "why-is-node-running": {"version": "2.2.2", "dev": true, "requires": {"siginfo": "^2.0.0", "stackback": "0.0.2"}}, "widest-line": {"version": "4.0.1", "dev": true, "requires": {"string-width": "^5.0.1"}}, "wildcard-match": {"version": "5.1.2", "dev": true}, "windows-release": {"version": "5.1.0", "dev": true, "requires": {"execa": "^5.1.1"}, "dependencies": {"execa": {"version": "5.1.1", "dev": true, "requires": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}}, "get-stream": {"version": "6.0.1", "dev": true}, "human-signals": {"version": "2.1.0", "dev": true}}}, "wkt-parser": {"version": "1.3.2"}, "wordwrap": {"version": "1.0.0", "dev": true}, "wrap-ansi": {"version": "8.1.0", "dev": true, "requires": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "dependencies": {"ansi-regex": {"version": "6.0.1", "dev": true}, "ansi-styles": {"version": "6.2.1", "dev": true}, "strip-ansi": {"version": "7.0.1", "dev": true, "requires": {"ansi-regex": "^6.0.1"}}}}, "wrappy": {"version": "1.0.2", "dev": true}, "write-file-atomic": {"version": "3.0.3", "dev": true, "requires": {"imurmurhash": "^0.1.4", "is-typedarray": "^1.0.0", "signal-exit": "^3.0.2", "typedarray-to-buffer": "^3.1.5"}}, "ws": {"version": "8.13.0", "dev": true, "requires": {}}, "xdg-basedir": {"version": "5.1.0", "dev": true}, "xml-name-validator": {"version": "4.0.0", "dev": true}, "xml-utils": {"version": "1.7.0", "peer": true}, "xmlchars": {"version": "2.2.0", "dev": true}, "xtend": {"version": "4.0.2", "dev": true}, "y18n": {"version": "5.0.8", "dev": true}, "yallist": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz", "integrity": "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==", "dev": true, "peer": true}, "yargs": {"version": "16.2.0", "dev": true, "requires": {"cliui": "^7.0.2", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.0", "y18n": "^5.0.5", "yargs-parser": "^20.2.2"}, "dependencies": {"emoji-regex": {"version": "8.0.0", "dev": true}, "string-width": {"version": "4.2.3", "dev": true, "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "yargs-parser": {"version": "20.2.9", "dev": true}}}, "yargs-parser": {"version": "21.1.1", "dev": true}, "yocto-queue": {"version": "0.1.0", "dev": true}, "z-schema": {"version": "5.0.5", "dev": true, "requires": {"commander": "^9.4.1", "lodash.get": "^4.4.2", "lodash.isequal": "^4.5.0", "validator": "^13.7.0"}, "dependencies": {"commander": {"version": "9.5.0", "dev": true, "optional": true}}}}}
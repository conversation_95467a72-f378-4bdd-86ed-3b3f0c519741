/*
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 09:40:10
 */
import type { App } from "vue";
import BcAnimationDrop from "./BcAnimationDrop.vue";
import BcAnimationFade from "./BcAnimationFade.vue";
import BcAnimationFeature from "./BcAnimationFeature.vue";
import BcAnimationPath from "./BcAnimationPath.vue";
import BcAnimationShake from "./BcAnimationShake.vue";
import BcAnimationSlide from "./BcAnimationSlide.vue";
import BcAnimationTeleport from "./BcAnimationTeleport.vue";
import BcAnimationZoom from "./BcAnimationZoom.vue";

let installed = false;

function install(app: App) {
  if (installed) return;
  installed = true;

  app.component("bc-animation-drop", BcAnimationDrop);
  app.component("bc-animation-fade", BcAnimationFade);
  app.component("bc-animation-feature", BcAnimationFeature);
  app.component("bc-animation-path", BcAnimationPath);
  app.component("bc-animation-shake", BcAnimationShake);
  app.component("bc-animation-slide", BcAnimationSlide);
  app.component("bc-animation-teleport", BcAnimationTeleport);
  app.component("bc-animation-zoom", BcAnimationZoom);
}

export default install;

export {
  install,
  BcAnimationDrop,
  BcAnimationFade,
  BcAnimationFeature,
  BcAnimationPath,
  BcAnimationShake,
  BcAnimationSlide,
  BcAnimationTeleport,
  BcAnimationZoom,
};

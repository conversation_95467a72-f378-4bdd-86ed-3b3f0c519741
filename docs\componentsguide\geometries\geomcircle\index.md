<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 11:45:37
-->

# bc-geom-circle

`bc-geom-circle` 可以在 `bc-feature` 内部使用来绘制单个圆。

<script lang="ts" setup>
import CircleDemo from "@demos/CircleDemo.vue"
</script>

<ClientOnly>
<CircleDemo />
</ClientOnly>

## 用法

::: code-group

<<< ../../../../src/demos/CircleDemo.vue

:::

## 属性

### center

- **类型**: `number[]`
  该圆的中心坐标（以地图投影为单位）。

### radius

- **类型**: `Number`
  该圆的半径（以地图投影为单位）。

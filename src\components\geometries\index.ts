/*
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 09:46:55
 */
import type { App } from "vue";
import BcGeomCircle from "./BcGeomCircle.vue";
import BcGeomLineString from "./BcGeomLineString.vue";
import BcGeomMultiLineString from "./BcGeomMultiLineString.vue";
import BcGeomMultiPoint from "./BcGeomMultiPoint.vue";
import BcGeomMultiPolygon from "./BcGeomMultiPolygon.vue";
import BcGeomPoint from "./BcGeomPoint.vue";
import BcGeomPolygon from "./BcGeomPolygon.vue";

let installed = false;

function install(app: App) {
  if (installed) return;
  installed = true;

  app.component("bc-geom-circle", BcGeomCircle);
  app.component("bc-geom-line-string", BcGeomLineString);
  app.component("bc-geom-multi-line-string", BcGeomMultiLineString);
  app.component("bc-geom-multi-point", BcGeomMultiPoint);
  app.component("bc-geom-multi-polygon", BcGeomMultiPolygon);
  app.component("bc-geom-point", BcGeomPoint);
  app.component("bc-geom-polygon", BcGeomPolygon);
}

export default install;

export {
  install,
  BcGeomCircle,
  BcGeomLineString,
  BcGeomMultiLineString,
  BcGeomMultiPoint,
  BcGeomMultiPolygon,
  BcGeomPoint,
  BcGeomPolygon,
};

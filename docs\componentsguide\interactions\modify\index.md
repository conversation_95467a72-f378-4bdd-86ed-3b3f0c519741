# bc-interaction-modify

> 用于修改特征几何形状的交互。

<script lang="ts" setup>
import SnapModifyDemo from "@demos/SnapModifyDemo.vue"
</script>

<ClientOnly>
<SnapModifyDemo/>
</ClientOnly>

## 用法

::: code-group

<<< ../../../../src/demos/SnapModifyDemo.vue

:::

## 属性

### condition

- **类型**: `Function`

### deleteCondition

- **类型**: `Function`

### insertVertexCondition

- **类型**: `Function`

### pixelTolerance

- **类型**: `Number`
- **默认值**: `10`

### wrapX

- **类型**: `Boolean`
- **默认值**: `false`

### hitDetection

- **类型**: `Boolean`

  ### features

- **类型**: `[Collection,Object]`

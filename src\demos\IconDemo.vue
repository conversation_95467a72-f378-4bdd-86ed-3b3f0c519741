<template>
  <bc-map
    ref="map"
    :loadTilesWhileAnimating="true"
    :loadTilesWhileInteracting="true"
    style="height: 500px"
  >
    <bc-view
      ref="view"
      :center="center"
      :rotation="rotation"
      :zoom="zoom"
      :projection="projection"
    />

    <bc-tile-layer>
      <bc-source-osm />
    </bc-tile-layer>

    <bc-vector-layer>
      <bc-source-vector
        url="https://raw.githubusercontent.com/alpers/Turkey-Maps-GeoJSON/master/tr-cities-airports.json"
        :format="geoJson"
        :projection="projection"
      >
      </bc-source-vector>
      <bc-style>
        <bc-style-stroke color="red" :width="2"></bc-style-stroke>
        <bc-style-fill color="rgba(255,255,255,0.1)"></bc-style-fill>
        <bc-style-icon :src="markerIcon" :scale="0.1"></bc-style-icon>
      </bc-style>
    </bc-vector-layer>
  </bc-map>
</template>

<script lang="ts" setup>
import { ref, inject } from "vue";

import markerIcon from "@/assets/marker.png";

const center = ref([34, 39.13]);
const projection = ref("EPSG:4326");
const zoom = ref(6.8);
const rotation = ref(0);
const format = inject("ol-format");
const geoJson = new format.GeoJSON();
</script>

<template>
  <bc-map style="height: 400px">
    <bc-view ref="view" :center="center" :zoom="zoom" />
    <bc-layer-group :opacity="0.4">
      <bc-tile-layer>
        <bc-source-osm />
      </bc-tile-layer>
      <bc-tile-layer>
        <bc-source-tile-json :url="url" crossOrigin="anonymous" />
      </bc-tile-layer>
    </bc-layer-group>
  </bc-map>
</template>

<script lang="ts" setup>
import { ref } from "vue";

const url = "https://a.tile.openstreetmap.org/4/6/6.png";
const center = ref([37.4057, 8.81566]);
const zoom = ref(4);
</script>

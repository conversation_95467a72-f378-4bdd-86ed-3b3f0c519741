<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 15:10:57
-->

# bc-tile-layer

`bc-tile-layer` 可以渲染来自源的图像，这些源在按缩放级别组织的网格中提供预渲染的平铺图像。它应该与平铺源组件一起使用，例如 `bc-source-xyz`、`bc-source-wmts`、`bc-source-osm`、`bc-source-bingmaps`

<script lang="ts" setup>
import TileLayerDemo from "@demos/TileLayerDemo.vue"
</script>
<ClientOnly>
<TileLayerDemo />
</ClientOnly>

## 用法

下面的示例展示了如何将 `bc-layer-tile` 组件与 `bc-source-wmts` 和 `bc-source-osm` 一起使用。

::: code-group

<<< ../../../../src/demos/TileLayerDemo.vue

:::

## 属性

### className

- **类型**: `string`
- **默认值**: `bc-layer`

要设置为图层元素的 CSS 类名称。

### opacity

- **类型**: `number `
- **默认值**: `1`

不透明度 (0, 1)。

### visible

- **类型**: `boolean`
- **默认值**: `true`

能见度。

### extent

- **类型**: `Array`

图层渲染的边界范围。该图层不会在此范围之外进行渲染。

### zIndex

- **类型**: `number`

图层渲染的`z-index`。在渲染时，图层将首先按 Z 索引排序，然后按位置排序。

### minResolution

- **类型**: `number`

该图层可见的最小分辨率（含）。

### maxResolution

- **类型**: `number`

最大分辨率（独占），低于该分辨率该图层将可见。

### minZoom

- **类型**: `number`

最小视图缩放级别（不包括），高于该级别该图层将可见。

### maxZoom

- **类型**: `number`

该图层可见的最大视图缩放级别（含）。

### preload

- **类型**: `number`
- **默认值**: `0`
  将加载达到预加载级别的低分辨率图块。

<template>
  <bc-map
    :loadTilesWhileAnimating="true"
    :loadTilesWhileInteracting="true"
    style="height: 400px"
    ref="map"
  >
    <bc-view
      ref="view"
      :center="center"
      :rotation="rotation"
      :zoom="zoom"
      :projection="projection"
    />

    <bc-tile-layer>
      <bc-source-osm />
    </bc-tile-layer>

    <bc-geolocation :projection="projection" @change:position="geoLocChange">
      <template>
        <bc-vector-layer :zIndex="2">
          <bc-source-vector>
            <bc-feature ref="positionFeature">
              <bc-geom-point :coordinates="position"></bc-geom-point>
              <bc-style>
                <bc-style-icon :src="hereIcon" :scale="0.1"></bc-style-icon>
              </bc-style>
            </bc-feature>
          </bc-source-vector>
        </bc-vector-layer>
      </template>
    </bc-geolocation>
  </bc-map>
</template>

<script setup lang="ts">
import hereIcon from "@/assets/here.png";
import { ref } from "vue";
import type { View } from "ol";
import type { ObjectEvent } from "ol/Object";

const center = ref([40, 40]);
const projection = ref("EPSG:4326");
const zoom = ref(12);
const rotation = ref(0);
const view = ref<View>();
const map = ref(null);
const position = ref([]);

const geoLocChange = (event: ObjectEvent) => {
  console.log("AAAAA", event);
  position.value = event.target.getPosition();
  view.value?.setCenter(event.target?.getPosition());
};
</script>

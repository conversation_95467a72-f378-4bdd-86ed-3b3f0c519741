<template>
  <input type="checkbox" id="checkbox" v-model="drawEnable" />
  <label for="checkbox">Draw Enable</label>

  <select id="type" v-model="drawType">
    <option value="Point">Point</option>
    <option value="LineString">LineString</option>
    <option value="Polygon">Polygon</option>
    <option value="Circle">Circle</option>
  </select>

  <bc-map
    :loadTilesWhileAnimating="true"
    :loadTilesWhileInteracting="true"
    style="height: 400px"
  >
    <bc-view
      ref="view"
      :center="center"
      :rotation="rotation"
      :zoom="zoom"
      :projection="projection"
    />

    <bc-tile-layer>
      <bc-source-osm />
    </bc-tile-layer>

    <bc-vector-layer>
      <bc-source-vector :projection="projection">
        <bc-interaction-draw
          v-if="drawEnable"
          :type="drawType"
          @drawend="drawend"
          @drawstart="drawstart"
        >
          <bc-style>
            <bc-style-stroke color="blue" :width="2"></bc-style-stroke>
            <bc-style-fill color="rgba(255, 255, 0, 0.4)"></bc-style-fill>
          </bc-style>
        </bc-interaction-draw>
      </bc-source-vector>

      <bc-style>
        <bc-style-stroke color="red" :width="2"></bc-style-stroke>
        <bc-style-fill color="rgba(255,255,255,0.1)"></bc-style-fill>
        <bc-style-circle :radius="7">
          <bc-style-fill color="red"></bc-style-fill>
        </bc-style-circle>
      </bc-style>
    </bc-vector-layer>
  </bc-map>
</template>

<script lang="ts" setup>
import { ref } from "vue";

const center = ref([40, 40]);
const projection = ref("EPSG:4326");
const zoom = ref(8);
const rotation = ref(0);

const drawEnable = ref(true);
const drawType = ref("Polygon");

const drawstart = (event) => {
  console.log(event);
};

const drawend = (event) => {
  console.log(event);
};
</script>

# bc-toggle-control

> 简单的切换控件 该控件可以通过交互来创建以控制其激活。

## Demo

请参阅[所有地图控件的演示页面](../index.md)

## 属性

### className

class of the control

- **类型**: `String`

### title

title of the control

- **类型**: `String`

### html

html to insert in the control

- **类型**: `String`

### interaction

`ol.interaction` associated with the control

- **类型**: `Object`

### active

the control is created active

- **类型**: `Boolean`
- **默认值**: `false`

### disable

the control is created disabled

- **类型**: `Boolean`
- **默认值**: `false`

### bar

a subbar (`ol.control.Bar`) associated with the control (drawn when active if control is nested in a ol.control.Bar)

- **类型**: `Object`

### autoActive

the control will activate when shown in an `ol.control.Bar`

- **类型**: `Boolean`
- **默认值**: `false`

### onToggle

callback when control is clicked (or use change:active event)

- **类型**: `Function`

<template>
  <bc-map
    :loadTilesWhileAnimating="true"
    :loadTilesWhileInteracting="true"
    style="height: 400px"
  >
    <bc-view ref="view" :center="center" :rotation="rotation" :zoom="zoom" />

    <bc-tile-layer>
      <bc-source-osm />
    </bc-tile-layer>

    <bc-tile-layer>
      <bc-source-wmts
        :attributions="attribution"
        :url="url"
        :matrixSet="matrixSet"
        :format="format"
        :layer="layerName"
        :styles="styleName"
      ></bc-source-wmts>
    </bc-tile-layer>
  </bc-map>
</template>

<script lang="ts" setup>
import { ref } from "vue";

const center = ref([-11158582, 4813697]);
const zoom = ref(4);
const rotation = ref(0);
const url = ref("https://mrdata.usgs.gov/mapcache/wmts");
const layerName = ref("sgmc2");
const matrixSet = ref("GoogleMapsCompatible");
const format = ref("image/png");
const styleName = ref("default");
const attribution = ref(
  'Tiles © <a href="https://services.arcgisonline.com/arcgis/rest/services/Demographics/USA_Population_Density/MapServer/">ArcGIS</a>'
);
</script>

# bc-map

> 核心组件

这是所有其他 bcgis-ol 组件的主容器，并且有一个`default`插槽来放置它们。
通常你会和`bc-view`一起使用组件来设置地图的`zoom`、`center`、`projection`和其他与视图相关的属性。

<script lang="ts" setup>
import MapDemo from "@demos/MapDemo.vue"
</script>

<ClientOnly>
<MapDemo />
</ClientOnly>

## 用法

简单地图的示例。 另请参阅`bc-view`组件的文档。

::: code-group

<<< ../../../src/demos/MapDemo.vue

:::

## 属性

### loadTilesWhileAnimating

- **类型**: `boolean`
- **默认值**: `false`

设置为`true`时，将在动画期间加载图块。

### loadTilesWhileInteracting

- **类型**: `boolean`
- **默认值**: `false`

当设置为`true`时，将在与地图交互时加载图块。

### mouseWheelZoom

- **类型**: `boolean`
- **默认值**: `true`

当设置为`false`时，将禁用使用鼠标滚轮进行缩放。

### moveTolerance

- **类型**: `number`
- **默认值**: `1`

光标必须移动才能被检测为地图移动的最小距离（以像素为单位）事件而不是点击。 增加此值可以更轻松地单击地图。

### pixelRatio

- **类型**: `number`
- **默认值**: `1`

设备上的物理像素和与设备无关的像素（下降）之间的比率。

## 事件

指针事件发送给 [`ol.MapBrowserEvent`](http://openlayers.org/en/latest/apidoc/module-ol_MapBrowserEvent-MapBrowserEvent.html)

- `click`
- `dblclick`
- `singleclick`
- `pointerdrag`
- `pointermove`

其他事件发送给 [`ol.MapEvent`](http://openlayers.org/en/latest/apidoc/module-ol_MapEvent-MapEvent.html)

- `movestart`
- `moveend`
- `postrender`
- `precompose`
- `postcompose`

## 方法

您可以从底层源访问所有方法。 查看[官方 OpenLayers 文档](https://openlayers.org/en/latest/apidoc/module-ol_Map.html)以查看可用的方法。

要访问源代码，您可以使用 ref() ，如下所示：

```vue
<template>
  <bc-map ref="mapRef">
    <!-- ... -->
  </bc-map>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import type Map from "ol/Map";

const mapRef = ref<{ map: Map }>(null);

onMounted(() => {
  const map: Map = mapRef.value?.map;
  // call your method on `map`
  const size = map.getSize();
});
</script>
```

此外，以下方法是直接公开的，并且可以如上所述使用`ref=""`来使用。

### focus()

在地图容器上触发集中。

### forEachFeatureAtPixel(pixel, callback, options = {})

- **参数**:
  - `pixel {number[]}`
  - `callback {function(ol.Feature, ?ol.layer.Layer): *}`
    功能回调。 将使用两个参数调用回调：OpenLayers `feature`
    在要素的像素和`layer`处（对于非托管图层将为空）。
    要停止检测，回调函数可以返回真值。
  - `[options] {Object | undefined}`
    - `layerFilter {function(ol.layer.Layer): boolean}` 图层过滤函数。
    - `hitTolerance {number | undefined}` 命中检测容差（以像素为单位）。默认值为`0`。
- **返回值**: `*` 回调返回的真实值。

检测与视口上的像素相交的特征，并执行回调
与每个相交的特征。 可以配置检测中包含的图层
通过`options`中的`layerFilter`选项。

### getCoordinateFromPixel(pixel)

- **参数**:
  - `pixel {number[]}`
- **返回值**: `number[]` 地图视图投影中像素的坐标。

获取给定像素的坐标。

### refresh()

- **返回值**: `{Promise<void>}`

更新地图大小并重新渲染地图。

### render()

- **返回值**: `{Promise<void>}`

请求地图渲染（在下一个动画帧）。

### updateSize()

更新地图大小。

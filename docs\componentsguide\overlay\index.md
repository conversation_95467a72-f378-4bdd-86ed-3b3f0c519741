<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 14:49:09
-->

# bc-overlay

> 附加到地理坐标的 HTML 元素

`bc-overlay`组件创建一个将显示在地图上的 HTML 元素。它具有**默认范围**的插槽来呈现您的自定义内容。

<script lang="ts" setup>
import OverlayDemo from "@demos/OverlayDemo.vue"
</script>

<ClientOnly>
<OverlayDemo />
</ClientOnly>

## 用法

下面的示例显示了如何将自定义内容添加到地图上。

::: code-group

<<< ../../../src/demos/OverlayDemo.vue

:::

## 属性

### position

- **类型**: `number[]`
- **Required**

视图中叠加位置的坐标

### offset

- **类型**: `number[]`
- **默认值**: `[0, 0]`

定位叠加层时使用的 XY 轴偏移（以像素为单位）。

### positioning

- **类型**: `string`
- **默认值**: `top-left`

相对于其可能值的叠加定位：`bottom-left`、`bottom-center`、`bottom-right`、`center-left`、`center-center`、`center-right`、`top-left`、`top-center`、 和`top-right`。

### stopEvent

- **类型**: `boolean`
- **默认值**: `true`

是否应停止从覆盖元素到地图视口的指针事件传播。设置为叠加层时，`true`叠加层将与地图控件放置在同一容器中。

### insertFirst

- **类型**: `boolean`
- **默认值**: `true`

确定覆盖层是前置还是附加到覆盖层容器中。当`stop-event`设置为时，`true`您可能会设置`insert-first`为`true`，以便覆盖层显示在控件下方。

### autoPan

- **类型**: `boolean`
- **默认值**: `false`

添加叠加层时启用地图平移，以便叠加层在当前视口中可见。

### autoPanMargin

- **类型**: `boolean`
- **默认值**: `20`

覆盖层和视口边框之间的边距（以像素为单位）。

### autoPanAnimation

- **类型**: `Object`
- **默认值**: `undefined`

用于将叠加层平移到视图中的动画选项。

## 事件

- `elementChanged`
- `offsetChanged`
- `positionChanged`
- `positioningChanged`

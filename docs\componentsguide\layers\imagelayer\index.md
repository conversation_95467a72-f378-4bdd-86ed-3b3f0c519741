<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: G<PERSON>er<PERSON>
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 15:08:18
-->

# bc-image-layer

`bc-image-layer` 组件可以渲染任何服务器渲染的图像，它是栅格源的容器，如 `bc-source-image-static`

<script lang="ts" setup>
import ImageLayerDemo from "@demos/ImageLayerDemo.vue"
</script>

<ClientOnly>
<ImageLayerDemo />
</ClientOnly>

## 用法

下面的示例展示了如何使用 `bc-image-layer` 组件和 `bc-source-image-static` 在地图上渲染自定义图像。地图视图配置有自定义投影，可将图像坐标直接转换为地图坐标。

::: code-group

<<< ../../../../src/demos/ImageLayerDemo.vue

:::

## 属性

### className

- **类型**: `string`
- **默认值**: `bc-layer`

要设置为图层元素的 CSS 类名称。

### opacity

- **类型**: `number`
- **默认值**: `1`

不透明度 (0, 1)。

### visible

- **类型**: `boolean`
- **默认值**: `true`

能见度。

### extent

- **类型**: `Array`

图层渲染的边界范围。该图层不会在此范围之外进行渲染。

### zIndex

- **类型**: `number`

图层渲染的 z-index。在渲染时，图层将首先按 Z 索引排序，然后按位置排序。

### minResolution

- **类型**: `number`

该图层可见的最小分辨率（含）。

### maxResolution

- **类型**: `number`

最大分辨率（独占），低于该分辨率该图层将可见。

### minZoom

- **类型**: `number`

最小视图缩放级别（不包括），高于该级别该图层将可见。

### maxZoom

- **类型**: `number`

该图层可见的最大视图缩放级别（含）。

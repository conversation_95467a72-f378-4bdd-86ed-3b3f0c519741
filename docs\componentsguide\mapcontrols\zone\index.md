<!--
 * @Description:
 * @Date: 2023-08-17 13:52:19
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-18 14:33:20
-->

# bc-zone-control

> 从一个区域跳转到另一个区域的控件。

<script lang="ts" setup>
import ZoneControlDemo from "@demos/ZoneControlDemo.vue"
</script>
<ClientOnly>
<ZoneControlDemo />
</ClientOnly>

## 用法

添加上下文菜单到地图

::: code-group

<<< ../../../../src/demos/ZoneControlDemo.vue

:::

## 属性

### className

- **类型**: `String`

### zones

区域数组: `{ name, extent (in EPSG:4326) }`

- **类型**: `Array.<any>`

### layer

要在控件中显示的图层或采用区域并返回要添加到控件的图层的函数

- **类型**: `ol.layer.Layer | function`

### projection

- **类型**: `String or Object`
- **默认值**: `EPSG:3857`

投影。默认是视图投影。投影代码必须包含以 : 分隔的数字结束部分，或者整个代码必须形成有效的 `ArcGIS SpatialReference` 定义。

### centerOnClick

单击区域时以单击为中心（或监听`select`事件以执行某些操作），默认 `true`

- **类型**: `Boolean`

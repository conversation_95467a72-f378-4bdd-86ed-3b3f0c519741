<template>
  <bc-map
    ref="map"
    :loadTilesWhileAnimating="true"
    :loadTilesWhileInteracting="true"
    style="height: 800px"
  >
    <bc-view
      ref="view"
      :center="center"
      :rotation="rotation"
      :zoom="zoom"
      :projection="projection"
    />

    <bc-swipe-control
      ref="swipeControl"
      v-if="layerList.length > 0"
      :layerList="layerList"
    />

    <bc-layerswitcherimage-control />

    <bc-zone-control
      :zones="zones"
      :projection="projection"
      :layer="jawgLayer.tileLayer"
      v-if="jawgLayer != null"
    >
    </bc-zone-control>

    <bc-tile-layer ref="osmLayer" title="OSM">
      <bc-source-osm />
    </bc-tile-layer>

    <bc-tile-layer ref="jawgLayer" title="JAWG">
      <bc-source-xyz
        crossOrigin="anonymous"
        url="https://c.tile.jawg.io/jawg-dark/{z}/{x}/{y}.png?access-token=87PWIbRaZAGNmYDjlYsLkeTVJpQeCfl2Y61mcHopxXqSdxXExoTLEv7dwqBwSWuJ"
      />
    </bc-tile-layer>

    <bc-control-bar>
      <bc-toggle-control
        html="<i class='fas fa-map-marker'></i>"
        className="edit"
        title="Point"
        :onToggle="(active) => changeDrawType(active, 'Point')"
      />
      <bc-toggle-control
        html="<i class='fas fa-draw-polygon'></i>"
        className="edit"
        title="Polygon"
        :onToggle="(active) => changeDrawType(active, 'Polygon')"
      />
      <bc-toggle-control
        html="<i class='fas fa-circle'></i>"
        className="edit"
        title="Circle"
        :onToggle="(active) => changeDrawType(active, 'Circle')"
      />
      <bc-toggle-control
        html="<i class='fas fa-grip-lines'></i>"
        className="edit"
        title="LineString"
        :onToggle="(active) => changeDrawType(active, 'LineString')"
      />
      <bc-videorecorder-control @stop="videoStopped">
      </bc-videorecorder-control>
      <bc-printdialog-control />
    </bc-control-bar>

    <bc-mouseposition-control />
    <bc-fullscreen-control />
    <bc-overviewmap-control>
      <bc-tile-layer>
        <bc-source-osm />
      </bc-tile-layer>
    </bc-overviewmap-control>

    <bc-scaleline-control />
    <bc-rotate-control />
    <bc-zoom-control />
    <bc-zoomslider-control />
    <bc-zoomtoextent-control
      :extent="[23.906, 42.812, 46.934, 34.597]"
      tipLabel="Fit to Turkey"
    />

    <bc-context-menu-control :items="contextMenuItems" />

    <bc-interaction-clusterselect @select="featureSelected" :pointRadius="20">
      <bc-style>
        <bc-style-stroke color="green" :width="5"></bc-style-stroke>
        <bc-style-fill color="rgba(255,255,255,0.5)"></bc-style-fill>
        <bc-style-icon :src="markerIcon" :scale="0.05"></bc-style-icon>
      </bc-style>
    </bc-interaction-clusterselect>

    <bc-interaction-select
      @select="featureSelected"
      :condition="selectCondition"
      :filter="selectInteactionFilter"
      v-if="!drawEnable"
    >
      <bc-style>
        <bc-style-stroke color="green" :width="10"></bc-style-stroke>
        <bc-style-fill color="rgba(255,255,255,0.5)"></bc-style-fill>
        <bc-style-icon :src="markerIcon" :scale="0.05"></bc-style-icon>
      </bc-style>
    </bc-interaction-select>

    <bc-vector-layer title="AIRPORTS" :preview="trIcon">
      <bc-source-vector
        ref="cities"
        url="https://raw.githubusercontent.com/alpers/Turkey-Maps-GeoJSON/master/tr-cities-airports.json"
        :format="geoJson"
        :projection="projection"
      >
        <bc-interaction-modify
          v-if="drawEnable"
          @modifyend="modifyend"
          @modifystart="modifystart"
        >
        </bc-interaction-modify>

        <bc-interaction-draw
          v-if="drawEnable"
          :type="drawType"
          @drawend="drawend"
          @drawstart="drawstart"
        >
        </bc-interaction-draw>

        <bc-interaction-snap v-if="drawEnable" />
      </bc-source-vector>

      <bc-style>
        <bc-style-stroke color="red" :width="2"></bc-style-stroke>
        <bc-style-fill color="rgba(255,255,255,0.1)"></bc-style-fill>
        <bc-style-circle :radius="7">
          <bc-style-fill color="blue"></bc-style-fill>
        </bc-style-circle>
      </bc-style>
    </bc-vector-layer>

    <bc-vector-layer
      :updateWhileAnimating="true"
      :updateWhileInteracting="true"
      title="STAR"
      :preview="starIcon"
    >
      <bc-source-vector ref="vectorsource">
        <bc-animation-shake :duration="2000" :repeat="5">
          <bc-feature v-for="index in 20" :key="index">
            <bc-geom-point
              :coordinates="[
                getRandomInRange(24, 45, 3),
                getRandomInRange(35, 41, 3),
              ]"
            ></bc-geom-point>

            <bc-style>
              <bc-style-icon :src="starIcon" :scale="0.1"></bc-style-icon>
            </bc-style>
          </bc-feature>
        </bc-animation-shake>
        <bc-feature>
          <bc-geom-circle :center="[38, 42]" :radius="2"></bc-geom-circle>
          <bc-style>
            <bc-style-stroke color="blue" :width="2"></bc-style-stroke>
            <bc-style-fill color="rgba(255,200,0,0.2)"></bc-style-fill>
          </bc-style>
        </bc-feature>
      </bc-source-vector>
    </bc-vector-layer>

    <bc-animated-clusterlayer
      :animationDuration="500"
      :distance="40"
      title="CLUSTER"
      :preview="clusterIcon"
    >
      <bc-source-vector ref="vectorsource">
        <bc-feature v-for="index in 500" :key="index">
          <bc-geom-point
            :coordinates="[
              getRandomInRange(24, 45, 3),
              getRandomInRange(35, 41, 3),
            ]"
          ></bc-geom-point>
        </bc-feature>
      </bc-source-vector>

      <bc-style :overrideStyleFunction="overrideStyleFunction">
        <bc-style-stroke color="red" :width="2"></bc-style-stroke>
        <bc-style-fill color="rgba(255,255,255,0.1)"></bc-style-fill>

        <bc-style-circle :radius="20">
          <bc-style-stroke
            color="black"
            :width="15"
            :lineDash="[]"
            lineCap="butt"
          ></bc-style-stroke>
          <bc-style-fill color="black"></bc-style-fill>
        </bc-style-circle>

        <bc-style-text>
          <bc-style-fill color="white"></bc-style-fill>
        </bc-style-text>
      </bc-style>
    </bc-animated-clusterlayer>

    <bc-overlay
      :position="selectedCityPosition"
      v-if="selectedCityName != '' && !drawEnable"
    >
      <template v-slot="slotProps">
        <div class="overlay-content">
          {{ selectedCityName }} {{ slotProps }}
        </div>
      </template>
    </bc-overlay>

    <bc-vector-layer>
      <bc-source-vector>
        <bc-feature ref="animationPath">
          <bc-geom-line-string :coordinates="path"></bc-geom-line-string>
          <bc-style-flowline
            color="red"
            color2="yellow"
            :width="10"
            :width2="10"
            :arrow="1"
          />
        </bc-feature>
        <bc-animation-path
          v-if="animationPath"
          :path="animationPath.feature"
          :duration="4000"
          :repeat="10"
        >
          <bc-feature>
            <bc-geom-point :coordinates="path[0]"></bc-geom-point>
            <bc-style>
              <bc-style-circle :radius="10">
                <bc-style-fill color="blue"></bc-style-fill>
                <bc-style-stroke color="blue" :width="2"></bc-style-stroke>
              </bc-style-circle>
            </bc-style>
          </bc-feature>
        </bc-animation-path>
      </bc-source-vector>
    </bc-vector-layer>

    <bc-webgl-points-layer :styles="webglPointsStyle">
      <bc-source-webglpoints
        :format="geoJson"
        url="https://openlayers.org/en/latest/examples/data/geojson/world-cities.geojson"
      />
    </bc-webgl-points-layer>
  </bc-map>
</template>

<script lang="ts" setup>
import { ref, inject, onMounted } from "vue";

import markerIcon from "@/assets/marker.png";
import starIcon from "@/assets/star.png";
import clusterIcon from "@/assets/cluster.png";
import trIcon from "@/assets/tr.png";

const center = ref([34, 39.13]);
const projection = ref("EPSG:4326");
const zoom = ref(6);
const rotation = ref(0);

const format = inject("ol-format");

const geoJson = new format.GeoJSON();

const selectConditions = inject("ol-selectconditions");

const selectCondition = selectConditions.pointerMove;

const selectedCityName = ref("");
const selectedCityPosition = ref([]);

const extent = inject("ol-extent");

const Feature = inject("bc-feature");
const Geom = inject("ol-geom");

const contextMenuItems = ref([]);
const vectorsource = ref(null);
const view = ref(null);

const drawEnable = ref(false);
const drawType = ref("Point");

const changeDrawType = (active, draw) => {
  drawEnable.value = active;
  drawType.value = draw;
};

contextMenuItems.value = [
  {
    text: "Center map here",
    classname: "some-style-class", // add some CSS rules
    callback: (val) => {
      view.value.setCenter(val.coordinate);
    }, // `center` is your callback function
  },
  {
    text: "Add a Marker",
    classname: "some-style-class", // you can add this icon with a CSS class
    // instead of `icon` property (see next line)
    icon: markerIcon, // this can be relative or absolute
    callback: (val) => {
      console.log(val);
      const feature = new Feature({
        geometry: new Geom.Point(val.coordinate),
      });
      vectorsource.value.source.addFeature(feature);
    },
  },
  "-", // this is a separator
];

const featureSelected = (event) => {
  if (event.selected.length == 1) {
    selectedCityPosition.value = extent.getCenter(
      event.selected[0].getGeometry().extent_
    );
    selectedCityName.value = event.selected[0].values_.name;
  } else {
    selectedCityName.value = "";
  }
};

const overrideStyleFunction = (feature, style) => {
  const clusteredFeatures = feature.get("features");
  const size = clusteredFeatures.length;

  const color = size > 20 ? "192,0,0" : size > 8 ? "255,128,0" : "0,128,0";
  const radius = Math.max(8, Math.min(size, 20));
  const dash = (2 * Math.PI * radius) / 6;
  const calculatedDash = [0, dash, dash, dash, dash, dash, dash];

  style.getImage().getStroke().setLineDash(dash);
  style
    .getImage()
    .getStroke()
    .setColor("rgba(" + color + ",0.5)");
  style.getImage().getStroke().setLineDash(calculatedDash);
  style
    .getImage()
    .getFill()
    .setColor("rgba(" + color + ",1)");

  style.getImage().setRadius(radius);

  style.getText().setText(size.toString());
};

const selectInteactionFilter = (feature) => {
  return feature.values_.name != undefined;
};

const getRandomInRange = (from, to, fixed) => {
  return (Math.random() * (to - from) + from).toFixed(fixed) * 1;
};

const drawstart = (event) => {
  console.log(event);
};

const drawend = (event) => {
  console.log(event);
};

const modifystart = (event) => {
  console.log(event);
};

const modifyend = (event) => {
  console.log(event);
};

const videoStopped = (event) => {
  console.log(event);
};

const swipeControl = ref(null);
const jawgLayer = ref(null);
const osmLayer = ref(null);
const layerList = ref([]);
onMounted(() => {
  layerList.value.push(jawgLayer.value.tileLayer);
  layerList.value.push(osmLayer.value.tileLayer);
  console.log(layerList.value);
});

const path = ref([
  [25.6064453125, 44.73302734375001],
  [27.759765625, 44.75500000000001],
  [28.287109375, 43.32677734375001],
  [30.55029296875, 46.40294921875001],
  [31.69287109375, 43.04113281250001],
]);
const animationPath = ref(null);

const zones = [
  {
    title: "France",
    extent: [
      -5.318421740712579, 41.16082274292913, 9.73284186155716,
      51.21957336557702,
    ],
  },
  {
    title: "Turkey",
    extent: [22.473435, 34.465842, 43.40239, 42.56525],
  },
  {
    title: "Germany",
    extent: [-0.101752, 47.49888, 20.827203, 54.043465],
  },
];

const webglPointsStyle = {
  symbol: {
    symbolType: "circle",
    size: [
      "interpolate",
      ["linear"],
      ["get", "population"],
      40000,
      8,
      2000000,
      28,
    ],
    color: "#ffed02",
    rotateWithView: false,
    offset: [0, 0],
    opacity: [
      "interpolate",
      ["linear"],
      ["get", "population"],
      40000,
      0.6,
      2000000,
      0.92,
    ],
  },
};
</script>

<style>
.overlay-content {
  background: red !important;
  color: white;
  box-shadow: 0 5px 10px rgb(2 2 2 / 20%);
  padding: 10px 20px;
  font-size: 16px;
}
</style>

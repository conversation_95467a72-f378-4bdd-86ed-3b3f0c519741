# bc-source-vector

`bc-source-vector` 可以与 `bc-vector-layer` 一起使用，在地图上绘制任何矢量数据。

<script lang="ts" setup>
import GeomPoint from "@demos/GeomPoint.vue"
import VectorSourceDemo1 from "@demos/VectorSourceDemo1.vue"
import VectorSourceDemo2 from "@demos/VectorSourceDemo2.vue"
import VectorSourceDemo3 from "@demos/VectorSourceDemo3.vue"
import AnimatedClusterDemo2 from "@demos/AnimatedClusterDemo2.vue"
</script>

## 用法

### `bc-feature` component (GeoJSON)

借助 的静态功能`bc-feature`应仅用于微小的静态层。

<ClientOnly>
<GeomPoint />
</ClientOnly>

::: code-group

<<< ../../../../src/demos/GeomPoint.vue

:::

### `url` property

只需提供 `url` 值和格式 `GeoJSON` 即可加载功能

<ClientOnly>
<VectorSourceDemo1 />
</ClientOnly>

::: code-group

<<< ../../../../src/demos/VectorSourceDemo1.vue

:::

### `features` property

<ClientOnly>
<AnimatedClusterDemo2 />
</ClientOnly>

::: code-group

<<< ../../../../src/demos/AnimatedClusterDemo2.vue

:::

### `urlFunction`

下一个示例通过视口 BBOX 从远程 WFS 服务加载功能。通过格式和策略，您可以定义自定义矢量源格式和加载策略。

<ClientOnly>
<VectorSourceDemo2/>
</ClientOnly>

::: code-group

<<< ../../../../src/demos/VectorSourceDemo2.vue

:::

### TopoJSON

您还可以使用其他矢量格式，例如 TopoJSON。

<ClientOnly>
<VectorSourceDemo3/>
</ClientOnly>

::: code-group

<<< ../../../../src/demos/VectorSourceDemo3.vue

:::

## 性能提示

使用 OpenLayers 在地图上渲染大量标记时，从 URL 请求 GeoJSON 数据与直接通过源向量传递要素之间可能存在性能差异。

**Data Transfer:**
当从 URL 请求数据时，GeoJSON 数据会根据需要通过网络传输，而不是预先加载整个数据集。这在处理大型数据集时非常有利，因为它可以最大限度地减少初始数据传输并减少将标记加载到地图上所需的时间。

**Data Streaming:**
从 URL 请求数据允许以较小的块流式传输 GeoJSON 数据，这可以在渲染大量标记时显着提高性能。可以逐步获取并增量呈现数据，从而提供更流畅的用户体验并减少系统资源的压力。

**Memory Management:**
通过从 URL 请求数据，GeoJSON 数据按需加载，并且可以由 OpenLayers 进行有效管理。当直接通过源向量传递特征时，所有数据都会立即加载到内存中，这可能会导致性能问题，并可能在处理极大的数据集时导致内存不足错误。

**Data Filtering and Caching:**
从 URL 请求数据允许服务器端数据过滤，这意味着您可以根据特定条件仅请求必要的标记。此外，服务器可以缓存 GeoJSON 数据，从而实现更快的后续请求并减少服务器和网络的负载。

**Dynamic Data Updates:**
如果您的数据是动态的且经常更新，则从 URL 请求数据提供了一种获取最新信息的便捷方法，而无需重新加载整个数据集。当处理实时数据或频繁更新时，这尤其有用。

## 属性

### Props 继承自 OpenLayers

属性直接从 OpenLayers 传递。它们的类型和默认值可以在[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_Vector-VectorSource.html)中查看。只有部分属性是由 Vue / HTML 的保留关键字引起的。下面的部分描述了这种偏离的 props。

### 偏离属性

无

## 事件

您可以从底层源访问所有事件。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_Vector-VectorSource.html)以查看将触发的可用事件。

```html
<bc-source-vector :url="url" @error="handleEvent" />
```

## 方法

您可以从底层源访问所有方法。查看[OpenLayers 官方文档](https://openlayers.org/en/latest/apidoc/module-ol_source_Vector-VectorSource.html)以查看可用的方法。

要访问源代码，您可以使用`ref()`如下所示：

```vue
<template>
  <!-- ... -->
  <bc-source-vector :url="url" ref="sourceRef" />
  <!-- ... -->
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import type VectorSource from "ol/source/vector";

const sourceRef = ref<{ source: VectorSource }>(null);

onMounted(() => {
  const source: VectorSource = sourceRef.value?.source;
  // call your method on `source`
});
</script>
```
